#!/bin/bash

# API 配置
BASE_URL="http://10.7.0.2:8080/api/location"
ENTERPRISE_ID="TEX948WTx7Tn9dF3"

# 设备列表
DEVICES=(
  "SN00001:IMEI00001"
  "SN00002:IMEI00002"
  "SN00003:IMEI00003"
)

# 小范围中心坐标（示例：北京附近）
CENTER_LON=116.404
CENTER_LAT=39.915
RADIUS=0.5  # 经纬度浮动半径（可根据需求调整）

# 生成小范围随机坐标
generate_nearby_location() {
  # 生成中心点附近的随机偏移（正负RADIUS范围内）
  lon_offset=$(awk -v r=$RADIUS 'BEGIN{srand(); printf "%.6f", (rand()*2-1)*r}')
  lat_offset=$(awk -v r=$RADIUS 'BEGIN{srand(); printf "%.6f", (rand()*2-1)*r}')
  
  lon=$(awk -v center=$CENTER_LON -v offset=$lon_offset 'BEGIN{printf "%.6f", center+offset}')
  lat=$(awk -v center=$CENTER_LAT -v offset=$lat_offset 'BEGIN{printf "%.6f", center+offset}')
  echo "$lon $lat"
}

# 模拟100次请求
for ((i=1; i<=100; i++)); do
  device=${DEVICES[$((RANDOM % 3))]}
  sn=$(echo "$device" | cut -d':' -f1)
  imei=$(echo "$device" | cut -d':' -f2)
  
  read -r longitude latitude <<< $(generate_nearby_location)
  
  curl -X POST \
    "$BASE_URL?sn=$sn&imei=$imei&longitude=$longitude&latitude=$latitude&enterprise_id=$ENTERPRISE_ID" \
    -H "Content-Type: application/json" \
    -s -o /dev/null -w "Device: $sn, Lon: $longitude, Lat: $latitude, Status: %{http_code}\n"
  
  sleep 0.1
done