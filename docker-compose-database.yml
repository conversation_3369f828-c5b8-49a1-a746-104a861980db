version: '3.8'

services:
  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: beacon
      MONGO_INITDB_ROOT_PASSWORD: beacON028
    volumes:
      - ./data/mongodb_data:/data/db
    restart: always

  tdengine:
    image: tdengine/tdengine
    container_name: tdengine
    ports:
      - "6030:6030" # taosd
      - "6041:6041" # taosAdapter
      # - "6043:6043" # taosKeeper
      # - "6060:6060" # taosExplorer
    volumes:
      - ./data/taos/dnode/data:/var/lib/taos
      - ./data/taos/dnode/log:/var/log/taos
    restart: unless-stopped
    
  redis:
    image: redis:latest
    container_name: redis
    volumes:
      - ./data/redis/data:/data
      - ./data/redis/logs:/logs
      - ./data/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped