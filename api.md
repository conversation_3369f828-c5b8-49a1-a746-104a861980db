# 物联网云平台 API 文档

## 目录
- [介绍](#介绍)
- [认证机制](#认证机制)
- [用户管理](#用户管理)
- [企业管理](#企业管理)
- [设备管理](#设备管理)
- [MQTT 服务](#mqtt-服务)
- [WireGuard 管理](#wireguard-管理)
- [SCADA 系统](#scada-系统)
- [系统信息](#系统信息)

## 介绍

本文档详细描述了物联网云平台的 REST API 接口。这些 API 允许您管理用户、企业、设备、MQTT、WireGuard VPN 以及 SCADA 系统等功能。

所有 API 请求都应使用 HTTPS，并且大多数请求需要 JWT 认证令牌。

### 基本 URL

```
http://10.0.0.43:8080/api
```

### 响应格式

所有接口均返回 JSON 格式数据。成功的请求通常会返回 HTTP 状态码 200、201 或 204，并包含相应的数据。错误响应包含错误消息和相应的 HTTP 状态码。

## 认证机制

大多数 API 请求需要认证。认证通过 JWT 令牌实现，该令牌可以在登录接口获取。

### 获取认证令牌

**接口**: `/api/login`

**方法**: POST

**请求体**:

```json
{
  "username": "user123",  // 也可以使用邮箱登录
  "password": "Password123@"
}
```

**响应**:

```json
{
  "username": "user123",
  "name": "张三",
  "email": "<EMAIL>",
  "role": "admin",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "enterprise_id": "abc123",
  "avatar": "data:image/webp;base64,..."
}
```

**状态码**:
- 200: 成功
- 401: 认证失败

### 使用认证令牌

在认证后的所有 API 请求中，需要在请求头中添加：

```
Authorization: Bearer {token}
```

或者在 URL 中添加查询参数：

```
?token={token}
```

## 用户管理

### 注册用户

**接口**: `/api/register`

**方法**: POST

**请求体**:

```json
{
  "username": "user123",
  "name": "张三",
  "password": "Password123@",
  "email": "<EMAIL>"
}
```

**URL 参数**:
- `code`: 必需 - 验证码
- `enterprise_code`: 可选 - 企业注册码

**响应**:

```json
{
  "message": "Registration successful",
  "username": "user123",
  "name": "张三",
  "email": "<EMAIL>",
  "role": "user"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 401: 验证码无效
- 409: 用户名或邮箱已存在

### 发送注册验证码

**接口**: `/api/registercode`

**方法**: POST

**请求体**:

```json
{
  "username": "user123",
  "email": "<EMAIL>"
}
```

**响应**:

```json
{
  "message": "Verification code sent successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 409: 邮箱已注册

### 忘记密码

**接口**: `/api/forgotcode`

**方法**: POST

**URL 参数**:
- `email`: 必需 - 用户邮箱

**响应**:

```json
{
  "message": "Verification code sent successfully"
}
```

**状态码**:
- 200: 成功
- 404: 邮箱未注册

### 重置密码

**接口**: `/api/resetpwd`

**方法**: POST

**URL 参数**:
- `email`: 必需 - 用户邮箱
- `code`: 必需 - 验证码

**响应**:

```json
{
  "message": "New password sent to email: <EMAIL>"
}
```

**状态码**:
- 200: 成功
- 401: 验证码无效

### 修改密码

**接口**: `/api/user/updatepwd`

**方法**: POST

**请求体**:

```json
{
  "old_password": "OldPassword123@",
  "new_password": "NewPassword123@"
}
```

**响应**:

```json
{
  "message": "Password updated successfully"
}
```

**状态码**:
- 200: 成功
- 400: 密码不符合要求
- 401: 原密码不匹配

### 更新个人资料

**接口**: `/api/user/updateprofile`

**方法**: POST

**请求体**:

```json
{
  "name": "张三",
  "email": "<EMAIL>"
}
```

**URL 参数** (邮箱更新时):
- `code`: 必需 - 验证码

**查询参数** (发送验证码时):
- `mode`: "verify" - 指定发送验证码
- `email`: 新邮箱

**响应**:

```json
{
  "message": "Profile updated successfully",
  "updated_fields": {
    "name": "张三",
    "email": "<EMAIL>"
  }
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 401: 验证码无效
- 409: 邮箱已被使用

### 更新头像

**接口**: `/api/user/updateavatar`

**方法**: POST

**请求体**:

```json
{
  "avatar": "data:image/jpeg;base64,..."
}
```

**响应**:

```json
{
  "message": "Avatar updated successfully",
  "avatar": "data:image/webp;base64,..."
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取用户列表

**接口**: `/api/user/getusers`

**方法**: GET

**查询参数**:
- `page`: 页码，默认 1
- `limit`: 每页数量，默认 20

**响应**:

```json
{
  "total": 50,
  "users": [
    {
      "id": "5f8d3b...",
      "username": "user1",
      "name": "张三",
      "email": "<EMAIL>",
      "role": "user",
      "created_at": "2023-01-01T12:00:00Z"
    },
    // 更多用户...
  ]
}
```

**状态码**:
- 200: 成功
- 403: 无权限

### 删除用户

**接口**: `/api/user/:id`

**方法**: DELETE

**URL 参数**:
- `id`: 用户ID

**响应**:

```json
{
  "message": "User deleted successfully"
}
```

**状态码**:
- 200: 成功
- 404: 用户不存在
- 403: 无权限

### 获取用户信息

**接口**: `/api/user/info/:id`

**方法**: GET

**URL 参数**:
- `id`: 用户ID

**响应**:

```json
{
  "username": "user123",
  "email": "<EMAIL>",
  "role": "user"
}
```

**状态码**:
- 200: 成功
- 404: 用户不存在

### 管理员更新用户

**接口**: `/api/user/updateuser/:id`

**方法**: POST

**URL 参数**:
- `id`: 用户ID

**请求体**:

```json
{
  "email": "<EMAIL>",
  "role": "operator",
  "username": "new_username"
}
```

**响应**:

```json
{
  "message": "User updated successfully"
}
```

**状态码**:
- 200: 成功
- 404: 用户不存在
- 403: 无权限

### 强制重置密码

**接口**: `/api/user/forceresetpwd/:email`

**方法**: POST

**URL 参数**:
- `email`: 用户邮箱

**响应**:

```json
{
  "message": "New password sent to email: <EMAIL>"
}
```

**状态码**:
- 200: 成功
- 404: 用户不存在
- 403: 无权限

## 企业管理

### 生成企业注册码

**接口**: `/api/enterprise/code`

**方法**: POST

**权限要求**: 需要 admin 或 superadmin 角色

**响应**:

```json
{
  "code": "abcdef1234567890",
  "message": "Enterprise registration code generated successfully"
}
```

**状态码**:
- 200: 成功
- 403: 无权限

### 获取企业注册码列表

**接口**: `/api/enterprise/codes`

**方法**: GET

**查询参数**:
- `page`: 页码，默认 1
- `limit`: 每页数量，默认 10

**响应**:

```json
{
  "codes": [
    {
      "code": "abcdef1234567890",
      "used": false,
      "createdAt": "2023-01-01T12:00:00Z"
    },
    {
      "code": "xyz789456123",
      "used": true,
      "createdAt": "2023-01-01T10:00:00Z",
      "usedAt": "2023-01-02T15:30:00Z"
    }
    // 更多注册码...
  ],
  "pagination": {
    "current": 1,
    "pageSize": 10,
    "total": 25
  }
}
```

**状态码**:
- 200: 成功
- 403: 无权限

### 删除企业注册码

**接口**: `/api/enterprise/code/:code`

**方法**: DELETE

**URL 参数**:
- `code`: 企业注册码

**响应**:

```json
{
  "message": "Enterprise code deleted successfully"
}
```

**状态码**:
- 200: 成功
- 404: 注册码不存在
- 403: 无权限

### 创建操作员

**接口**: `/api/enterprise/operator`

**方法**: POST

**请求体**:

```json
{
  "name": "操作员",
  "username": "operator123",
  "password": "Password123@",
  "email": "<EMAIL>"
}
```

**响应**:

```json
{
  "message": "Operator created successfully",
  "id": "5f8d3b..."
}
```

**状态码**:
- 201: 成功
- 400: 参数错误
- 409: 用户名或邮箱已存在

### 删除操作员

**接口**: `/api/enterprise/operator/:id/:enterprise_id`

**方法**: DELETE

**URL 参数**:
- `id`: 操作员ID
- `enterprise_id`: 企业ID

**响应**:

```json
{
  "message": "Operator deleted successfully"
}
```

**状态码**:
- 200: 成功
- 404: 操作员不存在
- 403: 无权限

### 获取操作员列表

**接口**: `/api/enterprise/operators`

**方法**: GET

**响应**:

```json
{
  "operators": [
    {
      "id": "5f8d3b...",
      "name": "操作员1",
      "username": "operator1",
      "email": "<EMAIL>",
      "enterprise_id": "abc123",
      "created_at": "2023-01-01T12:00:00Z"
    },
    // 更多操作员...
  ]
}
```

**状态码**:
- 200: 成功
- 403: 无权限

## 设备管理

### 获取设备二维码

**接口**: `/api/device/bind`

**方法**: GET

**查询参数**:
- `device_id`: 设备ID

**响应**:

```json
{
  "qrcode": "base64编码的二维码图片"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取设备列表

**接口**: `/api/device/list`

**方法**: GET

**查询参数**:
- `page`: 页码，默认 1
- `page_size`: 每页数量，默认 10
- `mode`: 可选值为 "moreinfos"（包含主题信息）或 "map"（地图视图）

**响应**:

```json
{
  "devices": [
    {
      "id": "5f8d3b...",
      "name": "设备1",
      "device_sn": "SN123456",
      "type": "BGTR",
      "imei_code": "IMEI123456",
      "longitude": 104.100959,
      "latitude": 30.542196,
      "registered_at": "2023-01-01T12:00:00Z",
      "registered_by": "admin",
      "operators": ["operator1", "operator2"]
    },
    // 更多设备...
  ],
  "total": 50,
  "page": 1
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 注册设备

**接口**: `/api/device/register`

**方法**: POST

**请求体**:

```json
{
  "device_sn": "SN123456",
  "name": "设备名称"
}
```

**响应**:

```json
{
  "message": "Device registered successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 404: 设备不存在

### 导入设备

**接口**: `/api/device/import`

**方法**: POST

**请求体**:

```json
{
  "device_sn": "SN123456",
  "type": "BGTR",
  "imei_code": "IMEI123456"
}
```

**响应**:

```json
{
  "message": "Device created successfully",
  "device": {
    "id": "5f8d3b...",
    "device_sn": "SN123456",
    "type": "BGTR",
    "imei_code": "IMEI123456",
    "name": "Unnamed",
    "enterprise_id": "",
    "longitude": 104.100959,
    "latitude": 30.542196,
    "created_at": "2023-01-01T12:00:00Z",
    "topics": []
  }
}
```

**状态码**:
- 201: 成功
- 400: 参数错误
- 409: 设备已存在

### 分配设备操作员

**接口**: `/api/device/assign`

**方法**: POST

**请求体**:

```json
{
  "device_sn": "SN123456",
  "operator_id": ["operator1", "operator2"]
}
```

**响应**:

```json
{
  "message": "Device assigned successfully"
}
```

**状态码**:
- 200: 成功
- 403: 无权限
- 404: 设备不存在

### 获取主题信息

**接口**: `/api/device/topicinfo`

**方法**: GET

**查询参数**:
- `device_sn`: 设备序列号
- `topic`: 主题名称

**响应**:

```json
{
  "topic": {
    "topic": "SN123456/temperature",
    "device_sn": "SN123456",
    "enterprise_id": "abc123",
    "data_type": "number",
    "created_by": "user123",
    "created_at": "2023-01-01T12:00:00Z"
  }
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 404: 主题不存在

### 删除设备主题

**接口**: `/api/device/topic`

**方法**: DELETE

**查询参数**:
- `device_sn`: 设备序列号
- `topic`: 主题名称

**响应**:

```json
{
  "message": "Topic deleted successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 404: 主题不存在

### 删除设备

**接口**: `/api/device/:device_sn`

**方法**: DELETE

**URL 参数**:
- `device_sn`: 设备序列号

**响应**:

```json
{
  "message": "Device deleted successfully"
}
```

**状态码**:
- 200: 成功
- 404: 设备不存在

### 取消操作员分配

**接口**: `/api/device/unassign`

**方法**: POST

**请求体**:

```json
{
  "device_sn": "SN123456",
  "operator_id": "operator1"
}
```

**响应**:

```json
{
  "message": "Operator unassigned successfully"
}
```

**状态码**:
- 200: 成功
- 404: 设备不存在

### 更新设备信息

**接口**: `/api/device/update`

**方法**: POST

**请求体**:

```json
{
  "device_sn": "SN123456",
  "enterprise_id": "abc123",
  "name": "新设备名称"
}
```

**响应**:

```json
{
  "message": "Device updated successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 404: 设备不存在

### 接收设备位置

**接口**: `/api/location`

**方法**: POST

**查询参数**:
- `sn`: 设备序列号
- `imei`: IMEI 码
- `longitude`: 经度
- `latitude`: 纬度

**响应**:

```json
{
  "message": "Successfully updated device location"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 404: 设备不存在

### 获取设备轨迹

**接口**: `/api/device/tracks`

**方法**: GET

**查询参数**:
- `device_sn`: 设备序列号

**响应**:

```json
{
  "SN123456": [
    {
      "ts": "2023-01-01 12:00:00",
      "longitude": 104.100959,
      "latitude": 30.542196
    },
    // 更多轨迹点...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取绑定状态 WebSocket

**接口**: `/api/device/ws/bind-status`

**方法**: GET (WebSocket)

**查询参数**:
- `device_id`: 设备ID

**WebSocket 消息**:

1. 连接成功:
```json
{
  "status": "connected",
  "device_id": "SN123456"
}
```

2. 心跳消息:
```json
{
  "status": "heartbeat"
}
```

3. 绑定成功:
```json
{
  "device_id": "SN123456",
  "openid": "wx_open_id",
  "nickname": "微信用户名",
  "status": "success",
  "timestamp": "1609459200"
}
```

4. 绑定超时:
```json
{
  "status": "timeout",
  "message": "Binding timeout after 5 minutes"
}
```

**状态码**:
- 101: WebSocket 协议切换成功
- 400: 参数错误 

### 设备类型管理

### 获取设备类型列表

**接口**: `/api/types`

**方法**: GET

**响应**:

```json
{
  "types": [
    {
      "name": "BGTR",
      "count": 10
    },
    {
      "name": "BC-4GM-C",
      "count": 5
    },
    // 更多类型...
  ]
}
```

**状态码**:
- 200: 成功

### 创建设备类型

**接口**: `/api/types`

**方法**: POST

**请求体**:

```json
{
  "name": "NEW-TYPE"
}
```

**响应**:

```json
{
  "message": "Device type created successfully"
}
```

**状态码**:
- 201: 成功
- 409: 类型已存在

### 删除设备类型

**接口**: `/api/types/:name`

**方法**: DELETE

**URL 参数**:
- `name`: 类型名称

**响应**:

```json
{
  "message": "Device type deleted successfully"
}
```

**状态码**:
- 200: 成功
- 404: 类型不存在
- 409: 类型正在使用中

## MQTT 服务

### 获取 MQTT 服务器信息

**接口**: `/api/mqttinfo`

**方法**: GET

**响应**:

```json
{
  "info": {
    "version": "1.3.0",
    "uptime": "5h15m30s",
    "clients_connected": 26,
    "clients_disconnected": 5
  },
  "topics": 150
}
```

**状态码**:
- 200: 成功

### 创建 MQTT 主题

**接口**: `/api/mqtt/topics`

**方法**: POST

**请求体**:

```json
{
  "topic": "temperature",
  "data_type": "number",
  "device_sn": "SN123456",
  "user_name": "user123",
  "client_id": "client123"
}
```

**响应**:

```json
{
  "message": "Topic created successfully",
  "topic": "SN123456/temperature"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限

### 获取主题消息

**接口**: `/api/mqtt/topics/messages`

**方法**: GET

**查询参数**:
- `topic`: 主题名称
- `page`: 页码，默认 1
- `pageSize`: 每页数量，默认 10

**响应**:

```json
{
  "total": 50,
  "page": 1,
  "pageSize": 10,
  "data": [
    {
      "ts": "2023-01-01T12:00:00Z",
      "client_id": "client123",
      "topic": "SN123456/temperature",
      "payload": "25.5",
      "qos": 1
    },
    // 更多消息...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限
- 404: 主题不存在

### 获取主题历史消息

**接口**: `/api/mqtt/topics/history`

**方法**: GET

**查询参数**:
- `topic`: 主题名称
- `startTime`: 开始时间
- `endTime`: 结束时间
- `format`: 格式，可选值为 "json"（默认）或 "csv"

**响应**:

对于 JSON 格式:
```json
{
  "messages": [
    {
      "ts": "2023-01-01T12:00:00Z",
      "client_id": "client123",
      "topic": "SN123456/temperature",
      "payload": "25.5",
      "qos": 1
    },
    // 更多消息...
  ]
}
```

对于 CSV 格式，直接下载包含以下字段的 CSV 文件：
- Time
- Client ID
- Topic
- Payload
- QoS

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限
- 404: 主题不存在

### 发布消息

**接口**: `/api/mqtt/publish`

**方法**: POST

**请求体**:

```json
{
  "topic": "SN123456/temperature",
  "payload": "25.5"
}
```

**响应**:

```json
{
  "message": "Message published successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限
- 404: 主题不存在

### 批量查询最新消息 (WebSocket)

**接口**: `/api/mqtt/topics/ws`

**方法**: GET (WebSocket)

**WebSocket 请求**:

1. 订阅主题:
```json
{
  "type": "subscribe",
  "topics": ["SN123456/temperature", "SN123456/humidity"],
  "mode": "stream",
  "interval": 1000
}
```

2. 取消订阅:
```json
{
  "type": "unsubscribe",
  "topics": ["SN123456/temperature"]
}
```

**WebSocket 响应**:

```json
[
  {
    "topic": "SN123456/temperature",
    "latestMessage": {
      "ts": "2023-01-01T12:00:00Z",
      "payload": "25.5",
      "client_id": "client123"
    }
  },
  {
    "topic": "SN123456/humidity",
    "latestMessage": {
      "ts": "2023-01-01T12:01:00Z",
      "payload": "60",
      "client_id": "client123"
    }
  }
]
```

**状态码**:
- 101: WebSocket 协议切换成功
- 400: 参数错误

### 创建阈值规则

**接口**: `/api/mqtt/threshold`

**方法**: POST

**请求体**:

```json
{
  "topic_id": "SN123456/temperature",
  "expression": "${value} > 30",
  "description": "温度过高警报",
  "is_enabled": true,
  "mode": "email",
  "contact": "<EMAIL>"
}
```

**响应**:

返回创建的阈值规则对象。

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限
- 404: 主题不存在

### 更新阈值规则

**接口**: `/api/mqtt/threshold/:id`

**方法**: PUT

**URL 参数**:
- `id`: 规则ID

**请求体**:

```json
{
  "expression": "${value} > 35",
  "description": "温度严重过高警报",
  "is_enabled": true,
  "mode": "email",
  "contact": "<EMAIL>"
}
```

**响应**:

返回更新后的阈值规则对象。

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限
- 404: 规则不存在

### 获取主题阈值规则

**接口**: `/api/mqtt/threshold`

**方法**: GET

**查询参数**:
- `topic_id`: 主题ID

**响应**:

返回主题的阈值规则对象。

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限
- 404: 规则不存在

### 删除阈值规则

**接口**: `/api/mqtt/threshold/:id`

**方法**: DELETE

**URL 参数**:
- `id`: 规则ID

**响应**:

```json
{
  "message": "Rule deleted successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限
- 404: 规则不存在

### 获取所有阈值规则

**接口**: `/api/mqtt/thresholds`

**方法**: GET

**响应**:

```json
{
  "rules": [
    {
      "id": "5f8d3b...",
      "topic": "SN123456/temperature",
      "expression": "${value} > 30",
      "description": "温度过高警报",
      "is_enabled": true,
      "created_by": "user123",
      "mode": "email",
      "contact": "<EMAIL>",
      "enterprise_id": "abc123"
    },
    // 更多规则...
  ]
}
```

**状态码**:
- 200: 成功

### 获取报警列表

**接口**: `/api/mqtt/alarms`

**方法**: GET

**查询参数**:
- `page`: 页码，默认 1
- `pageSize`: 每页数量，默认 10

**响应**:

```json
{
  "total": 25,
  "alarms": [
    {
      "id": "5f8d3b...",
      "ts": "2023-01-01T12:00:00Z",
      "topic": "SN123456/temperature",
      "value": "35.5",
      "desc": "温度过高警报",
      "alarm_type": "email",
      "alarm_contact": "<EMAIL>",
      "msg": "已发送"
    },
    // 更多报警...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取报警历史

**接口**: `/api/mqtt/alarms/history`

**方法**: GET

**查询参数**:
- `startTime`: 开始时间
- `endTime`: 结束时间
- `format`: 格式，可选值为 "json"（默认）或 "csv"

**响应**:

对于 JSON 格式:
```json
{
  "alarms": [
    {
      "ts": "2023-01-01T12:00:00Z",
      "topic": "SN123456/temperature",
      "value": "35.5",
      "desc": "温度过高警报",
      "alarm_type": "email",
      "alarm_contact": "<EMAIL>",
      "msg": "已发送"
    },
    // 更多报警...
  ]
}
```

对于 CSV 格式，直接下载包含以下字段的 CSV 文件：
- Time
- Topic
- Value
- Description
- Alarm Type
- Contact Info
- Send Status

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取可用主题

**接口**: `/api/mqtt/topics/available`

**方法**: GET

**响应**:

```json
{
  "topics": [
    {
      "topic": "SN123456/temperature",
      "device_sn": "SN123456",
      "enterprise_id": "abc123",
      "data_type": "number",
      "created_by": "user123",
      "created_at": "2023-01-01T12:00:00Z"
    },
    // 更多主题...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误 

## WireGuard 管理

### 创建 WireGuard 设备

**接口**: `/api/wg/newdevice`

**方法**: POST

**请求体**:

```json
{
  "name": "wg0",
  "enterprise_id": "abc123"
}
```

**响应**:

```json
{
  "message": "Wireguard interface created successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 创建 WireGuard 对等点

**接口**: `/api/wg/newpeer`

**方法**: POST

**请求体**:

```json
{
  "name": "客户端1",
  "enterprise_id": "abc123",
  "device_name": "wg0"
}
```

**响应**:

```json
{
  "message": "Peer created successfully",
  "peer": {
    "id": "5f8d3b...",
    "name": "客户端1",
    "public_key": "abcdef...",
    "private_key": "xyz123...",
    "allowed_ips": "********/32",
    "device_name": "wg0",
    "enterprise_id": "abc123",
    "created_at": "2023-01-01T12:00:00Z"
  },
  "config": "# WireGuard 配置\n[Interface]\nPrivateKey = xyz123...\n..."
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取 WireGuard 设备列表

**接口**: `/api/wg/listdevices`

**方法**: GET

**URL 参数**:
- `page`: 页码
- `len`: 每页数量

**响应**:

```json
{
  "data": [
    {
      "id": "5f8d3b...",
      "name": "wg0",
      "enterprise_id": "abc123",
      "listen_port": 50000,
      "public_key": "abcdef...",
      "private_key": "xyz123...",
      "address": "********/24",
      "created_at": "2023-01-01T12:00:00Z"
    },
    // 更多设备...
  ],
  "total": 5
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取 WireGuard 设备状态

**接口**: `/api/wg/device`

**方法**: GET

**查询参数**:
- `enterprise_id`: 企业ID

**响应**:

```json
{
  "data": {
    "id": "5f8d3b...",
    "name": "wg0",
    "enterprise_id": "abc123",
    "listen_port": 50000,
    "public_key": "abcdef...",
    "private_key": "xyz123...",
    "address": "********/24",
    "created_at": "2023-01-01T12:00:00Z"
  },
  "peers": [
    {
      "id": "5f8d3b...",
      "name": "客户端1",
      "public_key": "abcdef...",
      "private_key": "xyz123...",
      "allowed_ips": "********/32",
      "device_name": "wg0",
      "enterprise_id": "abc123",
      "created_at": "2023-01-01T12:00:00Z"
    },
    // 更多对等点...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取多个 WireGuard 设备状态

**接口**: `/api/wg/devices`

**方法**: POST

**请求体**:

```json
{
  "devices": [
    {
      "name": "wg0",
      "enterprise_id": "abc123"
    },
    {
      "name": "wg1",
      "enterprise_id": "xyz789"
    }
  ]
}
```

**响应**:

```json
{
  "data": [
    {
      "name": "wg0",
      "rx_bytes": 1024,
      "tx_bytes": 2048,
      "last_handshake": "2023-01-01T12:00:00Z",
      "peers": 2
    },
    // 更多设备状态...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 删除 WireGuard 设备

**接口**: `/api/wg/device`

**方法**: POST

**请求体**:

```json
{
  "name": "wg0",
  "enterprise_id": "abc123"
}
```

**响应**:

```json
{
  "message": "Wireguard interface deleted successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误
- 403: 无权限

### 删除 WireGuard 对等点

**接口**: `/api/wg/peer`

**方法**: POST

**请求体**:

```json
{
  "device_name": "wg0",
  "enterprise_id": "abc123",
  "peer_id": "5f8d3b..."
}
```

**响应**:

```json
{
  "message": "Peer deleted successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取对等点二维码

**接口**: `/api/wg/qrcode`

**方法**: GET

**查询参数**:
- `peer_id`: 对等点ID

**响应**:

```json
{
  "svg": "SVG 格式的 QR 码数据"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取接口状态

**接口**: `/api/wg/status`

**方法**: GET

**查询参数**:
- `device_name`: 设备名称

**响应**:

```json
{
  "data": [
    {
      "public_key": "abcdef...",
      "endpoint": "********:12345",
      "allowed_ips": "********/32",
      "latest_handshake": "2023-01-01T12:00:00Z",
      "transfer_rx": 1024,
      "transfer_tx": 2048,
      "persistent_keepalive": "25s"
    },
    // 更多对等点状态...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取流量统计

**接口**: `/api/wg/traffic`

**方法**: GET

**查询参数**:
- `device_name`: 设备名称
- `time_range`: 时间范围，如 "7d"（7天）

**响应**:

```json
{
  "data": [
    {
      "timestamp": "2023-01-01T12:00:00Z",
      "rx_bytes": 1024,
      "tx_bytes": 2048
    },
    // 更多统计数据...
  ]
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 设置带宽限制

**接口**: `/api/wg/bandwidth/limit`

**方法**: POST

**请求体**:

```json
{
  "device_name": "wg0",
  "limit": 1024
}
```

**响应**:

```json
{
  "message": "Bandwidth limit set successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 移除带宽限制

**接口**: `/api/wg/bandwidth/limit`

**方法**: DELETE

**查询参数**:
- `device_name`: 设备名称

**响应**:

```json
{
  "message": "Bandwidth limit removed successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取带宽限制

**接口**: `/api/wg/bandwidth/limit`

**方法**: GET

**查询参数**:
- `device_name`: 设备名称

**响应**:

```json
{
  "limit": 1024
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 列出所有带宽限制

**接口**: `/api/wg/bandwidth/limits`

**方法**: GET

**响应**:

```json
{
  "limits": [
    {
      "device_name": "wg0",
      "enterprise_id": "abc123",
      "limit": 1024
    },
    // 更多限制...
  ]
}
```

**状态码**:
- 200: 成功

### 恢复所有带宽限制

**接口**: `/api/wg/bandwidth/restore`

**方法**: POST

**响应**:

```json
{
  "message": "Bandwidth limits restored successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 启动 WireGuard 接口

**接口**: `/api/wg/start`

**方法**: POST

**查询参数**:
- `device_name`: 设备名称

**响应**:

```json
{
  "message": "Interface started successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 停止 WireGuard 接口

**接口**: `/api/wg/stop`

**方法**: POST

**查询参数**:
- `device_name`: 设备名称

**响应**:

```json
{
  "message": "Interface stopped successfully"
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

## SCADA 系统

### 保存 SCADA 项目

**接口**: `/api/scada/projects`

**方法**: POST

**请求体**:

```json
{
  "projectId": "scada-abc123",
  "name": "工厂监控",
  "description": "工厂温度和湿度监控",
  "widgets": {
    // 项目部件配置
  }
}
```

**响应**:

返回保存的项目对象，包括更新的时间戳。

**状态码**:
- 200: 成功
- 400: 参数错误

### 获取 SCADA 项目列表

**接口**: `/api/scada/projects`

**方法**: GET

**响应**:

```json
[
  {
    "projectId": "scada-abc123",
    "name": "工厂监控",
    "description": "工厂温度和湿度监控",
    "createdAt": "2023-01-01T12:00:00Z",
    "updatedAt": "2023-01-02T15:30:00Z",
    "createdBy": "abc123"
  },
  // 更多项目...
]
```

**状态码**:
- 200: 成功

### 获取 SCADA 项目详情

**接口**: `/api/scada/projects/:id`

**方法**: GET

**URL 参数**:
- `id`: 项目ID

**响应**:

返回完整的项目对象，包括部件配置。

**状态码**:
- 200: 成功
- 404: 项目不存在

### 删除 SCADA 项目

**接口**: `/api/scada/projects/:id`

**方法**: DELETE

**URL 参数**:
- `id`: 项目ID

**响应**:

```json
{
  "message": "Project deleted successfully"
}
```

**状态码**:
- 200: 成功
- 404: 项目不存在

### 导出 SCADA 项目

**接口**: `/api/scada/projects/:id/export`

**方法**: GET

**URL 参数**:
- `id`: 项目ID

**响应**:

作为文件下载返回项目的完整 JSON 配置。

**状态码**:
- 200: 成功
- 404: 项目不存在

### 导入 SCADA 项目

**接口**: `/api/scada/projects/import`

**方法**: POST

**请求体**:

完整的项目 JSON 配置。

**响应**:

```json
{
  "message": "Project imported successfully",
  "originalId": "scada-abc123",
  "newId": "scada-xyz789",
  "project": {
    // 导入的项目配置
  }
}
```

**状态码**:
- 200: 成功
- 400: 参数错误

### 克隆 SCADA 项目

**接口**: `/api/scada/projects/:id/clone`

**方法**: POST

**URL 参数**:
- `id`: 源项目ID

**响应**:

```json
{
  "message": "Project cloned successfully",
  "sourceId": "scada-abc123",
  "newId": "scada-xyz789",
  "project": {
    // 克隆的项目配置
  }
}
```

**状态码**:
- 200: 成功
- 404: 源项目不存在

## 系统信息

### 获取系统信息

**接口**: `/api/sys/info`

**方法**: GET

**查询参数**:
- `i`: 网络接口名称，可选
- `init`: 如果为 "1"，则返回接口列表

**响应**:

```json
{
  "hostname": "server-name",
  "kernel": "Linux 5.10.0",
  "uptime": "10 days, 5 hours, 30 minutes",
  "current_time": "2023-01-01 12:00:00",
  "cpu": "25.5%",
  "memory": "4096/8192",
  "disk": "102400/204800",
  "net": "1024/2048"
}
```

当 `init=1` 时，还会包含：

```json
{
  // ... 其他字段 ...
  "interfaceList": [
    "eth0",
    "wlan0",
    "lo"
  ]
}
```

**状态码**:
- 200: 成功
- 500: 服务器错误 