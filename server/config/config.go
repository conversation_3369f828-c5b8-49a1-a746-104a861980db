package config

import (
	"fmt"
	"os"

	"gopkg.in/yaml.v3"
)

type DBConfig struct {
	Username string `json:"username" yaml:"username"`
	Password string `json:"password" yaml:"password"`
	Host     string `json:"host" yaml:"host"`
	Port     string `json:"port" yaml:"port"`
	DBName   string `json:"dbname" yaml:"dbname"`
}

type MailConfig struct {
	Host     string `json:"host" yaml:"host"`
	Port     string `json:"port" yaml:"port"`
	Username string `json:"username" yaml:"username"`
	Password string `json:"password" yaml:"password"`
}

type RedisConfig struct {
	Host     string `json:"host" yaml:"host"`
	Port     string `json:"port" yaml:"port"`
	Password string `json:"password" yaml:"password"`
	DB       int    `json:"db" yaml:"db"`
}

type WechatConfig struct {
	AppID      string `json:"appid" yaml:"appid"`
	Secret     string `json:"secret" yaml:"secret"`
	TemplateID string `json:"template_id" yaml:"template_id"`
}

type SMSConfig struct {
	AccessKeyID     string `json:"access_key_id" yaml:"access_key_id"`
	AccessKeySecret string `json:"access_key_secret" yaml:"access_key_secret"`
	SignName        string `json:"sign_name" yaml:"sign_name"`
	SMSCode         string `json:"sms_code" yaml:"sms_code"`
}

type Config struct {
	MongoDB      DBConfig     `json:"mongodb" yaml:"MongoDB"`
	TaosDB       DBConfig     `json:"taosdb" yaml:"TaosDB"`
	Redis        RedisConfig  `json:"redis" yaml:"Redis"`
	JwtSecret    string       `json:"jwt_secret" yaml:"jwt_secret"`
	Mail         MailConfig   `json:"mail" yaml:"mail"`
	Host         string       `json:"host" yaml:"host"`
	NetInterface string       `json:"interface" yaml:"NetworkInterface"`
	Wechat       WechatConfig `json:"wechat" yaml:"wechat"`
	SMS          SMSConfig    `json:"sms" yaml:"sms"`
}

var AppConfig Config

func LoadConfig(filePath string) error {
	file, err := os.Open(filePath)
	if err != nil {
		return fmt.Errorf("could not open config file: %v", err)
	}
	defer file.Close()

	decoder := yaml.NewDecoder(file)
	err = decoder.Decode(&AppConfig)
	if err != nil {
		return fmt.Errorf("could not decode config file: %v", err)
	}

	return nil
}
