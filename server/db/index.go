package db

import (
	"context"
	"fmt"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// EnsureIndexes creates all required indexes for the application
func EnsureIndexes() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// MQTT Topics indexes
	if err := createTopicIndexes(ctx); err != nil {
		return fmt.Errorf("failed to create topic indexes: %w", err)
	}

	// Devices indexes
	if err := createDeviceIndexes(ctx); err != nil {
		return fmt.Errorf("failed to create device indexes: %w", err)
	}

	// Messages indexes
	if err := createMessageIndexes(ctx); err != nil {
		return fmt.Errorf("failed to create message indexes: %w", err)
	}

	return nil
}

func createTopicIndexes(ctx context.Context) error {
	topicCollection := MongoDB.Collection("mqtt_topics")

	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "topic", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "enterprise_id", Value: 1},
				{Key: "data_type", Value: 1},
			},
		},
		{
			Keys: bson.D{
				{Key: "device_sn", Value: 1},
			},
		},
	}

	_, err := topicCollection.Indexes().CreateMany(ctx, indexes)
	return err
}

func createDeviceIndexes(ctx context.Context) error {
	deviceCollection := MongoDB.Collection("devices")

	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "device_sn", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{
				{Key: "enterprise_id", Value: 1},
				{Key: "operators", Value: 1},
			},
		},
	}

	_, err := deviceCollection.Indexes().CreateMany(ctx, indexes)
	return err
}

func createMessageIndexes(ctx context.Context) error {
	messageCollection := MongoDB.Collection("mqtt_messages")

	indexes := []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "topic", Value: 1},
				{Key: "ts", Value: -1},
			},
		},
		{
			Keys: bson.D{
				{Key: "ts", Value: -1},
			},
		},
	}

	_, err := messageCollection.Indexes().CreateMany(ctx, indexes)
	return err
}
