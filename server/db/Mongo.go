package db

import (
	"beacon/cloud/config"
	"context"
	"fmt"
	"log"

	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func InitMongoDB() error {
	uri := fmt.Sprintf("mongodb://%s:%s@%s:%s", config.AppConfig.MongoDB.Username, config.AppConfig.MongoDB.Password, config.AppConfig.MongoDB.Host, config.AppConfig.MongoDB.Port)
	clientOptions := options.Client().ApplyURI(uri)
	client, err := mongo.Connect(context.TODO(), clientOptions)
	if err != nil {
		fmt.Println(uri)
		return err
	}

	err = client.Ping(context.TODO(), nil)
	if err != nil {
		return err
	}

	MongoDB = client.Database(config.AppConfig.MongoDB.DBName)
	log.Println("[MongoDB] Connected to MongoDB!")

	// 确保所有必要的索引都存在
	if err := EnsureIndexes(); err != nil {
		log.Fatalf("Failed to create indexes: %v", err)
	}

	return nil
}
