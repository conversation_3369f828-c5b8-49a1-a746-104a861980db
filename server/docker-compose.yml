version: '3.8'

services:
  app:
    image: ztbcs/cloud:server  # 使用你构建的镜像
    container_name: ztbcs-cloud-server
    restart: unless-stopped
    # 使用前台运行模式替代host网络模式，使其能被ui服务访问
    # network_mode: host
    networks:
      - cloud_network
    ports:
      - "8080:8080"  # 如果需要直接访问后端API
      - "1883:1883"  # MQTT端口
      - "50000-59999:50000-59999"  # 其他所需端口
    volumes:
      - ./config:/app/config  # 挂载本地 config 目录到容器内的 /app/config
      - /etc/wireguard:/etc/wireguard
      # - /var/run/docker.sock:/var/run/docker.sock  # 挂载 Docker API 的 Unix socket（如果需要）
    environment:
      - TZ=Asia/Shanghai  # 设置时区
    user: "root"  # 使用 root 用户运行
    cap_add:
      - NET_ADMIN  # 添加网络管理权限

  ui:
    image: ztbcs/cloud:ui
    container_name: ztbcs-cloud-ui
    restart: unless-stopped
    networks:
      - cloud_network
    environment:
      - API_HOST=app  # 这里指定后端服务名称，对应nginx中的代理目标
    ports:
      - "5000:5000"
    depends_on:
      - app

# 定义网络，使服务能够相互通信
networks:
  cloud_network:
    driver: bridge