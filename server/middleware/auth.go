package middleware

import (
	"beacon/cloud/db"
	"beacon/cloud/utils"
	"log"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

// AuthMiddleware 认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.<PERSON>("Authorization")
		var tokenString string
		if authHeader == "" {
			// 如果 header 中没有，则尝试从查询参数中获取 token
			tokenString = c.Query("token")
			if tokenString == "" {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Authorization header or token parameter is required"})
				c.Abort()
				return
			}
		} else {
			// 检查 Authorization 头格式：Bearer token
			parts := strings.Split(authHeader, " ")
			if len(parts) != 2 || parts[0] != "Bearer" {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "Invalid authorization header format"})
				c.Abort()
				return
			}
			tokenString = parts[1]
		}

		claims, err := utils.ParseToken(tokenString)
		if err != nil {
			log.Printf("Token validation failed: %v", err)
			switch err {
			case jwt.ErrTokenExpired:
				c.JSON(777, gin.H{"error": "Token expired"})
			case jwt.ErrSignatureInvalid:
				c.JSON(777, gin.H{"error": "Invalid token signature"})
			default:
				c.JSON(777, gin.H{"error": "Invalid token"})
			}
			c.Abort()
			return
		}

		// 从JWT中获取用户名和企业ID
		c.Set("username", claims.Username)
		c.Set("enterprise_id", claims.EnterpriseID)

		// 从Redis获取用户角色（现在支持回源到数据库）
		role, err := db.GetUserRole(claims.Username)
		if err != nil {
			log.Printf("Failed to get user role: %v", err)

			// 这里我们根据错误的具体情况来做处理
			if strings.Contains(err.Error(), "user not found") {
				// 用户在数据库中不存在
				c.JSON(http.StatusUnauthorized, gin.H{"error": "User not found"})
			} else if strings.Contains(err.Error(), "role is empty") {
				// 用户存在但没有角色
				c.JSON(http.StatusForbidden, gin.H{"error": "User has no assigned role"})
			} else {
				// 其他错误
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user role"})
			}

			c.Abort()
			return
		}

		// 设置角色到上下文
		c.Set("role", role)

		// 刷新Redis中角色的过期时间
		err = db.RefreshUserRole(claims.Username)
		if err != nil {
			log.Printf("Failed to refresh user role expiration time: %v", err)
			// 不中断请求，只记录日志
		}

		c.Next()
	}
}
