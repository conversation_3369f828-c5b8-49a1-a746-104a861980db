API路径
/api/wg/peer/allowed-ips/public
请求方法
POST
请求参数
| 参数名 | 类型 | 必填 | 说明 |
|-------|------|-----|------|
| peer_id | string | 是 | Peer的ID (MongoDB ObjectID格式) |
| allowed_ips | array of string | 是 | 需要添加到peer的IP地址或IP段列表 |
请求示例
Apply to wgController...
响应参数
| 参数名 | 类型 | 说明 |
|-------|------|------|
| message | string | 操作结果消息 |
响应示例
{
  "message": "Peer allowed IPs updated successfully"
}
错误响应示例
{
  "error": "Failed to find peer: mongo: no documents in result"
}