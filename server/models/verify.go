/*
 * @Author: ZXH
 * @Date: 2025-01-09 11:41:20
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-01-09 16:23:29
 * @Description:
 */
package models

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

type EmailData struct {
	Content string
}

type VerificationCode struct {
	ID        primitive.ObjectID `bson:"_id,omitempty"`
	Email     string             `bson:"email"`
	Code      string             `bson:"code"`
	CreatedAt time.Time          `bson:"createdAt"`
	ExpiresAt time.Time          `bson:"expiresAt"`
}
