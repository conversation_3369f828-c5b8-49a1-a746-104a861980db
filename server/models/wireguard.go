package models

type WireguardConfig struct {
	Username         string
	WG_HOST          string
	WGPort           int
	UIPort           int
	Password         string `json:"password"`
	Language         string `json:"wg_ui_language"`
	WGDefaultAddress string `json:"wg_default_address"`
	WGDefaultDNS     string `json:"wg_default_dns"`
	WGAllowedIPs     string `json:"wg_allowed_ips"`
	PasswordHash     string `json:"password_hash"`
}

type SSEvent struct {
	Event string `json:"event"`
	Data  string `json:"data"`
}
