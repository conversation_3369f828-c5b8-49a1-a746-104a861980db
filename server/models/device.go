package models

import (
	"context"
	"errors"
	"slices"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// models/device.go
type Device struct {
	ID                 primitive.ObjectID `json:"id" bson:"_id"`
	Name               string             `json:"name" bson:"name"`
	DeviceSN           string             `json:"device_sn" bson:"device_sn,omitempty"`
	Type               string             `json:"type" bson:"type,omitempty"`
	IMEI_Code          string             `json:"imei_code" bson:"imei_code,omitempty"`
	EnterpriseID       string             `json:"enterprise_id" bson:"enterprise_id"`
	RegisteredAt       time.Time          `json:"registered_at" bson:"registered_at"`
	RegisteredBy       string             `json:"registered_by" bson:"registered_by"`
	Operators          []string           `json:"operators" bson:"operators"` // 可访问的 Operator 列表
	Topics             []string           `json:"topics" bson:"topics"`       // 可访问的 Topic 列表
	Longitude          float64            `json:"longitude" bson:"longitude"`
	Latitude           float64            `json:"latitude" bson:"latitude"`
	CreatedAt          time.Time          `json:"created_at" bson:"created_at"`
	LastLocationUpdate time.Time          `json:"last_location_update" bson:"last_location_update"`
}

type DetailDevice struct {
	ID        primitive.ObjectID `json:"id"`
	Name      string             `json:"name"`
	DeviceSN  string             `json:"device_sn"`
	Topics    []Topic            `json:"topics"`
	CreatedAt time.Time          `json:"created_at"`
}

type DeviceQuery struct {
	DeviceSN     string
	EnterpriseID string
	Operator     string
	Role         string
	Page         int
	PageSize     int
}

var (
	ErrDeviceNotFound     = errors.New("device not found")
	ErrDuplicateDevice    = errors.New("duplicate device")
	ErrUnauthorizedAccess = errors.New("unauthorized access")
	ErrInvalidRequest     = errors.New("invalid request")
)

func buildDeviceFilter(query DeviceQuery) bson.M {
	filter := bson.M{}
	if query.DeviceSN != "" {
		filter["device_sn"] = query.DeviceSN
	}

	switch query.Role {
	case "enterprise":
		filter["enterprise_id"] = query.EnterpriseID
	case "operator":
		filter["enterprise_id"] = query.EnterpriseID
		filter["operators"] = query.Operator
	}
	return filter
}

func CreateDevice(device *Device, collection *mongo.Collection) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	device.CreatedAt = time.Now()
	_, err := collection.InsertOne(ctx, device)
	if mongo.IsDuplicateKeyError(err) {
		return ErrDuplicateDevice
	}
	return err
}

func UpdateDevice(deviceSN string, update bson.M, collection *mongo.Collection) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result, err := collection.UpdateOne(ctx,
		bson.M{"device_sn": deviceSN},
		update,
	)
	if result.MatchedCount == 0 {
		return ErrDeviceNotFound
	}
	return err
}

func DeleteDevice(deviceSN string, collection *mongo.Collection) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	result, err := collection.DeleteOne(ctx, bson.M{"device_sn": deviceSN})
	if result.DeletedCount == 0 {
		return ErrDeviceNotFound
	}
	return err
}

func FindDeviceBySN(deviceSN string, collection *mongo.Collection) (*Device, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	var device Device
	err := collection.FindOne(ctx, bson.M{"device_sn": deviceSN}).Decode(&device)
	if errors.Is(err, mongo.ErrNoDocuments) {
		return nil, ErrDeviceNotFound
	}
	return &device, err
}

// OperatorDevice represents the device information visible to operators
type OperatorDevice struct {
	ID       primitive.ObjectID `json:"id"`
	Name     string             `json:"name"`
	DeviceSN string             `json:"device_sn"`
	Topics   []Topic            `json:"topics"`
}

// GetDevices function update
func GetDevices(query DeviceQuery, collection *mongo.Collection) (any, int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	filter := buildDeviceFilter(query)

	// Get total count
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return nil, 0, err
	}

	// Configure pagination options
	skip := int64((query.Page - 1) * query.PageSize)
	opts := options.Find().
		SetSkip(skip).
		SetLimit(int64(query.PageSize))

	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var devices []Device
	if err := cursor.All(ctx, &devices); err != nil {
		return nil, 0, err
	}

	// If the role is operator, convert to OperatorDevice
	if query.Role == "operator" {
		operatorDevices := make([]OperatorDevice, len(devices))
		for i, device := range devices {
			operatorDevices[i] = OperatorDevice{
				ID:       device.ID,
				Name:     device.Name,
				DeviceSN: device.DeviceSN,
				// Topics will be populated later if needed
			}
		}
		return operatorDevices, total, nil
	}

	return devices, total, nil
}

func GetTopicsByDeviceSNs(deviceSNs []string, topicCol *mongo.Collection) (map[string][]Topic, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	cursor, err := topicCol.Find(ctx, bson.M{
		"device_sn": bson.M{"$in": deviceSNs},
	})
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	topicsMap := make(map[string][]Topic)
	for cursor.Next(ctx) {
		var topic Topic
		if err := cursor.Decode(&topic); err != nil {
			continue
		}
		topicsMap[topic.DeviceSN] = append(topicsMap[topic.DeviceSN], topic)
	}
	return topicsMap, nil
}

// operators
func AddOperators(deviceSN string, operators []string, collection *mongo.Collection) error {
	return UpdateDevice(deviceSN, bson.M{
		"$addToSet": bson.M{"operators": bson.M{"$each": operators}},
	}, collection)
}

func RemoveOperator(deviceSN string, operator string, collection *mongo.Collection) error {
	return UpdateDevice(deviceSN, bson.M{
		"$pull": bson.M{"operators": operator},
	}, collection)
}

// topics
func AddTopics(deviceSN string, topics []string, collection *mongo.Collection) error {
	return UpdateDevice(deviceSN, bson.M{
		"$addToSet": bson.M{"topics": bson.M{"$each": topics}},
	}, collection)
}

func RemoveTopic(deviceSN string, topic string, collection *mongo.Collection) error {
	return UpdateDevice(deviceSN, bson.M{
		"$pull": bson.M{"topics": topic},
	}, collection)
}

func CheckOperatorAccess(deviceSN string, operator string, collection *mongo.Collection) (bool, error) {
	device, err := FindDeviceBySN(deviceSN, collection)
	if err != nil {
		return false, err
	}
	return slices.Contains(device.Operators, operator), nil
}
