package controller

import (
	"beacon/cloud/db"
	"beacon/cloud/models"
	"beacon/cloud/service/logger"
	"beacon/cloud/service/mqttserver"
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
)

// ImportDevice handles the import of a single device, ensuring it's valid and unique.
func ImportDevice(c *gin.Context) {
	var deviceReq struct {
		DeviceSN  string `json:"device_sn" binding:"required"`
		Type      string `json:"type" binding:"required"`
		IMEI_Code string `json:"imei_code" binding:"required"`
	}

	if err := c.ShouldBindJSON(&deviceReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body", "details": err.Error()})
		return
	}

	// Validate DeviceSN format (alphanumeric, max 24 chars)
	if len(deviceReq.DeviceSN) > 24 || !isAlphaNumeric(deviceReq.DeviceSN) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid device serial number",
			"field":   "device_sn",
			"details": "Serial number must be alphanumeric and maximum 24 characters",
		})
		return
	}

	// Validate IMEI format (alphanumeric, max 24 chars)
	if len(deviceReq.IMEI_Code) > 24 || !isAlphaNumeric(deviceReq.IMEI_Code) {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid IMEI code",
			"field":   "imei_code",
			"details": "IMEI code must be alphanumeric and maximum 24 characters",
		})
		return
	}

	// Validate Type exists
	var typeCount int64
	typeCount, err := deviceTypeCol.CountDocuments(context.TODO(), bson.M{"name": deviceReq.Type})
	if err != nil || typeCount == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid device type",
			"field":   "type",
			"details": "Device type does not exist",
		})
		return
	}

	// Check if the DeviceSN is already used
	var snCount int64
	snCount, err = deviceCol.CountDocuments(context.TODO(), bson.M{"device_sn": deviceReq.DeviceSN})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check device SN"})
		return
	}
	if snCount > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error":   "Duplicate device serial number",
			"field":   "device_sn",
			"details": "A device with this serial number already exists",
		})
		return
	}

	// Check if the IMEI is already used
	var imeiCount int64
	imeiCount, err = deviceCol.CountDocuments(context.TODO(), bson.M{"imei_code": deviceReq.IMEI_Code})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check IMEI code"})
		return
	}
	if imeiCount > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error":   "Duplicate IMEI code",
			"field":   "imei_code",
			"details": "A device with this IMEI code already exists",
		})
		return
	}

	// Create the device
	device := models.Device{
		ID:           primitive.NewObjectID(),
		DeviceSN:     deviceReq.DeviceSN,
		Type:         deviceReq.Type,
		IMEI_Code:    deviceReq.IMEI_Code,
		Name:         "Unnamed",
		EnterpriseID: "",
		Longitude:    104.100959,
		Latitude:     30.542196,
		CreatedAt:    time.Now(),
		Topics:       []string{},
	}

	err = models.CreateDevice(&device, deviceCol)
	if err != nil {
		if err == models.ErrDuplicateDevice {
			c.JSON(http.StatusConflict, gin.H{"error": "Device already exists"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create device"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Device created successfully",
		"device":  device,
	})
}

// Helper function to validate alphanumeric strings
func isAlphaNumeric(s string) bool {
	for _, r := range s {
		if (r < '0' || r > '9') && (r < 'A' || r > 'Z') && (r < 'a' || r > 'z') {
			return false
		}
	}
	return true
}

// RegisterDevice updates an existing device's information and marks it as registered.
func RegisterDevice(c *gin.Context) {
	username, _ := c.Get("username")
	enterpriseID, _ := c.Get("enterprise_id")
	var deviceReq struct {
		DeviceSN string `json:"device_sn"`
		Name     string `json:"name"`
	}

	if err := c.ShouldBindJSON(&deviceReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Check if the device exists
	_, err := models.FindDeviceBySN(deviceReq.DeviceSN, deviceCol)
	if err != nil {
		if err == models.ErrDeviceNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find device"})
		return
	}

	// Update the device's information
	updateFields := bson.M{
		"$set": bson.M{
			"name":          deviceReq.Name,
			"enterprise_id": enterpriseID,
			"registered_at": time.Now(),
			"registered_by": username,
			"operators":     []string{username.(string)},
		},
	}

	if err := models.UpdateDevice(deviceReq.DeviceSN, updateFields, deviceCol); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to register device"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Device registered successfully"})
}

// GetDeviceList retrieves a list of devices based on the user's role and optional mode.
func GetDeviceList(c *gin.Context) {
	role := c.MustGet("role").(string)
	enterpriseID, _ := c.Get("enterprise_id")
	mode := c.Query("mode")
	if enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "enterprise_id is required"})
		return
	}

	// Add pagination parameters
	page := 1
	pageSize := 10
	if p := c.Query("page"); p != "" {
		if val, err := strconv.Atoi(p); err == nil && val > 0 {
			page = val
		}
	}
	if ps := c.Query("page_size"); ps != "" {
		if val, err := strconv.Atoi(ps); err == nil && val > 0 {
			pageSize = val
		}
	}

	username := ""
	if role == "operator" {
		username = c.MustGet("username").(string)
	}

	query := models.DeviceQuery{
		EnterpriseID: enterpriseID.(string),
		Role:         role,
		Operator:     username,
		Page:         page,
		PageSize:     pageSize,
	}

	devices, total, err := models.GetDevices(query, deviceCol)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch devices"})
		return
	}

	switch mode {
	case "moreinfos":
		// 延迟加载 Topics 数据，仅在需要时查询
		switch devs := devices.(type) {
		case []models.OperatorDevice:
			// 提取设备 SN 列表
			deviceSNs := make([]string, len(devs))
			for i, device := range devs {
				deviceSNs[i] = device.DeviceSN
			}
			topicsMap, err := models.GetTopicsByDeviceSNs(deviceSNs, topicCol)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch topics"})
				return
			}
			// 更新每个 operator 设备的 Topics
			for i := range devs {
				devs[i].Topics = topicsMap[devs[i].DeviceSN]
			}
			c.JSON(http.StatusOK, gin.H{
				"devices": devs,
				"total":   total,
				"page":    page,
			})
		case []models.Device:
			// 提取设备 SN 列表
			deviceSNs := make([]string, len(devs))
			for i, device := range devs {
				deviceSNs[i] = device.DeviceSN
			}
			topicsMap, err := models.GetTopicsByDeviceSNs(deviceSNs, topicCol)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch topics"})
				return
			}
			// 构建详细设备信息列表
			detailedDevices := make([]models.DetailDevice, 0, len(devs))
			for _, device := range devs {
				detailedDevices = append(detailedDevices, models.DetailDevice{
					ID:        device.ID,
					Name:      device.Name,
					DeviceSN:  device.DeviceSN,
					Topics:    topicsMap[device.DeviceSN],
					CreatedAt: device.CreatedAt,
				})
			}
			c.JSON(http.StatusOK, gin.H{
				"devices": detailedDevices,
				"total":   total,
				"page":    page,
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Unexpected device type"})
			return
		}
	case "map":
		// 地图模式，仅加载地图相关的设备数据
		type mapDevice struct {
			ID                 any       `json:"id"`
			Name               string    `json:"name"`
			DeviceSN           string    `json:"device_sn"`
			Longitude          float64   `json:"longitude"`
			Latitude           float64   `json:"latitude"`
			IMEI_Code          string    `json:"imei_code"`
			Type               string    `json:"type"`
			LastLocationUpdate time.Time `json:"last_location_update"`
		}
		switch devs := devices.(type) {
		case []models.Device:
			mapDevices := make([]mapDevice, 0, len(devs))
			for _, d := range devs {
				mapDevices = append(mapDevices, mapDevice{
					ID:                 d.ID,
					Name:               d.Name,
					DeviceSN:           d.DeviceSN,
					Longitude:          d.Longitude,
					Latitude:           d.Latitude,
					IMEI_Code:          d.IMEI_Code,
					Type:               d.Type,
					LastLocationUpdate: d.LastLocationUpdate,
				})
			}
			c.JSON(http.StatusOK, gin.H{
				"devices": mapDevices,
				"total":   total,
				"page":    page,
			})
		default:
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Unexpected device type for map mode"})
			return
		}
	default:
		c.JSON(http.StatusOK, gin.H{
			"devices": devices,
			"total":   total,
			"page":    page,
		})
	}
}

// GetTopicInfo retrieves information about a specific topic for a device.
func GetTopicInfo(c *gin.Context) {
	deviceSN := c.Query("device_sn")
	topicName := c.Query("topic")
	if deviceSN == "" || topicName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_sn and topic are required"})
		return
	}

	var topic models.Topic
	if err := topicCol.FindOne(context.TODO(), bson.M{"topic": topicName, "device_sn": deviceSN}).Decode(&topic); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"topic": topic})
}

// DeleteTopicFromDevice deletes a specific topic for a device.
func DeleteTopicFromDevice(c *gin.Context) {
	deviceSN := c.Query("device_sn")
	topicName := c.Query("topic")
	if deviceSN == "" || topicName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_sn and topic are required"})
		return
	}

	err := mqttserver.DeleteTopic(topicName, deviceSN)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete topic: " + err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Topic deleted successfully"})
}

// DeleteDevice deletes a device and all its associated topics.
func DeleteDevice(c *gin.Context) {
	deviceSN := c.Param("device_sn")
	if deviceSN == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_sn is required"})
		return
	}

	if err := models.DeleteDevice(deviceSN, deviceCol); err != nil {
		if err == models.ErrDeviceNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete device"})
		return
	}

	// Delete all topics associated with the device
	_, err := topicCol.DeleteMany(context.TODO(), bson.M{"device_sn": deviceSN})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete topics"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Device deleted successfully"})
}

// AssignDevice assigns operators to a device
func AssignDevice(c *gin.Context) {
	enterpriseID, _ := c.Get("enterprise_id")
	var assignmentRequest struct {
		DeviceSN   string   `json:"device_sn"`
		OperatorID []string `json:"operator_id"`
	}
	if err := c.ShouldBindJSON(&assignmentRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	role := c.MustGet("role").(string)

	// Verify operators exist and are authorized
	operatorFilter := bson.M{"username": bson.M{"$in": assignmentRequest.OperatorID}}
	if role != "superadmin" && role != "admin" {
		operatorFilter["enterprise_id"] = enterpriseID.(string)
	}

	count, err := userCol.CountDocuments(context.TODO(), operatorFilter)
	if err != nil || count != int64(len(assignmentRequest.OperatorID)) {
		c.JSON(http.StatusForbidden, gin.H{"error": "One or more operators not found or not authorized"})
		return
	}

	// Verify device exists and is accessible
	deviceFilter := bson.M{"device_sn": assignmentRequest.DeviceSN}
	if role != "superadmin" && role != "admin" {
		deviceFilter["enterprise_id"] = enterpriseID.(string)
	}

	var device models.Device
	err = deviceCol.FindOne(context.TODO(), deviceFilter).Decode(&device)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Device not found or not authorized"})
		return
	}

	if err := models.AddOperators(assignmentRequest.DeviceSN, assignmentRequest.OperatorID, deviceCol); err != nil {
		if err == models.ErrDeviceNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to assign operators"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Device assigned successfully"})
}

// UnassignDevice removes an operator from a device
func UnassignDevice(c *gin.Context) {
	var unassignRequest struct {
		DeviceSN   string `json:"device_sn"`
		OperatorID string `json:"operator_id"`
	}
	if err := c.ShouldBindJSON(&unassignRequest); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	if err := models.RemoveOperator(unassignRequest.DeviceSN, unassignRequest.OperatorID, deviceCol); err != nil {
		if err == models.ErrDeviceNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to unassign operator"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Operator unassigned successfully"})
}

// UpdateDevice handles updating device attributes including name
func UpdateDevice(c *gin.Context) {
	// Get user role and username for permission checking
	role := c.MustGet("role").(string)
	username, _ := c.Get("username")

	var updateReq struct {
		DeviceSN     string  `json:"device_sn" binding:"required"`
		EnterpriseID string  `json:"enterprise_id" binding:"required"`
		Name         *string `json:"name"`
		// Can add other optional fields here for future expansion
	}

	if err := c.ShouldBindJSON(&updateReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload", "details": err.Error()})
		return
	}

	// Build device filter based on user role
	deviceFilter := bson.M{"device_sn": updateReq.DeviceSN}
	if role != "superadmin" && role != "admin" {
		// For operators, check if they're authorized for this device
		deviceFilter["operators"] = username.(string)
	}

	// For non-superadmin roles, ensure enterprise ID matches
	if role != "superadmin" {
		deviceFilter["enterprise_id"] = updateReq.EnterpriseID
	}

	// Verify device exists and is accessible to this user
	var device models.Device
	err := deviceCol.FindOne(context.TODO(), deviceFilter).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "Device not found or you don't have permission to update it"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to find device"})
		return
	}

	// Build update document based on provided fields
	updateFields := bson.M{"$set": bson.M{}}
	updateSet := updateFields["$set"].(bson.M)

	// Add optional fields to update
	if updateReq.Name != nil {
		updateSet["name"] = *updateReq.Name
	}

	// If no fields to update, return early
	if len(updateSet) == 0 {
		c.JSON(http.StatusBadRequest, gin.H{"error": "No fields to update provided"})
		return
	}

	// Add last_updated field
	updateSet["last_updated"] = time.Now()
	updateSet["last_updated_by"] = username

	// Update the device
	if err := models.UpdateDevice(updateReq.DeviceSN, updateFields, deviceCol); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update device"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Device updated successfully"})
}

//location

// ReceiveDeviceLocation receives the device's location
func ReceiveDeviceLocation(c *gin.Context) {
	type req struct {
		DeviceSN  string  `form:"sn" binding:"required"`
		IMEICode  string  `form:"imei" binding:"required"`
		Longitude float64 `form:"longitude" binding:"required"`
		Latitude  float64 `form:"latitude" binding:"required"`
	}

	var LocationReq req
	if err := c.ShouldBindQuery(&LocationReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request payload"})
		return
	}

	var device models.Device
	err := deviceCol.FindOne(context.TODO(), bson.M{
		"device_sn": LocationReq.DeviceSN,
		"imei_code": LocationReq.IMEICode,
	}).Decode(&device)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			c.JSON(http.StatusNotFound, gin.H{"error": "device not found or imei code/enterprise_id not match"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to verify device info"})
		return
	}

	// 更新设备位置
	if err := models.UpdateDevice(LocationReq.DeviceSN, bson.M{
		"$set": bson.M{
			"longitude":            LocationReq.Longitude,
			"latitude":             LocationReq.Latitude,
			"last_location_update": time.Now(),
		},
	}, deviceCol); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update device location"})
		return
	}

	// store device location tracks
	query := fmt.Sprintf("INSERT INTO %s USING %s TAGS('%s') VALUES(NOW, %f, %f)", device.DeviceSN, db.STables["device_location"], device.EnterpriseID, LocationReq.Latitude, LocationReq.Longitude)
	_, err = db.Taos.Exec(query)
	if err != nil {
		logger.Logger(logger.LogLevelDebug, "failed to store device location,%v", err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to store device location"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Successfully updated device location"})
}

func GetDeviceTracks(c *gin.Context) {
	enterpriseID, _ := c.Get("enterprise_id")
	deviceSN := strings.ToLower(c.Query("device_sn"))

	if enterpriseID == "" || deviceSN == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "enterprise_id and device_sn are required"})
		return
	}

	role := c.MustGet("role").(string)
	// 查询指定企业ID和设备SN的位置数据
	whereClause := fmt.Sprintf("tbname = '%s'", deviceSN)
	if role != "superadmin" && role != "admin" {
		whereClause += fmt.Sprintf(" AND enterprise_id = '%s'", enterpriseID)
	}

	query := fmt.Sprintf("SELECT ts, longitude, latitude FROM %s WHERE %s ORDER BY ts ASC LIMIT 100",
		db.STables["device_location"],
		whereClause,
	)
	rows, err := db.Taos.Query(query)
	if err != nil {
		logger.Logger(logger.LogLevelDebug, "failed to get device tracks: %v", err.Error())
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to get device tracks"})
		return
	}
	defer rows.Close()

	// 存储设备轨迹点
	tracks := []map[string]interface{}{}

	for rows.Next() {
		var ts time.Time
		var longitude, latitude float64

		if err := rows.Scan(&ts, &longitude, &latitude); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to parse device tracks"})
			return
		}

		// 添加位置点
		trackPoint := map[string]interface{}{
			"ts":        ts.Format("2006-01-02 15:04:05"),
			"longitude": longitude,
			"latitude":  latitude,
		}

		tracks = append(tracks, trackPoint)
	}

	if err = rows.Err(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to process device tracks"})
		return
	}

	// 返回单个设备的轨迹数据
	response := map[string][]map[string]interface{}{
		deviceSN: tracks,
	}

	c.JSON(http.StatusOK, response)
}
