package controller

import (
	"beacon/cloud/db"
	"beacon/cloud/models"
	"beacon/cloud/service/notification"
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// ------------------------------ 验证码相关逻辑 ------------------------------

// 发送验证码
func sendVerificationCode(c *gin.Context, email string) {
	code := notification.GenerateVerificationCode()
	body, err := notification.RenderTemplate(notification.VerifyEmailTemplate, models.EmailData{Content: code})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to render email template"})
		return
	}
	to := []string{email}
	if err := notification.SendEmail(to, "Verification Code", body); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to send verification code"})
		return
	}
	if err := StoreVerificationCode(email, code); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store verification code"})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Verification code sent successfully"})
}

// 存储验证码到数据库
func StoreVerificationCode(email, code string) error {
	// 删除已存在的验证码
	filter := bson.M{"email": email}
	_, err := db.MongoDB.Collection("verification_codes").DeleteMany(context.TODO(), filter)
	if err != nil {
		return err
	}

	// 存储新的验证码
	now := time.Now()
	verificationCode := models.VerificationCode{
		Email:     email,
		Code:      code,
		CreatedAt: now,
		ExpiresAt: now.Add(10 * time.Minute),
	}

	_, err = db.MongoDB.Collection("verification_codes").InsertOne(context.TODO(), verificationCode)
	return err
}

// 验证验证码是否有效
func VerifyVerificationCode(email, code string) bool {
	codeInfo, err := GetVerificationCode(email)
	if err != nil {
		log.Println("Error getting verification code:", err)
		return false
	}
	if codeInfo == nil {
		log.Println("No verification code found for email:", email)
		return false
	}
	// 检查验证码是否匹配且未过期
	if codeInfo.Code != code || time.Now().After(codeInfo.ExpiresAt) {
		log.Println("Invalid verification code or expired for email:", email)
		DeleteVerificationCode(code)
		return false
	}
	return true
}

// 删除验证码
func DeleteVerificationCode(code string) error {
	filter := bson.M{"code": code}
	_, err := db.MongoDB.Collection("verification_codes").DeleteOne(context.TODO(), filter)
	return err
}

// 从数据库获取验证码信息
func GetVerificationCode(email string) (*models.VerificationCode, error) {
	var codeInfo models.VerificationCode

	// 定义查询条件
	filter := bson.M{"email": email}

	// 定义排序规则（按创建时间倒序）
	opts := options.FindOne().SetSort(bson.D{{Key: "created_at", Value: -1}})

	// 查询最新的一条数据
	err := db.MongoDB.Collection("verification_codes").FindOne(context.TODO(), filter, opts).Decode(&codeInfo)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, fmt.Errorf("no verification code found for email: %s", email)
		}
		return nil, err
	}
	return &codeInfo, nil
}

// ------------------------------ 用户注册验证码逻辑 ------------------------------

// 发送注册验证码
func SendRegisterCode(c *gin.Context) {
	var user models.User
	if err := c.ShouldBindJSON(&user); err != nil {
		log.Println(err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 检查邮箱是否已存在
	exists, err := emailExists(user.Email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Database error"})
		return
	}
	if exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Email already exists, please login"})
		return
	}

	// 检查用户名是否已存在
	exists, err = usernameExists(user.Username)
	if err != nil {
		log.Println(err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Unknown error, please try again later!"})
		return
	}
	if exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Username already exists, please try another one!"})
		return
	}

	// 发送验证码
	sendVerificationCode(c, user.Email)
}
