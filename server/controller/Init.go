package controller

import (
	"beacon/cloud/db"
	"beacon/cloud/models"
	"beacon/cloud/service/wireguardmgr"
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// EnterpriseController is a controller for enterprise operations
var (
	userCol       *mongo.Collection
	deviceCol     *mongo.Collection
	topicCol      *mongo.Collection
	enterpriseCol *mongo.Collection
	deviceTypeCol *mongo.Collection
	wgMgr         *wireguardmgr.Manager
)

// Init 初始化控制器包
func Init() {
	log.Println("Initializing controllers...")
	userCol = db.MongoDB.Collection("users")
	deviceCol = db.MongoDB.Collection("devices")
	topicCol = db.MongoDB.Collection("mqtt_topics")
	enterpriseCol = db.MongoDB.Collection("enterprise_codes")
	// Initialize device type collection
	deviceTypeCol = db.MongoDB.Collection("device_types")
	wgMgr = wireguardmgr.NewManager()

	// Initialize default device types
	InitializeDefaultDeviceTypes()

	// Initialize WireGuard indexes
	if err := wireguardmgr.InitIndexes(db.MongoDB); err != nil {
		fmt.Printf("Failed to init WireGuard indexes: %v\n", err)
	}

	// Initialize WireGuard manager and restore rate limits
	wireguardmgr.InitWireguardManager()
}

// Enterprise Codes helpers
// generateEnterpriseCode generates a new enterprise registration code
func generateEnterpriseCode() (string, error) {
	bytes := make([]byte, 8) // 16 character hex string
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// VerifyCode checks if an enterprise code is valid and unused
func VerifyCode(code string) bool {
	filter := bson.M{"code": code, "used": false}
	var enterpriseCode models.EnterpriseCode
	err := enterpriseCol.FindOne(context.TODO(), filter).Decode(&enterpriseCode)
	return err == nil
}

// MarkCodeAsUsed marks an enterprise code as used
func MarkCodeAsUsed(code string) error {
	now := time.Now()
	filter := bson.M{"code": code, "used": false}
	update := bson.M{"$set": bson.M{"used": true, "usedAt": now}}
	result, err := enterpriseCol.UpdateOne(context.TODO(), filter, update)
	if err != nil {
		return err
	}
	if result.ModifiedCount == 0 {
		return fmt.Errorf("enterprise code not found or already used")
	}
	return nil
}
