package controller

import (
	"beacon/cloud/models"
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

//--------------------------------Operator--------------------------------

func CreateOperator(c *gin.Context) {
	var operator struct {
		Name     string `json:"name"`
		Username string `json:"username"`
		Password string `json:"password"`
		Email    string `json:"email"`
	}
	enterpriseID, _ := c.Get("enterprise_id")
	// Parse request body
	if err := c.ShouldBindJSON(&operator); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	// Validate required fields
	if operator.Username == "" || operator.Password == "" || operator.Email == "" {
		c.J<PERSON><PERSON>(http.StatusBadRequest, gin.H{"error": "Username, password and email are required"})
		return
	}

	// 在验证必填字段后添加邮箱格式验证
	if !isValidEmail(operator.Email) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid email format"})
		return
	}

	// Create operator user
	operatorUser := models.User{
		Name:         operator.Name,
		Username:     operator.Username,
		Password:     operator.Password,
		EnterpriseID: enterpriseID.(string),
		Email:        strings.ToLower(operator.Email),
		Role:         "operator",
	}

	// Validate and prepare user
	if err := validateAndPrepareUser(&operatorUser); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Save to database
	result, err := userCol.InsertOne(context.TODO(), operatorUser)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create operator"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Operator created successfully",
		"id":      result.InsertedID,
	})
}

func DeleteOperator(c *gin.Context) {
	role := c.MustGet("role").(string)

	id, err := primitive.ObjectIDFromHex(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid operator ID"})
		return
	}

	enterpriseID, _ := c.Get("enterprise_id")
	if enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID is required"})
		return
	}

	// 查找用户
	filter := bson.M{"_id": id}
	user := models.User{}
	err = userCol.FindOne(context.TODO(), filter).Decode(&user)
	if err != nil {
		fmt.Printf("Error finding user: %v\n", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "operator not found"})
		return
	}

	// 权限检查
	if role != "superadmin" && role != "admin" {
		if user.EnterpriseID != enterpriseID {
			c.JSON(http.StatusForbidden, gin.H{"error": "you are not authorized to delete this operator"})
			return
		}
	}

	// 从设备中移除该操作员
	_, err = deviceCol.UpdateMany(
		context.TODO(),
		bson.M{"operators": user.Username},
		bson.M{
			"$pull": bson.M{
				"operators": user.Username,
			},
		},
	)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to update devices"})
		return
	}

	// 删除操作员
	result, err := userCol.DeleteOne(context.TODO(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "failed to delete operator"})
		return
	}
	if result.DeletedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "operator not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Operator deleted successfully"})
}

func GetOperators(c *gin.Context) {
	var filter bson.M
	role := c.MustGet("role").(string)
	if role == "superadmin" || role == "admin" {
		filter = bson.M{"role": "operator"}
	} else {
		enterpriseID, _ := c.Get("enterprise_id")
		if enterpriseID == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID is required"})
			return
		}
		filter = bson.M{"enterprise_id": enterpriseID, "role": "operator"}
	}
	cursor, err := userCol.Find(context.TODO(), filter)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch operators"})
		return
	}
	defer cursor.Close(context.TODO())

	var operators []struct {
		ID           string    `json:"id" bson:"_id"`
		Name         string    `json:"name" bson:"name"`
		Username     string    `json:"username"`
		Email        string    `json:"email"`
		EnterpriseID string    `json:"enterprise_id" bson:"enterprise_id"`
		CreatedAt    time.Time `json:"created_at" bson:"created_at"`
	}
	if err = cursor.All(context.TODO(), &operators); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode operators"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"operators": operators})
}
