package controller

import (
	"context"
	"encoding/csv"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"sync"
	"time"

	"beacon/cloud/db"
	"beacon/cloud/models"
	"beacon/cloud/service/mqttserver"
	"beacon/cloud/utils"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

const (
	errTopicRequired        = "topic parameter is required"
	errEnterpriseIDRequired = "enterprise_id is required"
	errPermissionDenied     = "you don't have permission to access this topic"
	errTopicNotFound        = "topic not found"
	defaultPageSize         = 10
	defaultPage             = 1
	roleAdmin               = "admin"
	roleSuperAdmin          = "superadmin"
	roleOperator            = "operator"
	roleEnterprise          = "enterprise"
	dataTypeNumber          = "number"
	modeEmail               = "email"
	modePhone               = "phone"
)

var (
	ErrInvalidMode   = errors.New("invalid mode: must be either 'email' or 'phone'")
	ErrEmailRequired = errors.New("email is required when mode is 'email'")
	ErrPhoneRequired = errors.New("phone is required when mode is 'phone'")
)

// MqttInfo retrieves MQTT server information and returns it along with the current topics count.
func MqttInfo(c *gin.Context) {
	info := mqttserver.MqttServer.Info
	topicsCount, err := mqttserver.GetTopicsCount()
	if err != nil {
		topicsCount = 0
	}

	response := gin.H{
		"info":   info,
		"topics": topicsCount,
	}

	c.JSON(http.StatusOK, response)
}

// CreateMqttTopic creates a new MQTT topic with a username prefix.
func CreateMqttTopic(c *gin.Context) {
	var request struct {
		Topic    string `json:"topic" binding:"required"`
		DataType string `json:"data_type" binding:"required"`
		DeviceSN string `json:"device_sn" binding:"required"`
		UserName string `json:"user_name" binding:"required"`
		ClientID string `json:"client_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get the current user's username.
	currentUser := c.GetString("username")
	role := c.MustGet("role")
	// Retrieve the user's clientId from the database.
	collection := db.MongoDB.Collection("devices")
	var device models.Device
	if err := collection.FindOne(c.Request.Context(), bson.M{"device_sn": request.DeviceSN}).Decode(&device); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user information"})
		return
	}

	if role != roleSuperAdmin && role != roleAdmin {
		if !utils.Contains(device.Operators, currentUser) {
			c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to create topic"})
			return
		}
	}
	// Prefix the topic with the username.
	prefixedTopic := request.DeviceSN + "/" + request.Topic

	// Create the topic via the mqttserver package.
	if err := mqttserver.CreateTopic(prefixedTopic, device.EnterpriseID, currentUser, request.DeviceSN, request.DataType); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Topic created successfully",
		"topic":   prefixedTopic,
	})
}

// 使用sync.Map缓存主题信息，避免重复查询
var topicCache sync.Map

// topicPermissionCheck checks if the user has permission to access the topic
func topicPermissionCheck(c *gin.Context, topic models.Topic, enterpriseID string) bool {
	userRole := c.GetString("role")
	if userRole == "superadmin" || userRole == "admin" {
		return true
	}
	return topic.EnterpriseID == enterpriseID
}

// getTopicDoc retrieves and validates a topic document
func getTopicDoc(c *gin.Context, topicName string) (*models.Topic, error) {
	if topicName == "" {
		return nil, errors.New(errTopicRequired)
	}

	// 检查缓存
	cacheKey := "topic:" + topicName
	if cachedTopic, found := topicCache.Load(cacheKey); found {
		topic := cachedTopic.(models.Topic)
		return &topic, nil
	}

	// 使用上下文控制超时
	ctx, cancel := context.WithTimeout(c.Request.Context(), 3*time.Second)
	defer cancel()

	var topicDoc models.Topic
	err := db.MongoDB.Collection("mqtt_topics").FindOne(
		ctx,
		bson.M{"topic": topicName},
	).Decode(&topicDoc)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return nil, errors.New(errTopicNotFound)
		}
		return nil, fmt.Errorf("数据库查询错误: %w", err)
	}

	// 存入缓存，30秒后过期
	topicCache.Store(cacheKey, topicDoc)
	go func() {
		time.Sleep(30 * time.Second)
		topicCache.Delete(cacheKey)
	}()

	return &topicDoc, nil
}

// handleError is a helper function to handle errors and return JSON responses
func handleError(c *gin.Context, statusCode int, err error) {
	c.JSON(statusCode, gin.H{"error": err.Error()})
}

// validateEnterpriseAccess checks enterprise ID and topic access permissions
func validateEnterpriseAccess(c *gin.Context, topic models.Topic) error {
	enterpriseID, _ := c.Get("enterprise_id")
	if enterpriseID == "" {
		return errors.New(errEnterpriseIDRequired)
	}

	if !topicPermissionCheck(c, topic, enterpriseID.(string)) {
		return errors.New(errPermissionDenied)
	}

	return nil
}

// GetMqttTopicMessages retrieves messages for a topic with pagination.
func GetMqttTopicMessages(c *gin.Context) {
	topic := c.Query("topic")
	topicDoc, err := getTopicDoc(c, topic)
	if err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	if err := validateEnterpriseAccess(c, *topicDoc); err != nil {
		handleError(c, http.StatusForbidden, err)
		return
	}

	// Parse pagination parameters
	pageNum, pageSizeNum := utils.GetPaginationParams(c)

	// Retrieve messages with pagination
	count, messages, err := mqttserver.GetTopicMessagesWithPagination(*topicDoc, pageNum, pageSizeNum)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"total":    count,
		"page":     pageNum,
		"pageSize": pageSizeNum,
		"data":     messages,
	})
}

// PublishMessage publishes a message to a specific topic after verifying permissions.
func PublishMessage(c *gin.Context) {
	var message struct {
		Topic   string `json:"topic" binding:"required"`
		Payload string `json:"payload" binding:"required"`
	}
	if err := c.ShouldBindJSON(&message); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	currentUser := c.GetString("username")
	userRole := c.GetString("role")

	// Retrieve topic details.
	collection := db.MongoDB.Collection("mqtt_topics")
	var topic models.Topic
	if err := collection.FindOne(c.Request.Context(), bson.M{"topic": message.Topic}).Decode(&topic); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
		return
	}

	// Verify publish permission.
	if userRole != "superadmin" && userRole != "admin" {
		//check if the topic is in the device's topics
		collection = db.MongoDB.Collection("devices")
		var device models.Device
		if err := collection.FindOne(c.Request.Context(), bson.M{"topics": message.Topic}).Decode(&device); err != nil {
			c.JSON(http.StatusNotFound, gin.H{"error": "Device not found"})
			return
		}

		if !utils.Contains(device.Operators, currentUser) {
			c.JSON(http.StatusForbidden, gin.H{"error": "You don't have permission to publish to this topic"})
			return
		}
	}

	if err := mqttserver.Publish(message.Topic, message.Payload); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Message published successfully"})
}

// BatchLatestQueryRequest defines the format for client batch query requests.
type BatchLatestQueryRequest struct {
	Type     string   `json:"type"`     // "subscribe" or "unsubscribe"
	Topics   []string `json:"topics"`   // List of topics
	Mode     string   `json:"mode"`     // "stream" for continuous updates
	Interval int      `json:"interval"` // Update interval in milliseconds
}

// TopicLatestResponse defines the result of each topic query.
type TopicLatestResponse struct {
	Topic         string      `json:"topic"`
	LatestMessage interface{} `json:"latestMessage,omitempty"`
	Error         string      `json:"error,omitempty"`
}

// upgrader is the global websocket upgrader.
// Note: In production, adjust CheckOrigin as needed for security.
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 在生产环境中应该根据实际需求设置更严格的检查
	},
	// 可选：配置 TLS 相关参数
	EnableCompression: true,
}

// BatchQueryLatestMessagesWS handles batch queries for the latest messages via a websocket connection.
func BatchQueryLatestMessagesWS(c *gin.Context) {
	wsConn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		return
	}
	defer wsConn.Close()
	// 使用更高效的map初始化
	subscribedTopics := make(map[string]bool, 10) // 预分配足够的空间

	// 使用带缓冲的通道减少阻塞
	stopChan := make(chan struct{})
	updateChan := make(chan []TopicLatestResponse, 100)
	updateIntervalChan := make(chan time.Duration, 5)

	// 默认更新间隔为1秒
	currentInterval := time.Second

	var wg sync.WaitGroup
	wg.Add(2)

	// 用于批量查询的上下文
	baseCtx, baseCancel := context.WithCancel(c.Request.Context())
	defer baseCancel()

	// 消息处理goroutine
	go func() {
		defer wg.Done()
		// 结束时发送停止信号
		defer close(stopChan)

		for {
			_, msg, err := wsConn.ReadMessage()
			if err != nil {
				return
			}

			var req BatchLatestQueryRequest
			if err := json.Unmarshal(msg, &req); err != nil {
				// 跳过无效消息
				continue
			}
			// fmt.Printf("Interval:%d\n", req.Interval)

			switch req.Type {
			case "subscribe":
				// 更新定时器间隔（如果提供了）
				if req.Interval > 0 {
					newInterval := time.Duration(req.Interval) * time.Millisecond
					if newInterval != currentInterval {
						currentInterval = newInterval
						select {
						case updateIntervalChan <- newInterval:
						case <-stopChan:
							return
						}
					}
				}

				// 批量验证主题权限
				topicsToVerify := make([]string, 0, len(req.Topics))
				for _, topic := range req.Topics {
					if !subscribedTopics[topic] {
						topicsToVerify = append(topicsToVerify, topic)
					}
				}

				if len(topicsToVerify) > 0 {
					// 创建查询上下文
					ctx, cancel := context.WithTimeout(baseCtx, 3*time.Second)

					// 批量查询主题，减少数据库查询次数
					filter := bson.M{"topic": bson.M{"$in": topicsToVerify}}
					cursor, err := db.MongoDB.Collection("mqtt_topics").Find(ctx, filter)

					if err == nil {
						var topics []models.Topic
						if err = cursor.All(ctx, &topics); err == nil {
							// 添加到已订阅主题
							for _, topic := range topics {
								subscribedTopics[topic.Topic] = true
							}
						}
						cursor.Close(ctx)
					}

					cancel()
				}
			case "unsubscribe":
				// 处理取消订阅请求
				for _, topic := range req.Topics {
					delete(subscribedTopics, topic)
				}
			}
		}
	}()

	// 定期查询并推送最新消息数据的goroutine
	go func() {
		defer wg.Done()

		ticker := time.NewTicker(currentInterval)
		defer ticker.Stop()

		// 缓存查询结果，减少重复查询
		topicCache := make(map[string]*time.Time)

		for {
			select {
			case <-stopChan:
				return
			case newInterval := <-updateIntervalChan:
				ticker.Stop()
				ticker = time.NewTicker(newInterval)
			case <-ticker.C:
				if len(subscribedTopics) == 0 {
					continue
				}

				// 复制当前订阅的主题，避免并发修改
				topics := make([]string, 0, len(subscribedTopics))
				for topic := range subscribedTopics {
					topics = append(topics, topic)
				}

				// 如果主题很多，使用并行查询
				var responses []TopicLatestResponse
				if len(topics) > 5 {
					responses = fetchMessagesParallel(baseCtx, topics, c, topicCache)
				} else {
					responses = fetchMessagesSequential(baseCtx, topics, c, topicCache)
				}

				if len(responses) > 0 {
					select {
					case updateChan <- responses:
					case <-stopChan:
						return
					default:
						// 如果通道已满，丢弃本次更新
					}
				}
			}
		}
	}()

	// 主循环：发送更新到客户端
	for {
		select {
		case <-stopChan:
			close(updateChan)
			close(updateIntervalChan)
			wg.Wait()
			return
		case updates, ok := <-updateChan:
			if !ok {
				return
			}

			// 设置写入超时
			wsConn.SetWriteDeadline(time.Now().Add(2 * time.Second))
			if err := wsConn.WriteJSON(updates); err != nil {
				return
			}
		}
	}
}

// fetchMessagesParallel 并行获取多个主题的最新消息
func fetchMessagesParallel(ctx context.Context, topics []string, c *gin.Context, topicCache map[string]*time.Time) []TopicLatestResponse {
	var (
		responses []TopicLatestResponse
		mutex     sync.Mutex
		wg        sync.WaitGroup
	)

	// 限制并发数
	semaphore := make(chan struct{}, 10)

	for _, topic := range topics {
		wg.Add(1)
		semaphore <- struct{}{}

		go func(topicName string) {
			defer wg.Done()
			defer func() { <-semaphore }()

			// 创建超时上下文
			fetchCtx, cancel := context.WithTimeout(ctx, 2*time.Second)
			defer cancel()

			var topicDoc models.Topic
			err := db.MongoDB.Collection("mqtt_topics").FindOne(
				fetchCtx,
				bson.M{"topic": topicName},
			).Decode(&topicDoc)

			if err == nil {
				// 检查缓存
				shouldFetch := true
				mutex.Lock()
				lastFetch, exists := topicCache[topicName]
				if exists && time.Since(*lastFetch) < 2*time.Second {
					shouldFetch = false
				}
				mutex.Unlock()

				if shouldFetch {
					latestMessage, err := mqttserver.GetTopicLatestMessage(topicDoc)
					if err == nil && latestMessage != nil {
						mutex.Lock()
						responses = append(responses, TopicLatestResponse{
							Topic:         topicName,
							LatestMessage: latestMessage,
						})
						now := time.Now()
						topicCache[topicName] = &now
						mutex.Unlock()
					}
				}
			}
		}(topic)
	}

	wg.Wait()
	return responses
}

// fetchMessagesSequential 顺序获取多个主题的最新消息
func fetchMessagesSequential(ctx context.Context, topics []string, c *gin.Context, topicCache map[string]*time.Time) []TopicLatestResponse {
	var responses []TopicLatestResponse

	for _, topic := range topics {
		// 检查缓存
		lastFetch, exists := topicCache[topic]
		if exists && time.Since(*lastFetch) < 5*time.Second {
			continue
		}

		var topicDoc models.Topic
		if err := db.MongoDB.Collection("mqtt_topics").FindOne(
			ctx,
			bson.M{"topic": topic},
		).Decode(&topicDoc); err != nil {
			continue
		}

		latestMessage, err := mqttserver.GetTopicLatestMessage(topicDoc)
		if err == nil && latestMessage != nil {
			responses = append(responses, TopicLatestResponse{
				Topic:         topic,
				LatestMessage: latestMessage,
			})
			now := time.Now()
			topicCache[topic] = &now
		}
	}

	return responses
}

// GetTopicHistoryMessages retrieves messages for a topic within a specified time range.
func GetTopicHistoryMessages(c *gin.Context) {
	topic := c.Query("topic")
	startTime := c.Query("startTime")
	endTime := c.Query("endTime")
	format := c.DefaultQuery("format", "json")

	if startTime == "" || endTime == "" {
		handleError(c, http.StatusBadRequest, errors.New("start time and end time are required"))
		return
	}

	topicDoc, err := getTopicDoc(c, topic)
	if err != nil {
		handleError(c, http.StatusBadRequest, err)
		return
	}

	if err := validateEnterpriseAccess(c, *topicDoc); err != nil {
		handleError(c, http.StatusForbidden, err)
		return
	}

	// 构建缓存键
	cacheKey := fmt.Sprintf("history:%s:%s:%s", topic, startTime, endTime)

	// 优化查询，使用并发控制和记忆化存储
	messages, err := mqttserver.GetTopicMessagesInTimeRangeWithCache(*topicDoc, startTime, endTime, cacheKey)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	if format == "csv" {
		exportMessagesToCSV(c, messages, topic, startTime, endTime)
		return
	}

	c.JSON(http.StatusOK, gin.H{"messages": messages})
}

// exportMessagesToCSV handles CSV export of messages
func exportMessagesToCSV(c *gin.Context, messages []models.Message, topic, startTime, endTime string) {
	filename := fmt.Sprintf("%s_%s_%s.csv", topic, startTime, endTime)
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Type", "text/csv")

	writer := csv.NewWriter(c.Writer)
	writer.Write([]string{"Time", "Client ID", "Topic", "Payload", "QoS"})

	for _, msg := range messages {
		writer.Write([]string{
			msg.Ts.String(),
			msg.ClientID,
			msg.Topic,
			msg.Payload,
			strconv.FormatInt(int64(msg.QoS), 10),
		})
	}

	writer.Flush()
}

// 设备权限缓存
var devicePermissionCache sync.Map

// verifyTopicAccess checks if the user has access to the given topic
func verifyTopicAccess(c *gin.Context, topicName string) error {
	currentUser := c.GetString("username")
	userRole := c.GetString("role")

	// 管理员和超级管理员拥有完全访问权限
	if userRole == roleSuperAdmin || userRole == roleAdmin {
		return nil
	}

	// 生成缓存键
	cacheKey := fmt.Sprintf("access:%s:%s", currentUser, topicName)

	// 检查权限缓存
	if _, found := devicePermissionCache.Load(cacheKey); found {
		return nil
	}

	// 使用上下文超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// 获取主题信息
	topicDoc, err := getTopicDoc(c, topicName)
	if err != nil {
		return err
	}

	// 使用索引优化的查询
	filter := bson.M{
		"device_sn":     topicDoc.DeviceSN,
		"enterprise_id": topicDoc.EnterpriseID,
		"operators":     currentUser,
	}

	// 使用Projection只获取必要字段
	opts := options.FindOne().SetProjection(bson.M{"_id": 1})

	var device struct {
		ID string `bson:"_id"`
	}

	err = db.MongoDB.Collection("devices").FindOne(ctx, filter, opts).Decode(&device)
	if err != nil {
		if err == mongo.ErrNoDocuments {
			return fmt.Errorf("未授权访问主题: %s", topicName)
		}
		return fmt.Errorf("验证设备访问权限失败: %w", err)
	}

	// 缓存权限结果（60秒）
	devicePermissionCache.Store(cacheKey, true)
	go func() {
		time.Sleep(60 * time.Second)
		devicePermissionCache.Delete(cacheKey)
	}()

	return nil
}

// CreateThresholdRule creates a threshold rule for a topic.
func CreateThresholdRule(c *gin.Context) {
	var request struct {
		TopicID     string `json:"topic_id" binding:"required"`
		Expression  string `json:"expression" binding:"required"`
		Description string `json:"description"`
		IsEnabled   bool   `json:"is_enabled"`
		Mode        string `json:"mode" binding:"required"`
		Contact     string `json:"contact" binding:"required"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Verify access to the topic
	if err := verifyTopicAccess(c, request.TopicID); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	// Check if the topic exists and is of number type.
	collection := db.MongoDB.Collection("mqtt_topics")
	var topic models.Topic
	if err := collection.FindOne(c.Request.Context(), bson.M{"_id": request.TopicID}).Decode(&topic); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Topic not found"})
		return
	}

	if topic.DataType != "number" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Threshold rules can only be set for number type topics"})
		return
	}

	// Create the threshold rule.
	rule := &models.ThresholdRule{
		Topic:        request.TopicID,
		Expression:   request.Expression,
		Description:  request.Description,
		IsEnabled:    request.IsEnabled,
		CreatedBy:    c.GetString("username"),
		Mode:         request.Mode,
		Contact:      request.Contact,
		EnterpriseID: topic.EnterpriseID,
	}
	if err := mqttserver.CreateThresholdRule(rule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, rule)
}

// UpdateThresholdRule updates an existing threshold rule.
func UpdateThresholdRule(c *gin.Context) {
	ruleID := c.Param("id")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Rule ID is required"})
		return
	}

	// Get existing rule to check topic access
	var existingRule models.ThresholdRule
	if err := db.MongoDB.Collection("mqtt_topic_thresholds").FindOne(
		c.Request.Context(),
		bson.M{"_id": ruleID},
	).Decode(&existingRule); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Rule not found"})
		return
	}

	// Verify access to the topic
	if err := verifyTopicAccess(c, existingRule.Topic); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	var request struct {
		Expression  string `json:"expression"`
		Description string `json:"description"`
		IsEnabled   bool   `json:"is_enabled"`
		Mode        string `json:"mode"`
		Contact     string `json:"contact"`
	}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Update the rule with new values.
	existingRule.Expression = request.Expression
	existingRule.Description = request.Description
	existingRule.IsEnabled = request.IsEnabled
	existingRule.Mode = request.Mode
	existingRule.Contact = request.Contact

	if err := mqttserver.UpdateThresholdRule(&existingRule); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, existingRule)
}

// GetThresholdRule retrieves the threshold rule for a specific topic.
func GetThresholdRule(c *gin.Context) {
	topicName := c.Query("topic_id")
	if topicName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Topic ID is required"})
		return
	}

	// Verify access to the topic
	if err := verifyTopicAccess(c, topicName); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	rule, err := mqttserver.GetThresholdRule(topicName)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	if rule == (models.ThresholdRule{}) {
		c.JSON(http.StatusNotFound, gin.H{"error": "No threshold rule found for this topic"})
		return
	}
	c.JSON(http.StatusOK, rule)
}

// GetThresholdRules retrieves all threshold rules for a specific enterprise.
func GetThresholdRules(c *gin.Context) {
	enterpriseID, _ := c.Get("enterprise_id")
	if enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID is required"})
		return
	}

	currentUser := c.GetString("username")
	userRole := c.GetString("role")

	// Build the base query
	filter := bson.M{}

	switch userRole {
	case "superadmin", "admin":
		filter = bson.M{}
	case "operator", "enterprise":
		// Get devices where the operator is assigned
		deviceCursor, err := db.MongoDB.Collection("devices").Find(
			context.TODO(),
			bson.M{
				"operators": bson.M{"$in": []string{currentUser}},
			},
		)
		if err != nil {
			// 如果查询出错，返回空数组
			c.JSON(http.StatusOK, gin.H{"rules": []models.ThresholdRule{}})
			return
		}

		var devices []models.Device
		if err = deviceCursor.All(context.TODO(), &devices); err != nil {
			// 如果解码出错，返回空数组
			c.JSON(http.StatusOK, gin.H{"rules": []models.ThresholdRule{}})
			return
		}

		// 如果没有设备，直接返回空数组
		if len(devices) == 0 {
			c.JSON(http.StatusOK, gin.H{"rules": []models.ThresholdRule{}})
			return
		}

		// Get all device topics
		var accessibleTopics []string
		for _, device := range devices {
			accessibleTopics = append(accessibleTopics, device.Topics...)
		}

		// 如果没有可访问的主题，直接返回空数组
		if len(accessibleTopics) == 0 {
			c.JSON(http.StatusOK, gin.H{"rules": []models.ThresholdRule{}})
			return
		}

		filter = bson.M{
			"topic": bson.M{"$in": accessibleTopics},
		}
	}

	cursor, err := db.MongoDB.Collection("mqtt_topic_thresholds").Find(context.TODO(), filter)
	if err != nil {
		// 如果查询出错，返回空数组
		c.JSON(http.StatusOK, gin.H{"rules": []models.ThresholdRule{}})
		return
	}

	var rules []models.ThresholdRule
	if err = cursor.All(context.TODO(), &rules); err != nil {
		// 如果解码出错，返回空数组
		c.JSON(http.StatusOK, gin.H{"rules": []models.ThresholdRule{}})
		return
	}

	// 直接返回结果，rules 已经是空切片或包含数据的切片
	c.JSON(http.StatusOK, gin.H{"rules": rules})
}

// DeleteThresholdRule deletes a threshold rule if the user has permission.
func DeleteThresholdRule(c *gin.Context) {
	ruleID := c.Param("id")
	if ruleID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Rule ID is required"})
		return
	}

	// Get existing rule to check topic access
	var existingRule models.ThresholdRule
	if err := db.MongoDB.Collection("mqtt_topic_thresholds").FindOne(
		c.Request.Context(),
		bson.M{"_id": ruleID},
	).Decode(&existingRule); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "Rule not found"})
		return
	}

	// Verify access to the topic
	if err := verifyTopicAccess(c, existingRule.Topic); err != nil {
		c.JSON(http.StatusForbidden, gin.H{"error": err.Error()})
		return
	}

	if err := mqttserver.DeleteThresholdRule(ruleID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, gin.H{"message": "Rule deleted successfully"})
}

// --------------------------------------------------alarm--------------------------------------------------
// GetTopicAlarms retrieves alarms for a specific topic.
func GetTopicAlarms(c *gin.Context) {
	enterpriseID, _ := c.Get("enterprise_id")
	if enterpriseID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID parameter is required"})
		return
	}

	pageStr := c.DefaultQuery("page", "1")
	pageSizeStr := c.DefaultQuery("pageSize", "10")
	pageNum, err := strconv.Atoi(pageStr)
	if err != nil || pageNum < 1 {
		pageNum = 1
	}
	pageSizeNum, err := strconv.Atoi(pageSizeStr)
	if err != nil || pageSizeNum < 1 {
		pageSizeNum = 10
	}

	total, alarms, err := mqttserver.GetAlarmListPagination(pageNum, pageSizeNum, enterpriseID.(string), c.GetString("role"))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"total":  total,
		"alarms": alarms,
	})
}

// GetAlarmHistory retrieves alarms within a specified time range and supports CSV export
func GetAlarmHistory(c *gin.Context) {
	enterpriseID, _ := c.Get("enterprise_id")
	startTime := c.Query("startTime")
	endTime := c.Query("endTime")
	format := c.DefaultQuery("format", "json")

	if enterpriseID == "" || startTime == "" || endTime == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Enterprise ID, start time and end time are required"})
		return
	}

	alarms, err := mqttserver.GetAlarmsInTimeRange(enterpriseID.(string), startTime, endTime)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	if format == "csv" {
		// Set headers for CSV download
		filename := fmt.Sprintf("alarms_%s_%s_%s.csv", enterpriseID, startTime, endTime)
		c.Header("Content-Description", "File Transfer")
		c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
		c.Header("Content-Type", "text/csv; charset=utf-8")

		// add UTF-8 BOM head, solve the problem of Chinese乱码 when Excel opens
		c.Writer.Write([]byte{0xEF, 0xBB, 0xBF})

		// Create a buffered writer for CSV
		writer := csv.NewWriter(c.Writer)

		// Write CSV header
		writer.Write([]string{"Time", "Topic", "Value", "Description", "Alarm Type", "Contact Info", "Send Status"})

		// Stream write each alarm
		for _, alarm := range alarms {
			writer.Write([]string{
				alarm.Ts.String(),
				alarm.Topic,
				alarm.Value,
				alarm.Desc,
				alarm.AlarmType,
				alarm.AlarmContact,
				alarm.Msg,
			})
		}

		writer.Flush()
		return
	}

	c.JSON(http.StatusOK, gin.H{"alarms": alarms})
}

// GetAvailableTopics retrieves all topics that can have threshold rules
func GetAvailableTopics(c *gin.Context) {
	enterpriseID, _ := c.Get("enterprise_id")
	if enterpriseID == "" {
		handleError(c, http.StatusBadRequest, errors.New(errEnterpriseIDRequired))
		return
	}

	currentUser := c.GetString("username")
	userRole := c.GetString("role")

	// 使用上下文超时控制
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	filter := buildTopicFilter(userRole, enterpriseID.(string), currentUser)

	// 执行查询
	topics, err := queryAvailableTopics(ctx, filter)
	if err != nil {
		handleError(c, http.StatusInternalServerError, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"topics": topics})
}

// 抽取查询逻辑到独立函数
func queryAvailableTopics(ctx context.Context, filter bson.M) ([]models.Topic, error) {
	cursor, err := db.MongoDB.Collection("mqtt_topics").Find(ctx, filter)
	if err != nil {
		return []models.Topic{}, fmt.Errorf("failed to query topics: %w", err)
	}
	defer cursor.Close(ctx)

	var topics []models.Topic
	if err = cursor.All(ctx, &topics); err != nil {
		return []models.Topic{}, fmt.Errorf("failed to decode topics: %w", err)
	}

	return topics, nil
}

// 构建查询过滤器
func buildTopicFilter(userRole, enterpriseID, currentUser string) bson.M {
	filter := bson.M{"data_type": dataTypeNumber}

	switch userRole {
	case roleOperator:
		// 优化操作员的查询逻辑
		deviceFilter := bson.M{
			"enterprise_id": enterpriseID,
			"operators":     currentUser,
		}
		devices, _ := getOperatorDevices(deviceFilter)
		if len(devices) > 0 {
			deviceSNs := make([]string, len(devices))
			for i, device := range devices {
				deviceSNs[i] = device.DeviceSN
			}
			filter["device_sn"] = bson.M{"$in": deviceSNs}
		}
	case roleEnterprise:
		filter["enterprise_id"] = enterpriseID
	}

	return filter
}

// Get Operators of Devices
func getOperatorDevices(filter bson.M) ([]models.Device, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	cursor, err := db.MongoDB.Collection("devices").Find(ctx, filter)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var devices []models.Device
	if err = cursor.All(ctx, &devices); err != nil {
		return nil, err
	}

	return devices, nil
}
