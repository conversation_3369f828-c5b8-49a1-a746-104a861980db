package controller

import (
	"beacon/cloud/db"
	"beacon/cloud/service/logger"
	"beacon/cloud/service/wechat"
	"context"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/gorilla/websocket"
)

func BindDeviceQRCode(c *gin.Context) {
	// 生成一个唯一的场景ID（这里简单示例，实际应该用更复杂的方法）
	sceneID := c.Query("device_id")

	if sceneID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "设备ID不能为空"})
		return
	}
	redirect_uri := GenerateCallback()
	png, err := wechat.GenerateQRCode(sceneID, redirect_uri)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	// 返回二维码图片
	base64Str := base64.StdEncoding.EncodeToString(png)
	c.<PERSON>(http.StatusOK, gin.H{
		"qrcode": base64Str,
	})
}

func generateUniqueToken() string {
	// 这里应该使用更安全的随机生成方法
	return "token_" + time.Now().Format("20060102150405") + "_" + fmt.Sprint(time.Now().Nanosecond())
}

// 使用一次性token避免callback暴露，改用 Redis 缓存 token
func GenerateCallback() string {
	// 生成唯一token
	token := generateUniqueToken()

	// 将 token 存入 redis，并设置5分钟过期时间
	key := "wechat_callback:" + token
	err := db.Redis.Set(db.Ctx, key, "active", time.Minute*5).Err()
	if err != nil {
		fmt.Println("Redis 设置 token 失败:", err)
	}
	// 返回动态回调地址
	return "https://wx.kvortex.cn/api/bind/" + token
}

// HandleBindStatusWS handles WebSocket connections for WeChat binding status
func HandleBindStatusWS(c *gin.Context) {
	// Get the device_id from query parameters
	deviceID := c.Query("device_id")
	if deviceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "device_id is required"})
		return
	}

	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	ws, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Failed to upgrade WebSocket connection: %v", err)
		return
	}
	defer ws.Close()

	// Create a device-specific channel name
	channelName := fmt.Sprintf("wechat_bind_status:%s", deviceID)

	// Subscribe to device-specific Redis channel for binding status updates
	pubsub := db.Redis.Subscribe(db.Ctx, channelName)
	defer pubsub.Close()

	// Send initial message to confirm connection
	err = ws.WriteMessage(websocket.TextMessage, []byte(`{"status":"connected","device_id":"`+deviceID+`"}`))
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error writing to WebSocket: %v", err)
		return
	}

	// Set up a timeout context - close the connection if binding doesn't happen within 5 minutes
	ctx, cancel := context.WithTimeout(db.Ctx, 5*time.Minute)
	defer cancel()

	// Set up a ticker for heartbeat
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	// Create channels for receiving messages and handling cleanup
	msgCh := make(chan *redis.Message)
	errCh := make(chan error)

	// Start goroutine to listen for Redis messages
	go func() {
		for {
			msg, err := pubsub.ReceiveMessage(ctx)
			if err != nil {
				errCh <- err
				return
			}
			msgCh <- msg
		}
	}()

	// Main event loop
	for {
		select {
		case <-ctx.Done():
			// Send timeout message
			ws.WriteMessage(websocket.TextMessage, []byte(`{"status":"timeout","message":"Binding timeout after 5 minutes"}`))
			logger.Logger(logger.LogLevelInfo, "WeChat", "Binding timeout for device_id: %s", deviceID)
			return

		case <-ticker.C:
			// Send heartbeat
			if err := ws.WriteMessage(websocket.TextMessage, []byte(`{"status":"heartbeat"}`)); err != nil {
				logger.Logger(logger.LogLevelError, "WeChat", "Error sending heartbeat: %v", err)
				return
			}

		case err := <-errCh:
			// Handle errors from Redis subscription
			logger.Logger(logger.LogLevelError, "WeChat", "Error from Redis subscription: %v", err)
			ws.WriteMessage(websocket.TextMessage, []byte(`{"status":"error","message":"Connection error"}`))
			return

		case msg := <-msgCh:
			// Process the message
			err = ws.WriteMessage(websocket.TextMessage, []byte(msg.Payload))
			if err != nil {
				logger.Logger(logger.LogLevelError, "WeChat", "Error writing to WebSocket: %v", err)
				return
			}

			// Check if this is a successful binding message
			var bindStatus map[string]string
			if err := json.Unmarshal([]byte(msg.Payload), &bindStatus); err == nil {
				if status, ok := bindStatus["status"]; ok && status == "success" {
					// Send a closing message
					ws.WriteMessage(websocket.TextMessage, []byte(`{"status":"closing","message":"Binding successful, closing connection"}`))
					logger.Logger(logger.LogLevelInfo, "WeChat", "Binding successful for device_id: %s, closing WebSocket", deviceID)
					return
				}
			}
		}
	}
}

// Update BindDeviceCallback to publish binding status
func BindDeviceCallback(c *gin.Context) {
	token := c.Param("token")
	key := "wechat_callback:" + token

	// Check if token exists in Redis
	exists, err := db.Redis.Exists(db.Ctx, key).Result()
	if err != nil || exists == 0 {
		htmlContent := `
		<html>
		<head>
			<meta charset="utf-8">
			<title>授权失败</title>
		</head>
		<body>
			<script>
				alert("无效的 token 或已过期！");
			</script>
			<h3>无效的 token 或已过期</h3>
		</body>
		</html>
		`
		c.Data(http.StatusNotFound, "text/html; charset=utf-8", []byte(htmlContent))
		return
	}

	// Get callback parameters
	code := c.Query("code")
	state := c.Query("state") // state contains device_id

	// Delete token from Redis
	db.Redis.Del(db.Ctx, key)

	userInfo, err := wechat.GetWxUserInfoFromOpenId(code)
	if err != nil {
		logger.Logger(logger.LogLevelError, "WeChat", "Error getting user info: %v", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get user info"})
		return
	}

	// Prepare binding status data
	bindingStatus := map[string]string{
		"device_id": state,
		"openid":    userInfo.OpenID,
		"nickname":  userInfo.NickName,
		"status":    "success",
		"timestamp": fmt.Sprintf("%d", time.Now().Unix()),
	}
	statusJSON, _ := json.Marshal(bindingStatus)

	// Create a device-specific channel name and publish to it
	channelName := fmt.Sprintf("wechat_bind_status:%s", state)
	db.Redis.Publish(db.Ctx, channelName, string(statusJSON))

	// Return success page
	htmlContent := fmt.Sprintf(`
	<html>
	<head>
		<meta charset="utf-8">
		<title>授权成功</title>
	</head>
	<body>
		<script>
			alert("授权成功！");
		</script>
		<h3>授权成功！</h3>
		<p>Device_id: %s</p>
		<p>用户昵称: %v</p>
	</body>
	</html>
	`, state, userInfo.NickName)

	c.Data(http.StatusOK, "text/html; charset=utf-8", []byte(htmlContent))
}

// CallbackHandler for WeChat OAuth callback
func WechatCallback(c *gin.Context) {
	wechatToken := "test"

	var param wechat.CheckSignatureRequest
	if err := c.ShouldBindQuery(&param); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	slice := sort.StringSlice{param.Timestamp, param.Nonce, wechatToken}

	sort.Strings(slice)
	//	将字符串数组拼接成字符串
	str := ""
	for _, value := range slice {
		str += value
	}

	h := sha1.New()
	h.Write([]byte(str))
	hashcode := fmt.Sprintf("%x", h.Sum(nil))
	if hashcode != param.Signature {
		c.JSON(http.StatusBadRequest, gin.H{"error": fmt.Sprintf("signature is not equal!Expected: %s, actually: %s\n", param.Signature, hashcode)})
		return
	}

	c.Writer.Write([]byte(param.Echostr))
}
