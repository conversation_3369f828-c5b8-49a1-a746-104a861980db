package controller

import (
	"beacon/cloud/db"
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// SCADAProject 表示SCADA项目结构
type SCADAProject struct {
	ProjectID   string    `json:"projectId" bson:"projectId"`
	Name        string    `json:"name" bson:"name"`
	Description string    `json:"description,omitempty" bson:"description,omitempty"`
	Widgets     any       `json:"widgets" bson:"widgets"`
	CreatedAt   time.Time `json:"createdAt,omitempty" bson:"createdAt,omitempty"`
	UpdatedAt   time.Time `json:"updatedAt,omitempty" bson:"updatedAt,omitempty"`
	CreatedBy   string    `json:"createdBy,omitempty" bson:"createdBy,omitempty"`
}

// 获取SCADA项目集合
func getScadaCollection() *mongo.Collection {
	return db.MongoDB.Collection("scada_projects")
}

// SaveSCADAProject 保存或更新SCADA项目
func SaveSCADAProject(c *gin.Context) {
	var project SCADAProject
	if err := c.ShouldBindJSON(&project); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid project data: " + err.Error(),
		})
		return
	}

	collection := getScadaCollection()
	ctx := context.Background()

	// 如果是新项目，生成唯一ID
	if project.ProjectID == "" {
		project.ProjectID = fmt.Sprintf("scada-%s", uuid.New().String())
	}

	// 更新时间戳
	now := time.Now()
	project.UpdatedAt = now

	// 从上下文中获取企业ID
	enterpriseID, exists := c.Get("enterprise_id")
	if exists {
		project.CreatedBy = enterpriseID.(string)
	}

	// 查询是否存在该项目
	filter := bson.M{"projectId": project.ProjectID}
	var existingProject SCADAProject
	err := collection.FindOne(ctx, filter).Decode(&existingProject)

	if err == mongo.ErrNoDocuments {
		// 新项目，设置创建时间
		project.CreatedAt = now

		// 插入新项目
		_, err = collection.InsertOne(ctx, project)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to save project: " + err.Error(),
			})
			return
		}
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to query project: " + err.Error(),
		})
		return
	} else {
		// 更新现有项目
		update := bson.M{
			"$set": bson.M{
				"name":        project.Name,
				"description": project.Description,
				"widgets":     project.Widgets,
				"updatedAt":   project.UpdatedAt,
			},
		}
		_, err = collection.UpdateOne(ctx, filter, update)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to update project: " + err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, project)
}

// GetSCADAProjects 获取所有SCADA项目列表
func GetSCADAProjects(c *gin.Context) {
	collection := getScadaCollection()
	ctx := context.Background()

	// 获取当前用户的企业ID
	enterpriseID, exists := c.Get("enterprise_id")
	var filter bson.M

	// 根据用户角色决定筛选条件
	role, roleExists := c.Get("role")
	if roleExists && (role == "superadmin") {
		// 超级管理员可以看到所有项目
		filter = bson.M{}
	} else if exists {
		// 其他用户只能看到自己企业的项目
		filter = bson.M{"createdBy": enterpriseID.(string)}
	} else {
		filter = bson.M{}
	}

	// 设置排序（按更新时间降序）和投影（排除widgets字段）
	findOptions := options.Find()
	findOptions.SetSort(bson.M{"updatedAt": -1})
	findOptions.SetProjection(bson.M{
		"widgets": 0,
	})

	cursor, err := collection.Find(ctx, filter, findOptions)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to query project list: " + err.Error(),
		})
		return
	}
	defer cursor.Close(ctx)

	// 解析结果
	var projects []SCADAProject
	if err = cursor.All(ctx, &projects); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to parse project list: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, projects)
}

// GetSCADAProject 获取特定SCADA项目
func GetSCADAProject(c *gin.Context) {
	projectID := c.Param("id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Project ID is required",
		})
		return
	}

	collection := getScadaCollection()
	ctx := context.Background()

	// 查询项目
	filter := bson.M{"projectId": projectID}
	var project SCADAProject
	err := collection.FindOne(ctx, filter).Decode(&project)

	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Project not found",
		})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to query project: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, project)
}

// DeleteSCADAProject 删除SCADA项目
func DeleteSCADAProject(c *gin.Context) {
	projectID := c.Param("id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Project ID is required",
		})
		return
	}

	collection := getScadaCollection()
	ctx := context.Background()

	// 删除项目
	filter := bson.M{"projectId": projectID}
	result, err := collection.DeleteOne(ctx, filter)

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to delete project: " + err.Error(),
		})
		return
	}

	if result.DeletedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Project not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Project deleted successfully",
	})
}

// ExportSCADAProject 导出项目配置为JSON
func ExportSCADAProject(c *gin.Context) {
	projectID := c.Param("id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Project ID is required",
		})
		return
	}

	collection := getScadaCollection()
	ctx := context.Background()

	// 查询项目
	filter := bson.M{"projectId": projectID}
	var project SCADAProject
	err := collection.FindOne(ctx, filter).Decode(&project)

	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Project not found",
		})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to query project: " + err.Error(),
		})
		return
	}

	// 设置响应头为下载文件
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=scada-%s.json", projectID))
	c.Header("Content-Type", "application/json")

	c.JSON(http.StatusOK, project)
}

// ImportSCADAProject 导入项目配置
func ImportSCADAProject(c *gin.Context) {
	var project SCADAProject
	if err := c.ShouldBindJSON(&project); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid project data: " + err.Error(),
		})
		return
	}

	collection := getScadaCollection()
	ctx := context.Background()

	// 生成新的项目ID避免冲突
	originalID := project.ProjectID
	project.ProjectID = fmt.Sprintf("scada-%s", uuid.New().String())

	// 更新时间戳
	now := time.Now()
	project.CreatedAt = now
	project.UpdatedAt = now

	// 从上下文中获取企业ID
	enterpriseID, exists := c.Get("enterprise_id")
	if exists {
		project.CreatedBy = enterpriseID.(string)
	}

	// 保存导入的项目
	_, err := collection.InsertOne(ctx, project)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to import project: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "Project imported successfully",
		"originalId": originalID,
		"newId":      project.ProjectID,
		"project":    project,
	})
}

// CloneSCADAProject 克隆现有项目
func CloneSCADAProject(c *gin.Context) {
	projectID := c.Param("id")
	if projectID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Source project ID is required",
		})
		return
	}

	collection := getScadaCollection()
	ctx := context.Background()

	// 查询源项目
	filter := bson.M{"projectId": projectID}
	var sourceProject SCADAProject
	err := collection.FindOne(ctx, filter).Decode(&sourceProject)

	if err == mongo.ErrNoDocuments {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Source project not found",
		})
		return
	} else if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to query source project: " + err.Error(),
		})
		return
	}

	// 创建克隆项目
	clonedProject := sourceProject
	clonedProject.ProjectID = fmt.Sprintf("scada-%s", uuid.New().String())
	clonedProject.Name = fmt.Sprintf("%s (副本)", sourceProject.Name)

	// 更新时间戳
	now := time.Now()
	clonedProject.CreatedAt = now
	clonedProject.UpdatedAt = now

	// 从上下文中获取企业ID
	enterpriseID, exists := c.Get("enterprise_id")
	if exists {
		clonedProject.CreatedBy = enterpriseID.(string)
	}

	// 保存克隆项目
	_, err = collection.InsertOne(ctx, clonedProject)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to clone project: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":  "Project cloned successfully",
		"sourceId": projectID,
		"newId":    clonedProject.ProjectID,
		"project":  clonedProject,
	})
}
