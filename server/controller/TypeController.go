package controller

import (
	"beacon/cloud/models"
	"log"
	"net/http"

	"github.com/gin-gonic/gin"
)

// GetDeviceTypes retrieves all device types
func GetDeviceTypes(c *gin.Context) {
	types, err := models.GetDeviceTypes(deviceTypeCol)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch device types"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"types": types,
	})
}

// CreateDeviceType creates a new device type
func CreateDeviceType(c *gin.Context) {
	var typeReq struct {
		Name string `json:"name" binding:"required"`
	}

	if err := c.ShouldBind<PERSON>(&typeReq); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request format"})
		return
	}

	if err := models.CreateDeviceType(typeReq.Name, deviceTypeCol); err != nil {
		if err == models.ErrDuplicateType {
			c.JSON(http.StatusConflict, gin.H{"error": "Device type already exists"})
			return
		}
		c.J<PERSON>(http.StatusInternalServerError, gin.H{"error": "Failed to create device type"})
		return
	}

	c.JSON(http.StatusCreated, gin.H{"message": "Device type created successfully"})
}

// DeleteDeviceType deletes a device type
func DeleteDeviceType(c *gin.Context) {
	typeName := c.Param("name")
	if typeName == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Type name is required"})
		return
	}

	// Get usage count before deleting
	count, err := models.GetTypeUsageCount(typeName, deviceCol)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to check type usage"})
		return
	}

	if count > 0 {
		c.JSON(http.StatusConflict, gin.H{
			"error":        "Device type is in use",
			"device_count": count,
		})
		return
	}

	if err := models.DeleteDeviceType(typeName, deviceTypeCol, deviceCol); err != nil {
		if err == models.ErrTypeNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Device type not found"})
			return
		}
		if err == models.ErrTypeInUse {
			c.JSON(http.StatusConflict, gin.H{
				"error":        "Device type is in use",
				"device_count": count,
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete device type"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Device type deleted successfully"})
}

// Initialize default device types if they don't exist
func InitializeDefaultDeviceTypes() {
	defaultTypes := []string{"BGTR", "BC-4GM-C", "BC-3GM-R", "BC-ECR-C"}

	for _, typeName := range defaultTypes {
		// 先检查类型是否存在
		exists, err := models.DeviceTypeExists(typeName, deviceTypeCol)
		if err != nil {
			// 记录错误但继续处理其他类型
			log.Printf("Error checking device type %s: %v", typeName, err)
			continue
		}

		// 只有当类型不存在时才创建
		if !exists {
			if err := models.CreateDeviceType(typeName, deviceTypeCol); err != nil {
				log.Printf("Error creating device type %s: %v", typeName, err)
			}
		}
	}
}
