package controller

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"beacon/cloud/models"
	"beacon/cloud/service/exporter"
	"beacon/cloud/utils"
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo/options"
)

/* ------------------------------- sysinfo Endpoints ------------------------------ */
func GetSystemInfo(c *gin.Context) {
	iface := c.Query("i")
	if iface == "" {
		iface = config.AppConfig.NetInterface
	}

	// Check for init parameter
	initParam := c.Query("init")

	// system basic info
	hostname, kernelVersion, uptime, err := exporter.GetBasicInfo()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get basic system info"})
		return
	}

	// cpu usage
	cpuUsage, err := exporter.GetCPUUsage()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get CPU usage"})
		return
	}

	// memory usage
	totalMemory, _, availableMemory, err := exporter.GetMemoryUsage()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get memory usage"})
		return
	}

	// disk usage
	totalDisk, usedDisk, err := exporter.GetDiskUsage()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get disk usage"})
		return
	}

	// network transmit
	bytesSent, bytesReceived, err := exporter.GetNetworkTransmit(iface)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get network IO"})
		return
	}

	// Prepare response
	response := gin.H{
		"hostname":     hostname,
		"kernel":       kernelVersion,
		"uptime":       uptime,
		"current_time": time.Now().Format("2006-01-02 15:04:05"),
		"cpu":          cpuUsage,
		"memory":       fmt.Sprintf("%d/%d", totalMemory-availableMemory, totalMemory),
		"disk":         fmt.Sprintf("%d/%d", usedDisk, totalDisk),
		"net":          fmt.Sprintf("%d/%d", bytesSent, bytesReceived),
	}

	// Add interface list if init=1
	if initParam == "1" {
		interfaces, err := exporter.GetNetworkInterfaces()
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get network interfaces"})
			return
		}
		response["interfaceList"] = interfaces
	}

	// return JSON
	c.JSON(http.StatusOK, response)
}

/* ------------------------------- Admin Endpoints ------------------------------ */
func GetUsers(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	skip := (page - 1) * limit

	filter := bson.M{"role": bson.M{"$nin": []string{"superadmin"}}}
	opts := options.Find().SetLimit(int64(limit)).SetSkip(int64(skip))

	total, _ := userCol.CountDocuments(c, filter)
	cursor, err := userCol.Find(c, filter, opts)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch users"})
		return
	}

	var users []models.User
	if err = cursor.All(c, &users); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Data processing error"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"total": total,
		"users": users,
	})
}

func GetUserProfile(c *gin.Context) {
	id := c.Param("id")
	objID, err := primitive.ObjectIDFromHex(id)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var user models.User
	if err := userCol.FindOne(c, bson.M{"_id": objID}).Decode(&user); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"username": user.Username,
		"email":    user.Email,
		"role":     user.Role,
	})
}

func AdminUpdateUser(c *gin.Context) {
	id, _ := primitive.ObjectIDFromHex(c.Param("id"))
	var update struct {
		Email    string `json:"email"`
		Role     string `json:"role"`
		Username string `json:"username"`
	}

	if err := c.ShouldBindJSON(&update); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid update data"})
		return
	}

	filter := bson.M{"_id": id}
	if c.GetString("role") == "admin" {
		filter["role"] = "user"
	}
	var enterprise_id string
	if update.Role == "enterprise" {
		enterprise_id, _ = utils.GenerateRandomString(16)
	}

	updateData := bson.M{"$set": bson.M{
		"email":         strings.ToLower(update.Email),
		"role":          update.Role,
		"username":      update.Username,
		"enterprise_id": enterprise_id,
	}}

	result, err := userCol.UpdateOne(c, filter, updateData)
	if err != nil || result.MatchedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found or unauthorized"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User updated successfully"})
}

func AdminDeleteUser(c *gin.Context) {
	id, _ := primitive.ObjectIDFromHex(c.Param("id"))
	filter := bson.M{"_id": id}

	if c.GetString("role") == "admin" {
		filter["role"] = "user"
	}

	result, err := userCol.DeleteOne(c, filter)
	if err != nil || result.DeletedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found or unauthorized"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "User deleted successfully"})
}

func ForceResetPassword(c *gin.Context) {
	email := c.Param("email")
	var user models.User
	if err := userCol.FindOne(c, bson.M{"email": email}).Decode(&user); err != nil {
		c.JSON(http.StatusNotFound, gin.H{"error": "User not found"})
		return
	}

	newPassword, err := resetPassword(user.Email)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Password reset failed"})
		return
	}

	RenderPasswordTemplateAndSendEmail(c, user.Email, newPassword)
}

// ---------------------------------- EnterpriseCode Endpoints ----------------------------------
// GenerateCode generates and stores a new enterprise registration code
func GenerateCode(c *gin.Context) {
	// Only superadmin and admin can generate enterprise codes
	code, err := generateEnterpriseCode()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to generate code"})
		return
	}

	enterpriseCode := models.EnterpriseCode{
		Code:      code,
		Used:      false,
		CreatedAt: time.Now(),
	}

	_, err = enterpriseCol.InsertOne(context.TODO(), enterpriseCode)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to store enterprise code"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    code,
		"message": "Enterprise registration code generated successfully",
	})
}

// ListCodes lists all enterprise codes with pagination
func ListCodes(c *gin.Context) {
	// Get pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	if page < 1 {
		page = 1
	}
	if limit < 1 {
		limit = 10
	}
	skip := (page - 1) * limit

	// Get total count
	total, err := enterpriseCol.CountDocuments(context.TODO(), bson.M{})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to count enterprise codes"})
		return
	}

	// Get codes with pagination
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit)).
		SetSort(bson.D{{Key: "createdAt", Value: -1}}) // Sort by creation date, newest first

	var codes []models.EnterpriseCode
	cursor, err := enterpriseCol.Find(context.TODO(), bson.M{}, opts)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to fetch enterprise codes"})
		return
	}
	defer cursor.Close(context.TODO())

	if err = cursor.All(context.TODO(), &codes); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to decode enterprise codes"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"codes": codes,
		"pagination": gin.H{
			"current":  page,
			"pageSize": limit,
			"total":    total,
		},
	})
}

// DeleteCode deletes an enterprise registration code
func DeleteCode(c *gin.Context) {
	// Only superadmin and admin can delete enterprise codes
	code := c.Param("code")
	if code == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Code is required"})
		return
	}

	result, err := db.MongoDB.Collection("enterprise_codes").DeleteOne(context.TODO(), bson.M{"code": code})
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete enterprise code"})
		return
	}

	if result.DeletedCount == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Enterprise code not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Enterprise code deleted successfully",
	})
}
