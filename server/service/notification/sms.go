package notification

import (
	"beacon/cloud/config"
	"beacon/cloud/models"
	"encoding/json"
	"fmt"
	"strings"

	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v4/client"
	"github.com/alibabacloud-go/tea/tea"
)

// SMSNotificationService 实现短信通知服务
type SMSNotificationService struct {
	client     *dysmsapi20170525.Client
	MaxRetries int
}

// NewSMSNotificationService 创建一个新的短信通知服务实例
func NewSMSNotificationService() *SMSNotificationService {
	config := &openapi.Config{
		AccessKeyId:     tea.String(config.AppConfig.SMS.AccessKeyID),
		AccessKeySecret: tea.String(config.AppConfig.SMS.AccessKeySecret),
	}
	config.Endpoint = tea.String("dysmsapi.aliyuncs.com")
	client, err := dysmsapi20170525.NewClient(config)
	if err != nil {
		return nil
	}
	return &SMSNotificationService{
		client:     client,
		MaxRetries: 3,
	}
}

// Send 发送短信通知
func (s *SMSNotificationService) Send(task models.AlertTask, deviceName string) error {
	par, err := json.Marshal(map[string]any{
		"name":  task.Rule.CreatedBy,
		"topic": fmt.Sprintf("%s，Topic:%s", deviceName, strings.SplitN(task.Rule.Topic, "/", 2)[1]),
		"value": task.Value,
	})
	if err != nil {
		return err
	}
	sendSmsRequest := &dysmsapi20170525.SendSmsRequest{
		PhoneNumbers:  tea.String(task.Rule.Contact),
		SignName:      tea.String(config.AppConfig.SMS.SignName),
		TemplateCode:  tea.String(config.AppConfig.SMS.SMSCode),
		TemplateParam: tea.String(string(par)),
	}
	res, err := s.client.SendSms(sendSmsRequest)
	fmt.Printf("%v , %v\n", *res.StatusCode, *res.Body.Message)

	if err != nil {
		return err
	}
	return nil
}
