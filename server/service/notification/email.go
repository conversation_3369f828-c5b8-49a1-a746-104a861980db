package notification

import (
	"beacon/cloud/config"
	"beacon/cloud/models"
	"bytes"
	"crypto/tls"
	"fmt"
	"log"
	"math/rand"
	"regexp"
	"strconv"
	"text/template"
	"time"

	"gopkg.in/gomail.v2"
)

// EmailNotificationService 实现邮件通知服务
type EmailNotificationService struct {
	MaxRetries int
}

// NewEmailNotificationService 创建一个新的邮件通知服务实例
func NewEmailNotificationService() *EmailNotificationService {
	return &EmailNotificationService{
		MaxRetries: 3,
	}
}

// Send 发送邮件通知
func (s *EmailNotificationService) Send(task models.AlertTask, deviceName string) error {

	renderedHTML, err := RenderTemplate(ThresholdAlertTemplate, map[string]string{
		"Device":      deviceName,
		"Topic":       task.Rule.Topic,
		"Value":       task.Payload,
		"Description": task.Rule.Description,
	})

	if err != nil {
		log.Printf("[EmailNotification] Error rendering threshold alert template: %v", err)
		return fmt.Errorf("error rendering template: %v", err)
	}

	// 发送邮件，支持重试
	for attempt := range s.MaxRetries {
		err = SendEmail([]string{task.Rule.Contact}, "Threshold Alert", renderedHTML)
		if err == nil {
			log.Printf("[EmailNotification] Email alert sent successfully to %s", task.Rule.Contact)
			return nil
		}

		log.Printf("[EmailNotification] Failed to send email alert (attempt %d/%d): %v",
			attempt+1, s.MaxRetries, err)

		// 重试前等待
		if attempt < s.MaxRetries-1 {
			time.Sleep(time.Duration(1<<attempt) * time.Second)
		}
	}

	return fmt.Errorf("failed to send email after %d attempts", s.MaxRetries)
}

const VerifyEmailTemplate = `
<body style="font-family: Arial, sans-serif; background: #F5F5F5; margin: 0; padding: 0;">
  <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
    <tr>
      <td align="center" style="padding: 20px;">
        <table role="presentation" width="600" cellspacing="0" cellpadding="0" border="0" style="background: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
          <tr>
            <td style="padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
              <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%">
                <tr>
                  <td align="center">
                    <img src="https://www.beaconglobaltech.com/wp-content/uploads/2024/02/logo.png" 
                         alt="BeaconGlobalTechnology" 
                         style="max-height: 50px; vertical-align: middle;">
                  </td>
                </tr>
              </table>
            </td>
          </tr>
          <tr>
            <td style="padding: 40px; text-align: center; word-wrap: break-word; word-break: break-word;">
              <p style="font-size: 16px; color: #666666; margin-bottom: 20px;">感谢您选择我们！您的验证码为：</p>
              
              <div style="font-size: 32px; font-weight: bold; color: #007BFF; background: #f0f8ff; padding: 15px; border-radius: 8px; display: inline-block;">
                {{.Content}}
              </div>
              <p style="font-size: 16px; color: #666666; margin-top: 20px;">
                该验证码将在 <strong>10分钟</strong> 后失效，请尽快使用。
              </p>
              <a href="https://www.beaconglobaltech.com" 
                 style="display: inline-block; margin-top: 20px; padding: 12px 24px; font-size: 16px; font-weight: bold; color: #ffffff; background-color: #007BFF; border-radius: 8px; text-decoration: none;">
                访问我们的网站
              </a>
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; font-size: 14px; color: #999999; border-top: 1px solid #e0e0e0;">
              <p>如果您没有请求此验证码，请忽略此邮件。</p>
              <p>如有任何问题，请联系 
                <a href="mailto:<EMAIL>" style="color: #007BFF; text-decoration: none;">
                  <EMAIL>
                </a>
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>
`
const ResetPasswordTemplate = `
<body style="font-family: Arial, sans-serif; background: #F5F5F5; margin: 0; padding: 0;">
  <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
    <tr>
      <td align="center" style="padding: 20px;">
        <table role="presentation" width="600" cellspacing="0" cellpadding="0" border="0" style="background: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
          <tr>
            <td style="padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
              <img src="https://www.beaconglobaltech.com/wp-content/uploads/2024/02/logo.png" 
                   alt="BeaconGlobalTechnology" 
                   style="max-height: 50px; vertical-align: middle;">
            </td>
          </tr>
          <tr>
            <td style="padding: 40px; text-align: center; word-wrap: break-word; word-break: break-word;">
              <p style="font-size: 16px; color: #666666; margin-bottom: 20px;">我们收到了您的重置密码请求。您的新密码为：</p>
              <div style="font-size: 32px; font-weight: bold; color: #007BFF; background: #f0f8ff; padding: 15px; border-radius: 8px; display: inline-block;">
                {{.Content}}
              </div>
              <p style="font-size: 16px; color: #666666; margin-top: 20px;">请立即登录并修改密码以确保账户安全。</p>
              <a href="https://www.beaconglobaltech.com" 
                 style="display: inline-block; margin-top: 20px; padding: 12px 24px; font-size: 16px; font-weight: bold; color: #ffffff; background-color: #007BFF; border-radius: 8px; text-decoration: none;">
                访问我们的网站
              </a>
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; font-size: 14px; color: #999999; border-top: 1px solid #e0e0e0;">
              <p>如有任何问题，请联系 
                <a href="mailto:<EMAIL>" style="color: #007BFF; text-decoration: none;">
                  <EMAIL>
                </a>
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>`

const ThresholdAlertTemplate = `
<body style="font-family: Arial, sans-serif; background: #F5F5F5; margin: 0; padding: 0;">
  <table role="presentation" width="100%" cellspacing="0" cellpadding="0" border="0">
    <tr>
      <td align="center" style="padding: 20px;">
        <table role="presentation" width="600" cellspacing="0" cellpadding="0" border="0" style="background: #ffffff; border-radius: 12px; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
          <tr>
            <td style="padding: 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
              <img src="https://www.beaconglobaltech.com/wp-content/uploads/2024/02/logo.png" 
                   alt="BeaconGlobalTechnology" 
                   style="max-height: 50px; vertical-align: middle;">
            </td>
          </tr>
          <tr>
            <td style="text-align: center;padding:30px; word-wrap: break-word; word-break: break-word;">
              <p style="font-size: 16px;text-align: left; color: #666666; margin-bottom: 20px;">设备：<strong>{{.Device}}</strong> ，主题：<strong>{{.Topic}}</strong> 当前已超出您设定的阈值：</p>
              <div style="font-size: 20px;text-align: left; color: #d9534f; background: #ffe6e6; padding: 15px; border-radius: 8px; display: inline-block;width: 100%;">
                <p><strong>Current Value: {{.Value}}</strong></p>
                <p><strong>Description: {{.Description}}</strong></p>
              </div>
              <p style="font-size: 16px; color: #666666; margin: 10px;">请立即检查设备状态并采取必要措施。</p>
              <a href="https://www.beaconglobaltech.com" 
                 style="display: inline-block; margin: 10px; padding: 12px 24px; font-size: 14px; font-weight: bold; color: #ffffff; background-color: #007BFF; border-radius: 8px; text-decoration: none;">
                查看详情
              </a>
            </td>
          </tr>
          <tr>
            <td style="padding: 20px; text-align: center; font-size: 14px; color: #999999; border-top: 1px solid #e0e0e0;">
              <p>如有任何问题，请联系 
                <a href="mailto:<EMAIL>" style="color: #007BFF; text-decoration: none;">
                  <EMAIL>
                </a>
              </p>
            </td>
          </tr>
        </table>
      </td>
    </tr>
  </table>
</body>`

func IsEmail(input string) bool {
	emailRegex := `^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`
	return regexp.MustCompile(emailRegex).MatchString(input)
}

func RenderTemplate(templateStr string, data interface{}) (string, error) {
	// 1. 解析模板
	tmpl, err := template.New("email").Parse(templateStr)
	if err != nil {
		return "", err
	}

	// 2. 渲染模板到缓冲区
	var renderedHTML bytes.Buffer
	if err := tmpl.Execute(&renderedHTML, data); err != nil {
		return "", err
	}

	// 3. 返回渲染后的 HTML 字符串
	return renderedHTML.String(), nil
}

func GenerateVerificationCode() string {
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	code := r.Intn(1000000)          // 生成 0 到 999999 的随机数
	return fmt.Sprintf("%06d", code) // 格式化为六位数
}

// SendEmail 使用 gomail 发送邮件的通用函数
func SendEmail(to []string, subject, body string) error {
	mailConfig := config.AppConfig.Mail
	smtpPort, err := strconv.Atoi(mailConfig.Port)
	if err != nil {
		return fmt.Errorf("invalid SMTP port: %v", err)
	}

	mail := gomail.NewMessage()
	mail.SetHeader("From", "BeaconGlobalTechnology"+"<"+mailConfig.Username+">")
	mail.SetHeader("To", to...)
	mail.SetHeader("Subject", subject)
	mail.SetBody("text/html", body)

	// 创建 SMTP 拨号器
	dialer := gomail.NewDialer(mailConfig.Host, smtpPort, mailConfig.Username, mailConfig.Password)
	dialer.TLSConfig = &tls.Config{InsecureSkipVerify: true}

	// 发送邮件
	if err := dialer.DialAndSend(mail); err != nil {
		return err
	}

	return nil
}
