package notification

import "beacon/cloud/models"

// NotificationService 接口定义通知服务的共同行为
type NotificationService interface {
	// Send 发送通知
	Send(task models.AlertTask, deviceName string) error
}

// NotificationFactory 基于通知方式创建相应的通知服务
func NotificationFactory(mode string) NotificationService {
	switch mode {
	case "email":
		return NewEmailNotificationService()
	case "wechat":
		return NewWechatNotificationService()
	case "sms":
		return NewSMSNotificationService()
	default:
		return NewLogNotificationService()
	}
}
