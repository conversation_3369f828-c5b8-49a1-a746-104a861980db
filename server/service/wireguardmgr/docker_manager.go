package wireguardmgr

import (
	"beacon/cloud/config"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"time"
)

// DockerManager handles WireGuard container lifecycle management
type DockerManager struct {
	baseConfigPath string
	basePort       int
}

// NewDockerManager creates a new Docker manager for WireGuard containers
func NewDockerManager() *DockerManager {
	return &DockerManager{
		baseConfigPath: "/app/wireguard-configs",
		basePort:       51888,
	}
}

// ContainerConfig represents the configuration for a WireGuard container
type ContainerConfig struct {
	ContainerName string
	ConfigPath    string
	Port          int
	ServerURL     string
	Peers         int
	InternalIP    string
}

// CreateWireGuardContainer creates a new WireGuard container for a device
func (dm *DockerManager) CreateWireGuardContainer(device *Device) error {
	log.Printf("Creating WireGuard container for device: %s", device.Name)

	// Create container configuration
	config := &ContainerConfig{
		ContainerName: fmt.Sprintf("wireguard-%s-%s", device.EnterpriseID, device.Name),
		ConfigPath:    filepath.Join(dm.baseConfigPath, fmt.Sprintf("config-%s-%s", device.EnterpriseID, device.Name)),
		Port:          device.ListenPort,
		ServerURL:     fmt.Sprintf("%s:%d", getServerHost(), device.ListenPort),
		Peers:         10,           // Initial peer count
		InternalIP:    "**********", // Fixed internal IP for each container
	}

	// Create config directory
	if err := os.MkdirAll(config.ConfigPath, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %v", err)
	}

	// Generate docker-compose configuration for this container
	if err := dm.generateDockerComposeConfig(config); err != nil {
		return fmt.Errorf("failed to generate docker-compose config: %v", err)
	}

	// Start the container
	if err := dm.startContainer(config); err != nil {
		return fmt.Errorf("failed to start container: %v", err)
	}

	// Wait for container to be ready
	if err := dm.waitForContainer(config.ContainerName); err != nil {
		return fmt.Errorf("container failed to start properly: %v", err)
	}

	log.Printf("Successfully created WireGuard container: %s", config.ContainerName)
	return nil
}

// generateDockerComposeConfig creates a docker-compose.yml for the specific container
func (dm *DockerManager) generateDockerComposeConfig(config *ContainerConfig) error {
	composeContent := fmt.Sprintf(`version: '3.8'

services:
  %s:
    image: linuxserver/wireguard:latest
    container_name: %s
    cap_add:
      - NET_ADMIN
      - SYS_MODULE
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Asia/Shanghai
      - SERVERURL=%s
      - SERVERPORT=%d
      - PEERS=%d
      - PEERDNS=auto
      - INTERNAL_SUBNET=%s
      - ALLOWEDIPS=0.0.0.0/0
      - LOG_CONFS=true
    volumes:
      - %s:/config
      - /lib/modules:/lib/modules
    ports:
      - "%d:51820/udp"
    sysctls:
      - net.ipv4.conf.all.src_valid_mark=1
    restart: unless-stopped
    networks:
      - wireguard-network

networks:
  wireguard-network:
    driver: bridge
`,
		config.ContainerName,
		config.ContainerName,
		config.ServerURL,
		51820, // Internal container port is always 51820
		config.Peers,
		config.InternalIP+"/24",
		config.ConfigPath,
		config.Port,
	)

	composeFile := filepath.Join(config.ConfigPath, "docker-compose.yml")
	return os.WriteFile(composeFile, []byte(composeContent), 0644)
}

// startContainer starts the WireGuard container using docker-compose
func (dm *DockerManager) startContainer(config *ContainerConfig) error {
	cmd := exec.Command("docker-compose", "-f", filepath.Join(config.ConfigPath, "docker-compose.yml"), "up", "-d")
	cmd.Dir = config.ConfigPath

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to start container: %v, output: %s", err, output)
	}

	log.Printf("Container started successfully: %s", config.ContainerName)
	return nil
}

// waitForContainer waits for the container to be ready
func (dm *DockerManager) waitForContainer(containerName string) error {
	maxRetries := 30
	for i := 0; i < maxRetries; i++ {
		cmd := exec.Command("docker", "inspect", "--format={{.State.Running}}", containerName)
		output, err := cmd.Output()

		if err == nil && strings.TrimSpace(string(output)) == "true" {
			// Container is running, wait a bit more for WireGuard to initialize
			time.Sleep(5 * time.Second)
			return nil
		}

		time.Sleep(2 * time.Second)
	}

	return fmt.Errorf("container %s failed to start within timeout", containerName)
}

// StopWireGuardContainer stops and removes a WireGuard container
func (dm *DockerManager) StopWireGuardContainer(device *Device) error {
	containerName := fmt.Sprintf("wireguard-%s-%s", device.EnterpriseID, device.Name)
	configPath := filepath.Join(dm.baseConfigPath, fmt.Sprintf("config-%s-%s", device.EnterpriseID, device.Name))

	log.Printf("Stopping WireGuard container: %s", containerName)

	// Stop container using docker-compose
	cmd := exec.Command("docker-compose", "-f", filepath.Join(configPath, "docker-compose.yml"), "down")
	cmd.Dir = configPath

	output, err := cmd.CombinedOutput()
	if err != nil {
		log.Printf("Warning: failed to stop container gracefully: %v, output: %s", err, output)

		// Force remove container
		forceCmd := exec.Command("docker", "rm", "-f", containerName)
		if forceErr := forceCmd.Run(); forceErr != nil {
			return fmt.Errorf("failed to force remove container: %v", forceErr)
		}
	}

	log.Printf("Successfully stopped container: %s", containerName)
	return nil
}

// RestartWireGuardContainer restarts a WireGuard container
func (dm *DockerManager) RestartWireGuardContainer(device *Device) error {
	containerName := fmt.Sprintf("wireguard-%s-%s", device.EnterpriseID, device.Name)
	configPath := filepath.Join(dm.baseConfigPath, fmt.Sprintf("config-%s-%s", device.EnterpriseID, device.Name))

	log.Printf("Restarting WireGuard container: %s", containerName)

	// Restart container using docker-compose
	cmd := exec.Command("docker-compose", "-f", filepath.Join(configPath, "docker-compose.yml"), "restart")
	cmd.Dir = configPath

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("failed to restart container: %v, output: %s", err, output)
	}

	// Wait for container to be ready
	if err := dm.waitForContainer(containerName); err != nil {
		return fmt.Errorf("container failed to restart properly: %v", err)
	}

	log.Printf("Successfully restarted container: %s", containerName)
	return nil
}

// UpdateContainerPeerConfig updates the peer configuration in a container
func (dm *DockerManager) UpdateContainerPeerConfig(device *Device, peers []Peer) error {
	containerName := fmt.Sprintf("wireguard-%s-%s", device.EnterpriseID, device.Name)
	configPath := filepath.Join(dm.baseConfigPath, fmt.Sprintf("config-%s-%s", device.EnterpriseID, device.Name))

	log.Printf("Updating peer configuration for container: %s", containerName)

	// Generate server configuration file
	serverConfig := dm.generateServerConfig(device, peers)

	// Write configuration to the mounted volume
	configFile := filepath.Join(configPath, "wg0.conf")
	if err := os.WriteFile(configFile, []byte(serverConfig), 0600); err != nil {
		return fmt.Errorf("failed to write server config: %v", err)
	}

	// Restart container to apply new configuration
	return dm.RestartWireGuardContainer(device)
}

// generateServerConfig generates the WireGuard server configuration
func (dm *DockerManager) generateServerConfig(device *Device, peers []Peer) string {
	config := fmt.Sprintf(`[Interface]
PrivateKey = %s
Address = **********/24
ListenPort = 51820
PostUp = iptables -A FORWARD -i %%i -j ACCEPT; iptables -A FORWARD -o %%i -j ACCEPT; iptables -t nat -A POSTROUTING -o eth+ -j MASQUERADE
PostDown = iptables -D FORWARD -i %%i -j ACCEPT; iptables -D FORWARD -o %%i -j ACCEPT; iptables -t nat -D POSTROUTING -o eth+ -j MASQUERADE

`, device.PrivateKey)

	// Add peer configurations
	for _, peer := range peers {
		allowedIPs := strings.Join(peer.AllowedIPs, ", ")
		config += fmt.Sprintf(`
[Peer]
PublicKey = %s
PresharedKey = %s
AllowedIPs = %s

`, peer.PublicKey, peer.PresharedKey, allowedIPs)
	}

	return config
}

// GetContainerStats retrieves statistics from a WireGuard container
func (dm *DockerManager) GetContainerStats(device *Device) (*ContainerStats, error) {
	containerName := fmt.Sprintf("wireguard-%s-%s", device.EnterpriseID, device.Name)

	// Execute wg show command inside the container
	cmd := exec.Command("docker", "exec", containerName, "wg", "show", "wg0", "dump")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to get container stats: %v", err)
	}

	return dm.parseWireGuardStats(string(output))
}

// ContainerStats represents statistics from a WireGuard container
type ContainerStats struct {
	InterfaceName string
	PublicKey     string
	ListenPort    int
	Peers         []PeerStats
}

// PeerStats represents peer statistics
type PeerStats struct {
	PublicKey       string
	PresharedKey    string
	Endpoint        string
	AllowedIPs      string
	LatestHandshake int64
	TransferRx      int64
	TransferTx      int64
}

// parseWireGuardStats parses the output of 'wg show dump'
func (dm *DockerManager) parseWireGuardStats(output string) (*ContainerStats, error) {
	lines := strings.Split(strings.TrimSpace(output), "\n")
	if len(lines) == 0 {
		return nil, fmt.Errorf("no stats data available")
	}

	stats := &ContainerStats{
		Peers: make([]PeerStats, 0),
	}

	for i, line := range lines {
		fields := strings.Split(line, "\t")
		if len(fields) < 3 {
			continue
		}

		if i == 0 {
			// First line is interface info
			stats.InterfaceName = fields[0]
			stats.PublicKey = fields[1]
			if len(fields) > 2 {
				if port, err := strconv.Atoi(fields[2]); err == nil {
					stats.ListenPort = port
				}
			}
		} else {
			// Subsequent lines are peer info
			peer := PeerStats{
				PublicKey: fields[0],
			}

			if len(fields) > 1 {
				peer.PresharedKey = fields[1]
			}
			if len(fields) > 2 {
				peer.Endpoint = fields[2]
			}
			if len(fields) > 3 {
				peer.AllowedIPs = fields[3]
			}
			if len(fields) > 4 {
				if handshake, err := strconv.ParseInt(fields[4], 10, 64); err == nil {
					peer.LatestHandshake = handshake
				}
			}
			if len(fields) > 5 {
				if rx, err := strconv.ParseInt(fields[5], 10, 64); err == nil {
					peer.TransferRx = rx
				}
			}
			if len(fields) > 6 {
				if tx, err := strconv.ParseInt(fields[6], 10, 64); err == nil {
					peer.TransferTx = tx
				}
			}

			stats.Peers = append(stats.Peers, peer)
		}
	}

	return stats, nil
}

// IsContainerRunning checks if a WireGuard container is running
func (dm *DockerManager) IsContainerRunning(device *Device) bool {
	containerName := fmt.Sprintf("wireguard-%s-%s", device.EnterpriseID, device.Name)

	cmd := exec.Command("docker", "inspect", "--format={{.State.Running}}", containerName)
	output, err := cmd.Output()

	return err == nil && strings.TrimSpace(string(output)) == "true"
}

// CleanupContainer removes container and its configuration directory
func (dm *DockerManager) CleanupContainer(device *Device) error {
	// Stop container first
	if err := dm.StopWireGuardContainer(device); err != nil {
		log.Printf("Warning: failed to stop container during cleanup: %v", err)
	}

	// Remove configuration directory
	configPath := filepath.Join(dm.baseConfigPath, fmt.Sprintf("config-%s-%s", device.EnterpriseID, device.Name))
	if err := os.RemoveAll(configPath); err != nil {
		return fmt.Errorf("failed to remove config directory: %v", err)
	}

	log.Printf("Successfully cleaned up container for device: %s", device.Name)
	return nil
}

// getServerHost returns the server host from configuration
func getServerHost() string {
	return config.AppConfig.Host
}
