package wireguardmgr

import (
	"context"
	"fmt"
	"log"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"sync"
)

// ContainerManager manages Docker containers for WireGuard devices using shell commands
type ContainerManager struct {
	mu        sync.RWMutex
	portStart int // Starting port for WireGuard containers
}

// NewContainerManager creates a new container manager
func NewContainerManager() (*ContainerManager, error) {
	// Check if Docker is available
	if err := exec.Command("docker", "version").Run(); err != nil {
		return nil, fmt.Errorf("Docker is not available: %v", err)
	}

	return &ContainerManager{
		portStart: 51888, // Starting port for WireGuard containers
	}, nil
}

// Close is a no-op for shell-based container manager
func (cm *ContainerManager) Close() error {
	return nil
}

// getNextAvailablePort finds the next available port for a new container
func (cm *ContainerManager) getNextAvailablePort(ctx context.Context) (int, error) {
	cm.mu.Lock()
	defer cm.mu.Unlock()

	// List all WireGuard containers and extract their ports
	cmd := exec.Command("docker", "ps", "-a", "--filter", "name=wg-", "--format", "{{.Ports}}")
	output, err := cmd.Output()
	if err != nil {
		return 0, fmt.Errorf("failed to list containers: %v", err)
	}

	usedPorts := make(map[int]bool)

	// Parse port information from docker ps output
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if strings.Contains(line, ":") {
			// Extract port numbers from format like "0.0.0.0:51888->51820/udp"
			parts := strings.Split(line, ":")
			if len(parts) >= 2 {
				portPart := strings.Split(parts[1], "-")[0]
				if port, err := strconv.Atoi(portPart); err == nil {
					usedPorts[port] = true
				}
			}
		}
	}

	// Find first available port
	for port := cm.portStart; port < cm.portStart+1000; port++ {
		if !usedPorts[port] {
			return port, nil
		}
	}

	return 0, fmt.Errorf("no available ports in range %d-%d", cm.portStart, cm.portStart+1000)
}

// CreateWireGuardContainer creates a new WireGuard container using docker run
func (cm *ContainerManager) CreateWireGuardContainer(ctx context.Context, deviceName, enterpriseID string, peers int) (*WireGuardContainer, error) {
	// Get next available port
	port, err := cm.getNextAvailablePort(ctx)
	if err != nil {
		return nil, err
	}

	containerName := fmt.Sprintf("wg-%s-%s", enterpriseID, deviceName)
	configDir := fmt.Sprintf("/tmp/wg-configs/%s", containerName)

	// Create config directory
	if err := os.MkdirAll(configDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create config directory: %v", err)
	}

	// Build docker run command
	args := []string{
		"run", "-d",
		"--name", containerName,
		"--restart", "unless-stopped",
		"--cap-add", "NET_ADMIN",
		"--cap-add", "SYS_MODULE",
		"--sysctl", "net.ipv4.ip_forward=1",
		"--sysctl", "net.ipv4.conf.all.src_valid_mark=1",
		"-p", fmt.Sprintf("%d:51820/udp", port),
		"-v", fmt.Sprintf("%s:/config", configDir),
		"-e", "PUID=1000",
		"-e", "PGID=1000",
		"-e", "TZ=Asia/Shanghai",
		"-e", "SERVERURL=auto",
		"-e", fmt.Sprintf("SERVERPORT=%d", port),
		"-e", fmt.Sprintf("PEERS=%d", peers),
		"-e", "PEERDNS=auto",
		"-e", "INTERNAL_SUBNET=**********",
		"linuxserver/wireguard:latest",
	}

	cmd := exec.Command("docker", args...)
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to create container: %v", err)
	}

	containerID := strings.TrimSpace(string(output))

	log.Printf("Created and started WireGuard container: %s (ID: %s) on port %d", containerName, containerID, port)

	return &WireGuardContainer{
		ID:           containerID,
		Name:         containerName,
		DeviceName:   deviceName,
		EnterpriseID: enterpriseID,
		Port:         port,
		ConfigDir:    configDir,
	}, nil
}

// StopContainer stops a WireGuard container
func (cm *ContainerManager) StopContainer(ctx context.Context, containerID string) error {
	cmd := exec.Command("docker", "stop", containerID)
	return cmd.Run()
}

// RemoveContainer removes a WireGuard container
func (cm *ContainerManager) RemoveContainer(ctx context.Context, containerID string) error {
	cmd := exec.Command("docker", "rm", "-f", containerID)
	return cmd.Run()
}

// RestartContainer restarts a WireGuard container
func (cm *ContainerManager) RestartContainer(ctx context.Context, containerID string) error {
	cmd := exec.Command("docker", "restart", containerID)
	return cmd.Run()
}

// GetContainerStatus gets the status of a container
func (cm *ContainerManager) GetContainerStatus(ctx context.Context, containerID string) (string, error) {
	cmd := exec.Command("docker", "inspect", "--format", "{{.State.Status}}", containerID)
	output, err := cmd.Output()
	if err != nil {
		return "", err
	}
	return strings.TrimSpace(string(output)), nil
}

// ListWireGuardContainers lists all WireGuard containers
func (cm *ContainerManager) ListWireGuardContainers(ctx context.Context) ([]ContainerInfo, error) {
	cmd := exec.Command("docker", "ps", "-a", "--filter", "name=wg-", "--format", "{{.ID}}\t{{.Names}}\t{{.Status}}\t{{.Ports}}")
	output, err := cmd.Output()
	if err != nil {
		return nil, err
	}

	var containers []ContainerInfo
	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		if line == "" {
			continue
		}
		parts := strings.Split(line, "\t")
		if len(parts) >= 4 {
			containers = append(containers, ContainerInfo{
				ID:     parts[0],
				Name:   parts[1],
				Status: parts[2],
				Ports:  parts[3],
			})
		}
	}

	return containers, nil
}

// WireGuardContainer represents a WireGuard container instance
type WireGuardContainer struct {
	ID           string `bson:"container_id"`
	Name         string `bson:"container_name"`
	DeviceName   string `bson:"device_name"`
	EnterpriseID string `bson:"enterprise_id"`
	Port         int    `bson:"port"`
	ConfigDir    string `bson:"config_dir"`
}

// ContainerInfo represents basic container information
type ContainerInfo struct {
	ID     string
	Name   string
	Status string
	Ports  string
}
