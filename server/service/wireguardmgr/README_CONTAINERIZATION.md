# WireGuard Containerization Refactoring

This document describes the refactoring of the WireGuard management system from host-level interface management to containerized deployment using Docker.

## Overview

The WireGuard management system has been refactored to use containerization technology for traffic isolation between interfaces. Each WireGuard device now runs in its own isolated Docker container using the `linuxserver/wireguard` image.

## Key Changes

### 1. New Docker Management System

**File: `docker_manager.go`**
- New `DockerManager` struct for container lifecycle management
- Automatic container creation, configuration, and cleanup
- Uses `linuxserver/wireguard` Docker image
- Each device gets its own isolated container with unique port mapping

### 2. Simplified Device Management

**File: `manager.go`**
- **CreateDevice**: Creates Docker containers instead of host interfaces
- **DeleteDevice**: Cleans up containers and configurations
- **Peer Management**: Updates container configurations instead of host configs
- Removed complex IPAM (IP Address Management) system
- Removed host-level rate limiting

### 3. Updated Statistics Collection

**File: `collector.go`**
- Collects statistics from containers using `docker exec` commands
- Retrieves data from database-managed devices instead of host interfaces

### 4. Port Allocation Changes

**File: `wgController.go`**
- Port range changed from 50000-59999 to 51888-52887
- Matches containerized WireGuard deployment requirements

## Container Configuration

Each WireGuard device container is configured with:

```yaml
services:
  wireguard-{enterprise}-{device}:
    image: linuxserver/wireguard:latest
    container_name: wireguard-{enterprise}-{device}
    cap_add:
      - NET_ADMIN
      - SYS_MODULE
    environment:
      - PUID=1000
      - PGID=1000
      - TZ=Asia/Shanghai
      - SERVERURL={host}:{port}
      - SERVERPORT=51820
      - PEERS=10
      - PEERDNS=auto
      - INTERNAL_SUBNET=**********/24
      - ALLOWEDIPS=0.0.0.0/0
      - LOG_CONFS=true
    volumes:
      - {config-path}:/config
      - /lib/modules:/lib/modules
    ports:
      - "{external-port}:51820/udp"
    sysctls:
      - net.ipv4.conf.all.src_valid_mark=1
    restart: unless-stopped
```

## Benefits

### 1. Traffic Isolation
- Each device runs in its own container with isolated networking
- No complex subnet allocation or routing required
- Better security boundaries between different enterprises

### 2. Simplified Management
- No need for complex IPAM system
- No host-level interface management
- Automatic container lifecycle management

### 3. Consistent Environment
- Each WireGuard instance runs in a consistent container environment
- Easier debugging and troubleshooting
- Better resource isolation

### 4. Scalability
- Containers can be easily managed and scaled
- Better resource utilization
- Easier deployment and maintenance

## Removed Components

The following components were removed as they are no longer needed:

1. **IPAM System** (`ipam.go` functionality)
   - Complex subnet allocation per enterprise
   - IP address management and tracking
   - Network routing configuration

2. **Rate Limiting** (`ratelimit.go` functionality)
   - Host-level traffic control using tc/iptables
   - Bandwidth limitation management
   - QoS configuration

3. **Host Interface Management**
   - Direct WireGuard interface creation on host
   - Host-level configuration file management
   - Interface start/stop operations

## Migration Notes

### For Existing Deployments

1. **Backup existing configurations** before migration
2. **Stop all existing WireGuard interfaces** on the host
3. **Update Docker configuration** to allow container networking
4. **Ensure Docker and docker-compose** are installed
5. **Update port mappings** in firewall rules (51888+ instead of 50000+)

### Configuration Changes

- **Port Range**: 51888-52887 (instead of 50000-59999)
- **Internal IP**: Fixed **********/24 for all containers
- **Config Path**: `/app/wireguard-configs/config-{enterprise}-{device}`

## Testing

Run the included tests to verify functionality:

```bash
cd server/service/wireguardmgr
go test -v
```

## Dependencies

- Docker Engine
- docker-compose
- `linuxserver/wireguard` Docker image

## Future Enhancements

1. **Container Health Monitoring**: Add health checks for containers
2. **Resource Limits**: Configure CPU and memory limits per container
3. **Logging Integration**: Centralized logging for all containers
4. **Backup/Restore**: Automated backup of container configurations
5. **Monitoring Dashboard**: Real-time monitoring of container status and metrics
