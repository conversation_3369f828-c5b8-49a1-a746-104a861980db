package wireguardmgr

import (
	"context"
	"log"
	"sync"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

var initOnce sync.Once

// InitIndexes initializes required database indexes
func InitIndexes(db *mongo.Database) error {
	// WireGuard device collection indexes
	_, err := db.Collection(DeviceCollection).Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "name", Value: 1},
				{Key: "enterprise_id", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "enterprise_id", Value: 1}},
		},
	})
	if err != nil {
		return err
	}

	// Peer collection indexes
	_, err = db.Collection(PeerCollection).Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "enterprise_id", Value: 1},
				{Key: "public_key", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "enterprise_id", Value: 1}},
		},
	})
	if err != nil {
		return err
	}

	// Rate limit collection indexes
	_, err = db.Collection(RateLimitCollection).Indexes().CreateMany(context.Background(), []mongo.IndexModel{
		{
			Keys: bson.D{
				{Key: "interface_name", Value: 1},
				{Key: "enterprise_id", Value: 1},
			},
			Options: options.Index().SetUnique(true),
		},
		{
			Keys: bson.D{{Key: "enterprise_id", Value: 1}},
		},
	})

	return err
}

// InitWireguardManager initializes the WireGuard manager and restores rate limits
func InitWireguardManager() {
	initOnce.Do(func() {
		log.Println("Initializing WireGuard manager...")

		// Restore rate limits
		log.Println("Restoring WireGuard interface rate limits...")
		err := RestoreAllRateLimits()
		if err != nil {
			log.Printf("Warning: failed to restore all rate limits: %v", err)
		} else {
			log.Println("Successfully restored all WireGuard interface rate limits")
		}
	})
}
