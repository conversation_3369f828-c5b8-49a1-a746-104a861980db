package wireguardmgr

import (
	"os"
	"testing"
)

// TestDockerManager tests the basic functionality of DockerManager
func TestDockerManager(t *testing.T) {
	dm := NewDockerManager()

	// Test basic configuration
	if dm.baseConfigPath != "/app/wireguard-configs" {
		t.<PERSON><PERSON><PERSON>("Expected baseConfigPath to be '/app/wireguard-configs', got %s", dm.baseConfigPath)
	}

	if dm.basePort != 51888 {
		t.<PERSON>rro<PERSON>("Expected basePort to be 51888, got %d", dm.basePort)
	}
}

// TestContainerConfig tests container configuration generation
func TestContainerConfig(t *testing.T) {
	dm := NewDockerManager()

	config := &ContainerConfig{
		ContainerName: "wireguard-test-enterprise-test-device",
		ConfigPath:    "/tmp/test-config-test-enterprise-test-device",
		Port:          51888,
		ServerURL:     "wg.beaconglobaltech.com:51888",
		Peers:         10,
		InternalIP:    "**********",
	}

	// Create test directory
	err := os.MkdirAll(config.ConfigPath, 0755)
	if err != nil {
		t.Errorf("Failed to create test directory: %v", err)
		return
	}
	defer os.RemoveAll(config.ConfigPath)

	// Test docker-compose generation
	err = dm.generateDockerComposeConfig(config)
	if err != nil {
		t.Errorf("Failed to generate docker-compose config: %v", err)
	}
}

// TestServerConfigGeneration tests WireGuard server configuration generation
func TestServerConfigGeneration(t *testing.T) {
	dm := NewDockerManager()

	device := &Device{
		PrivateKey: "test-private-key",
	}

	peers := []Peer{
		{
			PublicKey:    "peer1-public-key",
			PresharedKey: "peer1-preshared-key",
			AllowedIPs:   []string{"**********/24"},
		},
		{
			PublicKey:    "peer2-public-key",
			PresharedKey: "peer2-preshared-key",
			AllowedIPs:   []string{"**********/24"},
		},
	}

	config := dm.generateServerConfig(device, peers)

	// Check if config contains expected elements
	expectedElements := []string{
		"[Interface]",
		"PrivateKey = test-private-key",
		"Address = **********/24",
		"ListenPort = 51820",
		"[Peer]",
		"PublicKey = peer1-public-key",
		"PresharedKey = peer1-preshared-key",
		"AllowedIPs = **********/24",
	}

	for _, element := range expectedElements {
		if !contains(config, element) {
			t.Errorf("Expected config to contain '%s', but it didn't. Config: %s", element, config)
		}
	}
}

// TestWireGuardStatsParser tests the WireGuard stats parsing functionality
func TestWireGuardStatsParser(t *testing.T) {
	dm := NewDockerManager()

	// Mock WireGuard dump output
	mockOutput := `wg0	test-public-key	51820	off
peer1-public-key	peer1-preshared-key	*************:51820	**********/32	1640995200	1024	2048
peer2-public-key	peer2-preshared-key	*************:51820	**********/32	1640995300	2048	4096`

	stats, err := dm.parseWireGuardStats(mockOutput)
	if err != nil {
		t.Errorf("Failed to parse WireGuard stats: %v", err)
		return
	}

	// Check interface info
	if stats.InterfaceName != "wg0" {
		t.Errorf("Expected interface name 'wg0', got '%s'", stats.InterfaceName)
	}

	if stats.PublicKey != "test-public-key" {
		t.Errorf("Expected public key 'test-public-key', got '%s'", stats.PublicKey)
	}

	if stats.ListenPort != 51820 {
		t.Errorf("Expected listen port 51820, got %d", stats.ListenPort)
	}

	// Check peer info
	if len(stats.Peers) != 2 {
		t.Errorf("Expected 2 peers, got %d", len(stats.Peers))
		return
	}

	peer1 := stats.Peers[0]
	if peer1.PublicKey != "peer1-public-key" {
		t.Errorf("Expected peer1 public key 'peer1-public-key', got '%s'", peer1.PublicKey)
	}

	if peer1.TransferRx != 1024 {
		t.Errorf("Expected peer1 RX bytes 1024, got %d", peer1.TransferRx)
	}

	if peer1.TransferTx != 2048 {
		t.Errorf("Expected peer1 TX bytes 2048, got %d", peer1.TransferTx)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(s) > len(substr) &&
		(s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			containsSubstring(s, substr)))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
