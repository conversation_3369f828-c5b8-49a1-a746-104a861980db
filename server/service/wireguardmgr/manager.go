package wireguardmgr

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"context"
	"fmt"
	"log"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"golang.zx2c4.com/wireguard/wgctrl"
	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

// Manager wraps MongoDB client and operations on device collections
type Manager struct {
	DevicesColl   *mongo.Collection
	PeersColl     *mongo.Collection
	Helper        *WGHelper
	ipam          *IPAM
	DockerManager *DockerManager
}

// NewManager creates a new Manager based on MongoDB URI and database name
func NewManager() *Manager {
	return &Manager{
		DevicesColl:   db.MongoDB.Collection(DeviceCollection),
		PeersColl:     db.MongoDB.Collection(PeerCollection),
		Helper:        NewWG<PERSON>elper(wgConfigPath),
		ipam:          NewIPAM(),
		DockerManager: NewDockerManager(),
	}
}

// -------------------- Device CRUD Operations --------------------

// CreateDevice creates a device and generates configuration file using Docker containers
func (m *Manager) CreateDevice(ctx context.Context, device *Device) error {
	log.Printf("Starting to create containerized device: %s for enterprise: %s", device.Name, device.EnterpriseID)

	// No need for complex IP allocation - each container has its own isolated network
	// Use a fixed internal IP for simplicity
	device.IP = "**********/24"
	log.Printf("Using fixed internal IP: %s", device.IP)

	device.ID = primitive.NewObjectID()
	device.Endpoint = fmt.Sprintf("%s:%d", config.AppConfig.Host, device.ListenPort)
	privateKey, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		return fmt.Errorf("failed to generate private key: %v", err)
	}
	device.PrivateKey = privateKey.String()
	device.PublicKey = privateKey.PublicKey().String()
	device.CreatedAt = time.Now()

	// Insert into database first
	_, err = m.DevicesColl.InsertOne(ctx, device)
	if err != nil {
		return fmt.Errorf("failed to insert device into database: %v", err)
	}

	// Create Docker container for this device
	if err := m.DockerManager.CreateWireGuardContainer(device); err != nil {
		// Rollback database operation if container creation fails
		_, deleteErr := m.DevicesColl.DeleteOne(ctx, bson.M{"_id": device.ID})
		if deleteErr != nil {
			log.Printf("Warning: failed to rollback device creation: %v", deleteErr)
		}
		return fmt.Errorf("failed to create WireGuard container: %v", err)
	}

	log.Printf("Successfully created containerized device: %s", device.Name)
	return nil
}

func (m *Manager) GetDevice(ctx context.Context, enterpriseID string) (*Device, error) {
	var device Device
	err := m.DevicesColl.FindOne(ctx, bson.M{"enterprise_id": enterpriseID}).Decode(&device)
	if err != nil {
		return nil, err
	}
	return &device, nil
}

func (m *Manager) UpdateDevice(ctx context.Context, device *Device) error {
	filter := bson.M{"_id": device.ID}
	update := bson.M{"$set": bson.M{
		"name": device.Name,
		"ip":   device.IP,
	}}
	_, err := m.DevicesColl.UpdateOne(ctx, filter, update)
	return err
}

func (m *Manager) DeleteDevice(ctx context.Context, name string, enterpriseID string) error {
	// 先获取设备信息，以便后续删除相关的peers
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	log.Printf("Starting to delete containerized device: %s for enterprise: %s", name, enterpriseID)

	// 删除设备
	_, err = m.DevicesColl.DeleteOne(ctx, bson.M{"name": name, "enterprise_id": enterpriseID})
	if err != nil {
		return fmt.Errorf("failed to delete device: %v", err)
	}

	// 删除关联的所有peers
	_, err = m.PeersColl.DeleteMany(ctx, bson.M{"enterprise_id": enterpriseID})
	if err != nil {
		log.Printf("Warning: failed to delete associated peers: %v", err)
	}

	// 停止并清理Docker容器
	if err := m.DockerManager.CleanupContainer(device); err != nil {
		log.Printf("Warning: failed to cleanup WireGuard container: %v", err)
	}

	// 删除采集记录
	_, err = db.Taos.Exec(fmt.Sprintf("DROP TABLE IF EXISTS wgstats.wg_%s", name))
	if err != nil {
		log.Printf("Warning: failed to delete wgstats record: %v", err)
	}

	log.Printf("Successfully deleted containerized device: %s", name)
	return nil
}

func (m *Manager) ListDevices(ctx context.Context, limit int, page int) ([]Device, int64, error) {
	// 确保 limit 和 page 的值有效
	if limit <= 0 {
		limit = 10 // 默认每页显示10条
	}
	if page <= 0 {
		page = 1 // 默认第一页
	}

	// 获取总数
	total, err := m.DevicesColl.CountDocuments(ctx, bson.M{})
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count devices: %v", err)
	}

	skip := (page - 1) * limit

	// 使用 Find 方法时添加 Skip 和 Limit
	opts := options.Find().
		SetSkip(int64(skip)).
		SetLimit(int64(limit))

	cursor, err := m.DevicesColl.Find(ctx, bson.M{}, opts)
	if err != nil {
		return nil, 0, err
	}
	defer cursor.Close(ctx)

	var devices []Device
	if err := cursor.All(ctx, &devices); err != nil {
		return nil, 0, err
	}

	return devices, total, nil
}

// -------------------- Peer CRUD 操作 --------------------

// CreatePeer 创建一个新的 Peer，自动配置相关参数 (containerized version)
func (m *Manager) CreatePeer(ctx context.Context, deviceName string, enterpriseID string, peerName string) (*Peer, error) {
	// 获取关联的设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return nil, fmt.Errorf("failed to get device: %v", err)
	}

	// 获取现有peers数量来分配IP
	existingPeers, err := m.ListPeers(ctx, enterpriseID)
	if err != nil {
		return nil, fmt.Errorf("failed to list existing peers: %v", err)
	}

	// 为新peer分配IP (在容器内部网络中)
	peerIPNum := len(existingPeers) + 2 // 从**********开始分配
	if peerIPNum > 254 {
		return nil, fmt.Errorf("maximum number of peers reached for this device")
	}
	peerIP := fmt.Sprintf("10.13.13.%d", peerIPNum)

	// 创建新的peer
	peer := &Peer{
		ID:           primitive.NewObjectID(),
		Name:         peerName,
		EnterpriseID: enterpriseID,
		IP:           peerIP + "/32",
		CreatedAt:    time.Now(),
		DNS:          "*******,***************",
	}

	// 生成密钥对
	privateKey, err := wgtypes.GeneratePrivateKey()
	if err != nil {
		return nil, fmt.Errorf("failed to generate private key: %v", err)
	}
	peer.PrivateKey = privateKey.String()
	peer.PublicKey = privateKey.PublicKey().String()

	// 生成预共享密钥
	presharedKey, err := wgtypes.GenerateKey()
	if err != nil {
		return nil, fmt.Errorf("failed to generate preshared key: %v", err)
	}
	peer.PresharedKey = presharedKey.String()

	// 设置 AllowedIPs（允许访问整个容器子网）
	peer.AllowedIPs = []string{
		"**********/24", // 容器内部子网
	}

	// 将peer插入到wg_peers集合
	_, err = m.PeersColl.InsertOne(ctx, peer)
	if err != nil {
		return nil, fmt.Errorf("failed to insert peer: %v", err)
	}

	// 获取所有peers并更新容器配置
	allPeers, err := m.ListPeers(ctx, enterpriseID)
	if err != nil {
		// 回滚数据库操作
		m.PeersColl.DeleteOne(ctx, bson.M{"_id": peer.ID})
		return nil, fmt.Errorf("failed to list peers for config update: %v", err)
	}

	// 更新容器中的peer配置
	if err := m.DockerManager.UpdateContainerPeerConfig(device, allPeers); err != nil {
		// 回滚数据库操作
		m.PeersColl.DeleteOne(ctx, bson.M{"_id": peer.ID})
		return nil, fmt.Errorf("failed to update container peer config: %v", err)
	}

	log.Printf("Successfully created peer %s for containerized device %s", peerName, deviceName)
	return peer, nil
}

func (m *Manager) GetPeer(ctx context.Context, enterpriseID string, peerID primitive.ObjectID) (*Peer, error) {
	var peer Peer
	err := m.PeersColl.FindOne(ctx, bson.M{
		"_id":           peerID,
		"enterprise_id": enterpriseID,
	}).Decode(&peer)

	if err != nil {
		return nil, fmt.Errorf("failed to get peer: %v", err)
	}

	return &peer, nil
}

func (m *Manager) UpdatePeer(ctx context.Context, deviceName string, enterpriseID string, peer *Peer) error {
	// 更新peer
	update := bson.M{
		"$set": bson.M{
			"name":          peer.Name,
			"dns":           peer.DNS,
			"allowed_ips":   peer.AllowedIPs,
			"preshared_key": peer.PresharedKey,
		},
	}

	result, err := m.PeersColl.UpdateOne(
		ctx,
		bson.M{
			"_id":           peer.ID,
			"enterprise_id": enterpriseID,
		},
		update,
	)

	if err != nil {
		return fmt.Errorf("failed to update peer: %v", err)
	}

	if result.MatchedCount == 0 {
		return fmt.Errorf("peer not found")
	}

	// 获取设备以更新配置文件
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device for config update: %v", err)
	}

	// 获取所有peers并更新容器配置
	allPeers, err := m.ListPeers(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to list peers for config update: %v", err)
	}

	// 更新容器中的peer配置
	if err := m.DockerManager.UpdateContainerPeerConfig(device, allPeers); err != nil {
		return fmt.Errorf("failed to update container peer config: %v", err)
	}

	log.Printf("Successfully updated peer for containerized device %s", deviceName)
	return nil
}

func (m *Manager) DeletePeer(ctx context.Context, deviceName string, enterpriseID string, peerID primitive.ObjectID) error {
	// 验证peer存在
	_, err := m.GetPeer(ctx, enterpriseID, peerID)
	if err != nil {
		return fmt.Errorf("failed to get peer: %v", err)
	}

	// 从wg_peers集合中删除peer
	result, err := m.PeersColl.DeleteOne(ctx, bson.M{
		"_id":           peerID,
		"enterprise_id": enterpriseID,
	})

	if err != nil {
		return fmt.Errorf("failed to delete peer: %v", err)
	}

	if result.DeletedCount == 0 {
		return fmt.Errorf("peer not found")
	}

	// 获取设备信息以更新配置
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	// 获取所有剩余的peers并更新容器配置
	allPeers, err := m.ListPeers(ctx, enterpriseID)
	if err != nil {
		log.Printf("Warning: failed to list peers for config update: %v", err)
		return nil
	}

	// 更新容器中的peer配置
	if err := m.DockerManager.UpdateContainerPeerConfig(device, allPeers); err != nil {
		log.Printf("Warning: failed to update container peer config: %v", err)
	}

	log.Printf("Successfully deleted peer from containerized device %s", deviceName)
	return nil
}

func (m *Manager) ListPeers(ctx context.Context, enterpriseID string) ([]Peer, error) {
	// 查询该企业的所有peers
	cursor, err := m.PeersColl.Find(ctx, bson.M{
		"enterprise_id": enterpriseID,
	})

	if err != nil {
		return nil, fmt.Errorf("failed to query peers: %v", err)
	}
	defer cursor.Close(ctx)

	var peers []Peer
	if err := cursor.All(ctx, &peers); err != nil {
		return nil, fmt.Errorf("failed to decode peers: %v", err)
	}

	return peers, nil
}

// -------------------- Status --------------------
// GetInterfacesStatus 获取 WireGuard 接口状态
func (m *Manager) GetInterfacesStatus(ifaces []string) ([]DeviceStatus, error) {
	client, err := wgctrl.New()

	if err != nil {
		return nil, fmt.Errorf("failed to create wgctrl client: %v", err)
	}
	defer client.Close()

	deviceStatus := []DeviceStatus{}

	for _, iface := range ifaces {
		device, err := client.Device(iface)
		if err != nil {
			return nil, fmt.Errorf("failed to get interface status: %v", err)
		}

		//查询设备信息
		var deviceInfo Device
		err = m.DevicesColl.FindOne(context.Background(), bson.M{"device_name": iface}).Decode(&deviceInfo)
		if err != nil {
			return nil, err
		}
		deviceStatus = append(deviceStatus, DeviceStatus{
			Name:           device.Name,
			Status:         device.Type.String(),
			Port:           device.ListenPort,
			EnterpriseCode: deviceInfo.EnterpriseID,
		})
	}

	return deviceStatus, nil
}

// GetWGPeerStatus 获取 WireGuard 接口 Peer 状态
func (m *Manager) GetWGPeerStatus(iface string) ([]PeerStatusResponse, error) {
	client, err := wgctrl.New()
	if err != nil {
		return nil, fmt.Errorf("failed to create wgctrl client: %v", err)
	}
	defer client.Close()

	device, err := client.Device(iface)
	if err != nil {
		return nil, fmt.Errorf("failed to get interface status: %v", err)
	}

	deviceStatus := []PeerStatusResponse{}

	// 获取所有 peer 状态
	for _, wgPeer := range device.Peers {
		peerStatus := peer2rpc(wgPeer)
		deviceStatus = append(deviceStatus, peerStatus)
	}

	return deviceStatus, nil
}

// GetWgInterfaceBandwidth 计算指定 WireGuard 接口的总带宽（发送和接收的总字节数）
func (m *Manager) GetWgInterfaceBandwidth(iface string) (map[string]interface{}, error) {
	client, err := wgctrl.New()
	if err != nil {
		return nil, fmt.Errorf("failed to create wgctrl client: %v", err)
	}
	defer client.Close()

	// 获取设备信息
	var deviceInfo Device
	err = m.DevicesColl.FindOne(context.Background(), bson.M{"name": iface}).Decode(&deviceInfo)
	if err != nil {
		return nil, fmt.Errorf("failed to get device info: %v", err)
	}

	// 初始化带宽统计变量
	bandwidth := map[string]interface{}{
		"device_name":   iface,
		"enterprise_id": deviceInfo.EnterpriseID,
		"totalReceived": 0,
		"totalSent":     0,
		"peersCount":    0,
		"status":        "stopped", // 默认为停止状态
	}

	// 尝试获取设备状态
	device, err := client.Device(iface)
	if err != nil {
		// 如果获取设备状态失败，说明接口未启动，返回默认值
		return bandwidth, nil
	}

	// 更新状态为运行中
	bandwidth["status"] = "running"
	bandwidth["peersCount"] = len(device.Peers)

	// 累加所有 peer 的发送和接收字节数
	var totalReceived, totalSent int
	for _, wgPeer := range device.Peers {
		totalReceived += int(wgPeer.ReceiveBytes)
		totalSent += int(wgPeer.TransmitBytes)
	}

	// 更新带宽信息
	bandwidth["totalReceived"] = totalReceived
	bandwidth["totalSent"] = totalSent

	return bandwidth, nil
}

func peer2rpc(wgPeer wgtypes.Peer) PeerStatusResponse {
	return PeerStatusResponse{
		PublicKey:     wgPeer.PublicKey.String(),
		LastHandshake: wgPeer.LastHandshakeTime,
		ReceiveBytes:  wgPeer.ReceiveBytes,
		TransmitBytes: wgPeer.TransmitBytes,
	}
}

// SetDeviceBandwidthLimit 设置设备的带宽限制
func (m *Manager) SetDeviceBandwidthLimit(ctx context.Context, deviceName string, enterpriseID string, limit uint32) error {
	// 获取设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	// 应用并保存带宽限制
	rateLimit := DefaultRateLimit(limit)
	if err := SaveAndApplyRateLimit(device.Name, enterpriseID, rateLimit); err != nil {
		return fmt.Errorf("failed to apply and save bandwidth limit: %v", err)
	}

	// 更新数据库中的带宽限制值
	filter := bson.M{"_id": device.ID}
	update := bson.M{"$set": bson.M{"bandwidth_limit": limit}}
	_, err = m.DevicesColl.UpdateOne(ctx, filter, update)
	if err != nil {
		// 尝试回滚带宽限制
		RemoveAndDeleteRateLimit(device.Name, enterpriseID)
		return fmt.Errorf("failed to update device bandwidth limit in database: %v", err)
	}

	return nil
}

// RemoveDeviceBandwidthLimit 移除设备的带宽限制
func (m *Manager) RemoveDeviceBandwidthLimit(ctx context.Context, deviceName string, enterpriseID string) error {
	// 获取设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return fmt.Errorf("failed to get device: %v", err)
	}

	// 移除带宽限制并从数据库删除设置
	if err := RemoveAndDeleteRateLimit(device.Name, enterpriseID); err != nil {
		return fmt.Errorf("failed to remove and delete bandwidth limit: %v", err)
	}

	// 更新数据库中的带宽限制值
	filter := bson.M{"_id": device.ID}
	update := bson.M{"$unset": bson.M{"bandwidth_limit": ""}}
	_, err = m.DevicesColl.UpdateOne(ctx, filter, update)
	if err != nil {
		return fmt.Errorf("failed to update device bandwidth limit in database: %v", err)
	}

	return nil
}

// GetDeviceBandwidthLimit 获取设备的带宽限制
func (m *Manager) GetDeviceBandwidthLimit(ctx context.Context, deviceName string, enterpriseID string) (uint32, error) {
	// 获取设备信息
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		return 0, fmt.Errorf("failed to get device: %v", err)
	}

	// 如果数据库中没有记录带宽限制，则尝试从系统或持久化存储获取
	if device.BandwidthLimit == 0 {
		limit, err, _ := GetCurrentRateLimit(device.Name, enterpriseID)
		if err != nil || limit == nil {
			// 如果系统中也没有设置带宽限制，则返回0
			return 0, nil
		}
		return limit.Rate, nil
	}

	return device.BandwidthLimit, nil
}

// ListAllRateLimits 列出所有接口的带宽限制
func (m *Manager) ListAllRateLimits(ctx context.Context) ([]RateLimitRecord, error) {
	return ListStoredRateLimits()
}

// EnsureAllRateLimitsApplied 确保所有存储的带宽限制被应用到接口
func (m *Manager) EnsureAllRateLimitsApplied(ctx context.Context) error {
	return RestoreAllRateLimits()
}

// GetServerPeerRoutes 获取服务器端Peer的路由配置
func (m *Manager) GetServerPeerRoutes(ctx context.Context, enterpriseID string, peerID primitive.ObjectID) ([]string, error) {
	// 获取peer信息
	peer, err := m.GetPeer(ctx, enterpriseID, peerID)
	if err != nil {
		return nil, fmt.Errorf("failed to get peer: %v", err)
	}

	// 基本路由总是包含peer自己的IP地址
	routes := []string{fmt.Sprintf("%s/32", peer.IP)}

	// 查询额外的路由信息
	cursor, err := db.MongoDB.Collection(PeerRoutesCollection).Find(ctx,
		bson.M{"peer_id": peerID, "enterprise_id": enterpriseID})

	// 如果没有找到额外路由，直接返回基本路由
	if err != nil && err != mongo.ErrNoDocuments {
		return routes, nil
	}

	if err == nil {
		defer cursor.Close(ctx)

		var route struct {
			Network string `bson:"network"`
		}

		for cursor.Next(ctx) {
			if err := cursor.Decode(&route); err == nil {
				routes = append(routes, route.Network)
			}
		}
	}

	return routes, nil
}

// UpdateServerPeerRoutes 更新服务器端Peer的路由配置
func (m *Manager) UpdateServerPeerRoutes(ctx context.Context, deviceName string, enterpriseID string,
	peerID primitive.ObjectID, routes []string) error {

	// 获取peer信息
	peer, err := m.GetPeer(ctx, enterpriseID, peerID)
	if err != nil {
		return fmt.Errorf("failed to get peer: %v", err)
	}

	// 过滤掉peer自己的IP，这是默认已经包含的
	peerIPWithMask := fmt.Sprintf("%s/32", peer.IP)
	var additionalRoutes []string
	for _, route := range routes {
		if route != peerIPWithMask {
			additionalRoutes = append(additionalRoutes, route)
		}
	}

	// 备份原有路由配置，以便回滚
	var originalRoutes []struct {
		Network string `bson:"network"`
	}
	cursor, err := db.MongoDB.Collection(PeerRoutesCollection).Find(ctx,
		bson.M{"peer_id": peerID, "enterprise_id": enterpriseID})
	if err == nil {
		defer cursor.Close(ctx)
		if err = cursor.All(ctx, &originalRoutes); err != nil {
			log.Printf("Warning: failed to backup original routes: %v", err)
		}
	}

	// 清除现有的额外路由
	_, err = db.MongoDB.Collection(PeerRoutesCollection).DeleteMany(ctx,
		bson.M{"peer_id": peerID, "enterprise_id": enterpriseID})
	if err != nil {
		return fmt.Errorf("failed to clear existing routes: %v", err)
	}

	// 插入新的额外路由
	var insertedIDs []primitive.ObjectID
	for _, route := range additionalRoutes {
		result, err := db.MongoDB.Collection(PeerRoutesCollection).InsertOne(ctx, bson.M{
			"peer_id":       peerID,
			"enterprise_id": enterpriseID,
			"network":       route,
			"created_at":    time.Now(),
		})
		if err != nil {
			// 发生错误时尝试回滚已插入的路由
			if len(insertedIDs) > 0 {
				db.MongoDB.Collection(PeerRoutesCollection).DeleteMany(ctx,
					bson.M{"_id": bson.M{"$in": insertedIDs}})
			}
			return fmt.Errorf("failed to insert route %s: %v", route, err)
		}
		if oid, ok := result.InsertedID.(primitive.ObjectID); ok {
			insertedIDs = append(insertedIDs, oid)
		}
	}

	// 获取设备以更新配置文件
	device, err := m.GetDevice(ctx, enterpriseID)
	if err != nil {
		// 回滚数据库更改
		var routes []struct{ Network string }
		for _, r := range originalRoutes {
			routes = append(routes, struct{ Network string }{Network: r.Network})
		}
		restoreOriginalRoutes(ctx, peerID, enterpriseID, routes)
		return fmt.Errorf("failed to get device for config update: %v", err)
	}

	// 更新WireGuard配置文件
	if err := m.generateServerConfig(device); err != nil {
		// 回滚数据库更改
		var routes []struct{ Network string }
		for _, r := range originalRoutes {
			routes = append(routes, struct{ Network string }{Network: r.Network})
		}
		restoreOriginalRoutes(ctx, peerID, enterpriseID, routes)
		return fmt.Errorf("failed to update server config: %v", err)
	}

	// 同步配置到接口
	err = m.Helper.SyncConfig(device.Name)
	if err != nil {
		// 检查是否因为接口未运行而失败
		if !m.Helper.IsInterfaceRunning(device.Name) {
			log.Printf("Interface %s is not running, configuration file updated but not applied", device.Name)
			return nil
		}
		// 其他错误，尝试回滚
		var routes []struct{ Network string }
		for _, r := range originalRoutes {
			routes = append(routes, struct{ Network string }{Network: r.Network})
		}
		restoreOriginalRoutes(ctx, peerID, enterpriseID, routes)
		if origErr := m.generateServerConfig(device); origErr != nil {
			log.Printf("Failed to restore original config: %v", origErr)
		}

		return fmt.Errorf("failed to sync config: %v", err)
	}

	return nil
}

// 辅助函数：恢复原始路由配置
func restoreOriginalRoutes(ctx context.Context, peerID primitive.ObjectID, enterpriseID string,
	originalRoutes []struct{ Network string }) {

	// 先清除当前路由
	db.MongoDB.Collection(PeerRoutesCollection).DeleteMany(ctx,
		bson.M{"peer_id": peerID, "enterprise_id": enterpriseID})

	// 恢复原始路由
	for _, route := range originalRoutes {
		db.MongoDB.Collection(PeerRoutesCollection).InsertOne(ctx, bson.M{
			"peer_id":       peerID,
			"enterprise_id": enterpriseID,
			"network":       route.Network,
			"created_at":    time.Now(),
		})
	}
}

// UpdatePeerAllowedIPs updates the AllowedIPs for a specific peer
func (m *Manager) UpdatePeerAllowedIPs(ctx context.Context, deviceName string, enterpriseID string, peerID primitive.ObjectID, allowedIPs []string) error {
	// 这个方法需要同时更新客户端和服务器端的配置

	// 1. 更新服务器端的路由配置
	err := m.UpdateServerPeerRoutes(ctx, deviceName, enterpriseID, peerID, allowedIPs)
	if err != nil {
		return fmt.Errorf("failed to update server routes: %v", err)
	}

	// 2. 如果需要获取peer信息可以取消注释
	// peer, err := m.GetPeer(ctx, enterpriseID, peerID)
	// if err != nil {
	//	return fmt.Errorf("failed to get peer: %v", err)
	// }

	// 3. 更新客户端的AllowedIPs配置（保持不变，这是客户端需要路由的流量）
	// 注意：通常这里不需要修改，因为客户端的AllowedIPs通常是固定的网段
	// 但如果需要同步修改，可以取消下面的注释

	/*
		// 更新客户端配置
		update := bson.M{
			"$set": bson.M{
				"allowed_ips": allowedIPs,
			},
		}

		result, err := m.PeersColl.UpdateOne(
			ctx,
			bson.M{
				"_id":           peerID,
				"enterprise_id": enterpriseID,
			},
			update,
		)

		if err != nil {
			return fmt.Errorf("failed to update peer: %v", err)
		}

		if result.MatchedCount == 0 {
			return fmt.Errorf("peer not found")
		}
	*/

	return nil
}
