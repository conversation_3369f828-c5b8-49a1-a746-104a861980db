package wireguardmgr

import (
	"beacon/cloud/db"
	"context"
	"fmt"
	"log"
	"strings"
	"sync"
	"time"

	"golang.zx2c4.com/wireguard/wgctrl"
	"golang.zx2c4.com/wireguard/wgctrl/wgtypes"
)

const (
	batchSize       = 50 // batch write size
	maxWorkers      = 10 // max concurrent goroutines
	collectInterval = 15 * time.Second
)

type DeviceStat struct {
	Name       string
	PublicKey  string
	RxBytes    int64
	TxBytes    int64
	ListenPort int
}

// collector status
var (
	collecting   bool
	collectorMux sync.Mutex
	stopChan     chan struct{}
)

func GetWgInterfaceBandwidth(dev *wgtypes.Device) (int64, int64, error) {
	var receiveBytes, transmitBytes int64
	for _, peer := range dev.Peers {
		receiveBytes += peer.ReceiveBytes
		transmitBytes += peer.TransmitBytes
	}
	return receiveBytes, transmitBytes, nil
}

func insertBatch(batch []DeviceStat) error {
	// filter devices with traffic
	var validStats []DeviceStat
	for _, stat := range batch {
		if stat.RxBytes > 0 || stat.TxBytes > 0 {
			validStats = append(validStats, stat)
		}
	}

	// if no valid data, return directly
	if len(validStats) == 0 {
		return nil
	}

	var sb strings.Builder
	sb.WriteString("INSERT INTO ")

	for i, stat := range validStats {
		sb.WriteString(fmt.Sprintf(
			"wgstats.wg_%s USING %s TAGS('%s',%d) VALUES(NOW, %d, %d)",
			stat.Name,
			db.STables["wgstats"],
			stat.PublicKey,
			stat.ListenPort,
			stat.RxBytes,
			stat.TxBytes,
		))

		if i != len(validStats)-1 {
			sb.WriteString(", ")
		}
	}

	// execute batch insert
	_, err := db.Taos.Exec(sb.String())
	if err != nil {
		log.Printf("insert wg_device_stats error: %v", err)
		return err
	}

	return nil
}

// StartCollector start wireguard collector
func StartCollector(ctx context.Context) error {
	collectorMux.Lock()
	if collecting {
		collectorMux.Unlock()
		return fmt.Errorf("collector is already running")
	}

	stopChan = make(chan struct{})
	collecting = true
	collectorMux.Unlock()

	go func() {
		log.Println("Starting WireGuard statistics collector...")
		CollectDeviceStats(ctx)
	}()

	return nil
}

// StopCollector stop wireguard collector
func StopCollector() {
	collectorMux.Lock()
	defer collectorMux.Unlock()

	if !collecting {
		return
	}

	close(stopChan)
	collecting = false
	log.Println("WireGuard statistics collector stopped")
}

func CollectDeviceStats(ctx context.Context) {
	wgClient, err := wgctrl.New()
	if err != nil {
		log.Printf("Failed to initialize WireGuard client: %v", err)
		return
	}
	defer wgClient.Close()

	// start collector goroutine
	ticker := time.NewTicker(collectInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			collectAndInsertStats(wgClient)
		case <-ctx.Done():
			log.Println("Context canceled, stopping WireGuard collector")
			return
		case <-stopChan:
			return
		}
	}
}

func collectAndInsertStats(wgClient *wgctrl.Client) {
	devices, err := wgClient.Devices()
	if err != nil {
		log.Printf("Failed to get WireGuard devices: %v", err)
		return
	}

	if len(devices) == 0 {
		return
	}

	var wg sync.WaitGroup
	sem := make(chan struct{}, maxWorkers)

	// batch insert buffer
	var buffer []DeviceStat

	for _, dev := range devices {
		receiveBytes, transmitBytes, err := GetWgInterfaceBandwidth(dev)
		if err != nil {
			log.Printf("Failed to get bandwidth for device %s: %v", dev.Name, err)
			continue
		}

		buffer = append(buffer, DeviceStat{
			Name:       dev.Name,
			PublicKey:  dev.PublicKey.String(),
			RxBytes:    receiveBytes,
			TxBytes:    transmitBytes,
			ListenPort: dev.ListenPort,
		})

		// trigger write when buffer is full
		if len(buffer) >= batchSize {
			batch := make([]DeviceStat, len(buffer))
			copy(batch, buffer)

			wg.Add(1)
			sem <- struct{}{}

			go func(data []DeviceStat) {
				defer func() {
					<-sem
					wg.Done()
				}()

				if err := insertBatch(data); err != nil {
					log.Printf("Batch insert failed: %v", err)
				}
			}(batch)

			buffer = nil
		}
	}

	// handle remaining data
	if len(buffer) > 0 {
		batch := make([]DeviceStat, len(buffer))
		copy(batch, buffer)

		wg.Add(1)
		sem <- struct{}{}

		go func(data []DeviceStat) {
			defer func() {
				<-sem
				wg.Done()
			}()

			if err := insertBatch(data); err != nil {
				log.Printf("Batch insert failed: %v", err)
			}
		}(batch)
	}

	// wait for all write operations to complete
	wg.Wait()
}

// DeviceTrafficRecord represents a wireguard traffic record with timestamp
type DeviceTrafficRecord struct {
	Timestamp time.Time `json:"ts"`
	RxBytes   int64     `json:"rx_bytes"`
	TxBytes   int64     `json:"tx_bytes"`
}

// QueryDeviceStats queries the traffic records for a specific device in a time range
// deviceName: the name of the wireguard interface (e.g. "wg0")
// startTime, endTime: the time range to query
// limit: maximum number of records to return, 0 means no limit
// returns: a slice of DeviceTrafficRecord and an error if any
func QueryDeviceStats(deviceName, time_range string, limit int) ([]DeviceTrafficRecord, error) {
	if deviceName == "" {
		return nil, fmt.Errorf("device name cannot be empty")
	}
	if time_range == "" {
		time_range = "1h"
	}
	// Build query
	var query string
	if limit > 0 {
		query = fmt.Sprintf(
			"SELECT * FROM wgstats.wg_%s WHERE ts >=NOW-%s AND ts <= NOW LIMIT %d",
			deviceName, time_range, limit,
		)
	} else {
		query = fmt.Sprintf(
			"SELECT * FROM wgstats.wg_%s WHERE ts >= NOW-%s AND ts <= NOW",
			deviceName, time_range,
		)
	}
	// Execute query
	rows, err := db.Taos.Query(query)
	if err != nil {
		return nil, fmt.Errorf("query failed: %v", err)
	}
	defer rows.Close()

	// Parse results
	var results []DeviceTrafficRecord
	for rows.Next() {
		var record DeviceTrafficRecord
		var ts time.Time
		if err := rows.Scan(&ts, &record.RxBytes, &record.TxBytes); err != nil {
			return nil, fmt.Errorf("failed to scan row: %v", err)
		}
		record.Timestamp = ts
		results = append(results, record)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("error while iterating over rows: %v", err)
	}

	return results, nil
}
