package wireguardmgr

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Device represents a WireGuard device running in a container
type Device struct {
	ID           primitive.ObjectID `bson:"_id,omitempty"`
	Name         string             `bson:"name"`
	EnterpriseID string             `bson:"enterprise_id"`
	CreatedAt    time.Time          `bson:"created_at"`
	ListenPort   int                `bson:"listen_port,omitempty"` // External port mapping
	Endpoint     string             `bson:"endpoint,omitempty"`    // Format "IP:port", optional

	// Container-specific fields
	ContainerID   string `bson:"container_id,omitempty"`   // Docker container ID
	ContainerName string `bson:"container_name,omitempty"` // Docker container name
	ConfigDir     string `bson:"config_dir,omitempty"`     // Container config directory path

	// Legacy fields (deprecated but kept for migration compatibility)
	IP             string `bson:"ip,omitempty"`              // Deprecated: containers manage their own IPs
	PublicKey      string `bson:"public_key,omitempty"`      // Deprecated: generated by container
	PrivateKey     string `bson:"private_key,omitempty"`     // Deprecated: generated by container
	BandwidthLimit uint32 `bson:"bandwidth_limit,omitempty"` // Deprecated: handled by container
}

// Peer represents a WireGuard peer
type Peer struct {
	ID           primitive.ObjectID `bson:"_id,omitempty"`
	Name         string             `bson:"name"`
	EnterpriseID string             `bson:"enterprise_id"` // Enterprise ID for device association
	DNS          string             `bson:"dns"`
	PrivateKey   string             `bson:"private_key"`
	PublicKey    string             `bson:"public_key"`
	AllowedIPs   []string           `bson:"allowed_ips"` // e.g. "********/32"
	IP           string             `bson:"ip"`
	CreatedAt    time.Time          `bson:"created_at"`
	PresharedKey string             `bson:"preshared_key,omitempty"` // Preshared key
}

type DeviceStatus struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	Name           string             `bson:"name"`
	Status         string             `bson:"status"`
	EnterpriseCode string             `bson:"enterprise_code"`
	Port           int                `bson:"port"`
}

type PeerStatusResponse struct {
	PublicKey     string    `bson:"public_key"`
	LastHandshake time.Time `bson:"last_handshake"`
	ReceiveBytes  int64     `bson:"receive_bytes"`
	TransmitBytes int64     `bson:"transmit_bytes"`
}

type ListPeersRequest struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

const (
	DeviceCollection     = "wg_devices"
	PeerCollection       = "wg_peers"
	PeerRoutesCollection = "peer_routes"

	// Container-specific constants
	ContainerConfigBasePath = "/tmp/wg-configs" // Base path for container config directories
	DefaultPeersCount       = 1                 // Default number of peers to create in container
)
