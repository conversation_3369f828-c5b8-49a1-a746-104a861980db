package wireguardmgr

import (
	"time"

	"go.mongodb.org/mongo-driver/bson/primitive"
)

// Device represents a WireGuard device
type Device struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	Name           string             `bson:"name"`
	EnterpriseID   string             `bson:"enterprise_id"`
	IP             string             `bson:"ip"` // Management subnet or device IP
	CreatedAt      time.Time          `bson:"created_at"`
	ListenPort     int                `bson:"listen_port,omitempty"`     // System will automatically assign port
	Endpoint       string             `bson:"endpoint,omitempty"`        // Format "IP:port", optional
	PublicKey      string             `bson:"public_key,omitempty"`      // Public key
	PrivateKey     string             `bson:"private_key,omitempty"`     // Private key
	BandwidthLimit uint32             `bson:"bandwidth_limit,omitempty"` // 带宽限制，单位为 kbps
	// Peers field removed, use association query instead
}

// Peer represents a WireGuard peer
type Peer struct {
	ID           primitive.ObjectID `bson:"_id,omitempty"`
	Name         string             `bson:"name"`
	EnterpriseID string             `bson:"enterprise_id"` // Enterprise ID for device association
	DNS          string             `bson:"dns"`
	PrivateKey   string             `bson:"private_key"`
	PublicKey    string             `bson:"public_key"`
	AllowedIPs   []string           `bson:"allowed_ips"` // e.g. "********/32"
	IP           string             `bson:"ip"`
	CreatedAt    time.Time          `bson:"created_at"`
	PresharedKey string             `bson:"preshared_key,omitempty"` // Preshared key
}

type DeviceStatus struct {
	ID             primitive.ObjectID `bson:"_id,omitempty"`
	Name           string             `bson:"name"`
	Status         string             `bson:"status"`
	EnterpriseCode string             `bson:"enterprise_code"`
	Port           int                `bson:"port"`
}

type PeerStatusResponse struct {
	PublicKey     string    `bson:"public_key"`
	LastHandshake time.Time `bson:"last_handshake"`
	ReceiveBytes  int64     `bson:"receive_bytes"`
	TransmitBytes int64     `bson:"transmit_bytes"`
}

type ListPeersRequest struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

const (
	DeviceCollection     = "wg_devices"
	PeerCollection       = "wg_peers"
	PeerRoutesCollection = "peer_routes"
	wgConfigPath         = "/etc/wireguard"
)
