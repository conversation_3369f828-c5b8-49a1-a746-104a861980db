package mqttserver

import (
	"beacon/cloud/db"
	"beacon/cloud/models"
	"fmt"
	"log"
	"strconv"
	"strings"

	"github.com/Knetic/govaluate"
	_ "github.com/taosdata/driver-go/v3/taosWS"
)

// ----------------------------------------------------Taos----------------------------------------------------
// 替换 `/` 为 `_`
func sanitizeTableName(name string) string {
	return strings.ReplaceAll(name, "/", "_")
}

func InsertMessage(tableName, tag string, clientID, topicName, payload string, qos byte, isAlarm bool) error {
	query := fmt.Sprintf(
		"INSERT INTO data_%s USING %s TAGS ('%s') VALUES (NOW,'%s','%s','%s',%d,%v)",
		tableName, db.STables["messages"], tag, clientID, topicName, payload, qos, isAlarm)
	// log.Printf("InsertMessage query: %s", query)
	_, err := db.Taos.Exec(query)
	return err
}

func InsertAlarm(topic, value, description, threshold_type, threshold_contact, send_status, enterprise_id string) error {
	query := fmt.Sprintf("INSERT INTO alert_%s USING %s TAGS('%s') VALUES(NOW, '%s', %s, '%s', '%s','%s','%s')",
		sanitizeTableName(topic), db.STables["alarm_log"], enterprise_id, topic, value, description, threshold_type, threshold_contact, send_status)

	_, err := db.Taos.Exec(query)
	if err != nil {
		log.Printf("Error inserting threshold: %v", err)
	}
	return err
}

func GetAlarmListPagination(page, pageSize int, enterpriseID, role string) (int, []models.Alarm, error) {
	var query string
	if role == "admin" || role == "superadmin" {
		query = fmt.Sprintf("SELECT * FROM %s ORDER BY ts DESC LIMIT %d OFFSET %d",
			db.STables["alarm_log"], pageSize, (page-1)*pageSize)
	} else {
		query = fmt.Sprintf("SELECT * FROM %s WHERE enterprise_id = '%s' ORDER BY ts DESC LIMIT %d OFFSET %d",
			db.STables["alarm_log"], enterpriseID, pageSize, (page-1)*pageSize)
	}
	rows, err := db.Taos.Query(query)
	if err != nil {
		return 0, nil, err
	}
	defer rows.Close()

	var alarms []models.Alarm
	for rows.Next() {
		var alarm models.Alarm
		err := rows.Scan(&alarm.Ts, &alarm.Topic, &alarm.Value, &alarm.Desc, &alarm.AlarmType, &alarm.AlarmContact, &alarm.Msg, &alarm.Username)
		if err != nil {
			return 0, nil, err
		}
		alarms = append(alarms, alarm)
	}

	total := 0
	var countQuery string
	if role == "admin" || role == "superadmin" {
		countQuery = fmt.Sprintf("SELECT COUNT(*) FROM %s", db.STables["alarm_log"])
	} else {
		countQuery = fmt.Sprintf("SELECT COUNT(*) FROM %s WHERE enterprise_id = '%s'", db.STables["alarm_log"], enterpriseID)
	}
	err = db.Taos.QueryRow(countQuery).Scan(&total)
	if err != nil {
		return 0, nil, err
	}

	return total, alarms, nil
}

// GetAlarmsInTimeRange retrieves alarms within a specified time range
func GetAlarmsInTimeRange(enterpriseID, startTime, endTime string) ([]models.Alarm, error) {
	query := fmt.Sprintf("SELECT * FROM %s WHERE enterprise_id = '%s' AND ts >= '%s' AND ts <= '%s' ORDER BY ts DESC",
		db.STables["alarm_log"], enterpriseID, startTime, endTime)

	rows, err := db.Taos.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var alarms []models.Alarm
	for rows.Next() {
		var alarm models.Alarm
		err := rows.Scan(&alarm.Ts, &alarm.Topic, &alarm.Value, &alarm.Desc, &alarm.AlarmType, &alarm.AlarmContact, &alarm.Msg, &alarm.Username)
		if err != nil {
			return nil, err
		}
		alarms = append(alarms, alarm)
	}

	return alarms, nil
}

//-------------------------------------value evaluate-------------------------------------

// evaluateExpression evaluates a threshold expression with the given value
func evaluateExpression(expression string, value float64) (bool, error) {
	// Normalize the expression by removing spaces
	normalizedExpr := strings.ReplaceAll(expression, " ", "")

	// Handle special case for OR expressions like "value<10||value>20"
	if strings.Contains(normalizedExpr, "||") &&
		(strings.Contains(normalizedExpr, "value<") || strings.Contains(normalizedExpr, "value>") ||
			strings.Contains(normalizedExpr, "value<=") || strings.Contains(normalizedExpr, "value>=")) {
		parts := strings.Split(normalizedExpr, "||")

		// Check if we have exactly two conditions for outside range
		if len(parts) == 2 {
			expr1 := strings.ReplaceAll(parts[0], "value", fmt.Sprintf("%f", value))
			expr2 := strings.ReplaceAll(parts[1], "value", fmt.Sprintf("%f", value))

			// Create combined expression for evaluation
			combinedExpr := fmt.Sprintf("(%s) || (%s)", expr1, expr2)
			evalExpr, err := govaluate.NewEvaluableExpression(combinedExpr)
			if err != nil {
				return false, fmt.Errorf("invalid outside range expression format: %v", err)
			}

			result, err := evalExpr.Evaluate(nil)
			if err != nil {
				return false, fmt.Errorf("error evaluating outside range expression: %v", err)
			}

			boolResult, ok := result.(bool)
			if !ok {
				return false, fmt.Errorf("outside range expression did not evaluate to a boolean value")
			}

			return boolResult, nil
		}
	}

	// Handle special case for range expressions like "10<value<20" or "value>10 && value<20"
	if strings.Contains(normalizedExpr, "<value<") || strings.Contains(normalizedExpr, ">value>") ||
		strings.Contains(normalizedExpr, "<=value<=") || strings.Contains(normalizedExpr, ">=value>=") ||
		strings.Contains(normalizedExpr, "<value<=") || strings.Contains(normalizedExpr, "<=value<") ||
		strings.Contains(normalizedExpr, ">value>=") || strings.Contains(normalizedExpr, ">=value>") {
		return evaluateRangeExpression(normalizedExpr, value)
	}

	// For standard expressions, use govaluate
	// Replace 'value' with the actual value in the expression
	expr := strings.ReplaceAll(normalizedExpr, "value", fmt.Sprintf("%f", value))

	// Use govaluate to evaluate the expression
	evalExpr, err := govaluate.NewEvaluableExpression(expr)
	if err != nil {
		return false, fmt.Errorf("invalid expression format: %v", err)
	}

	result, err := evalExpr.Evaluate(nil)
	if err != nil {
		return false, fmt.Errorf("error evaluating expression: %v", err)
	}

	// Convert the result to boolean
	boolResult, ok := result.(bool)
	if !ok {
		return false, fmt.Errorf("expression did not evaluate to a boolean value")
	}

	return boolResult, nil
}

// evaluateRangeExpression handles range expressions like "10<value<20"
func evaluateRangeExpression(expression string, value float64) (bool, error) {
	// Extract the range values
	var min, max float64
	var minOp, maxOp string
	var err error

	if strings.Contains(expression, "<value<") {
		parts := strings.Split(expression, "<value<")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = "<"
		maxOp = "<"
	} else if strings.Contains(expression, ">value>") {
		parts := strings.Split(expression, ">value>")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = ">"
		maxOp = ">"
	} else if strings.Contains(expression, "<=value<=") {
		parts := strings.Split(expression, "<=value<=")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = "<="
		maxOp = "<="
	} else if strings.Contains(expression, ">=value>=") {
		parts := strings.Split(expression, ">=value>=")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = ">="
		maxOp = ">="
	} else if strings.Contains(expression, "<value<=") {
		parts := strings.Split(expression, "<value<=")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = "<"
		maxOp = "<="
	} else if strings.Contains(expression, "<=value<") {
		parts := strings.Split(expression, "<=value<")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = "<="
		maxOp = "<"
	} else if strings.Contains(expression, ">value>=") {
		parts := strings.Split(expression, ">value>=")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = ">"
		maxOp = ">="
	} else if strings.Contains(expression, ">=value>") {
		parts := strings.Split(expression, ">=value>")
		if len(parts) != 2 {
			return false, fmt.Errorf("invalid range expression format")
		}
		min, err = strconv.ParseFloat(strings.TrimSpace(parts[1]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid minimum value: %v", err)
		}
		max, err = strconv.ParseFloat(strings.TrimSpace(parts[0]), 64)
		if err != nil {
			return false, fmt.Errorf("invalid maximum value: %v", err)
		}
		minOp = ">="
		maxOp = ">"
	} else {
		return false, fmt.Errorf("unsupported range expression format")
	}

	// Create a standard expression to evaluate with govaluate
	var standardExpr string
	if minOp == "<" && maxOp == "<" {
		standardExpr = fmt.Sprintf("%f < %f && %f < %f", min, value, value, max)
	} else if minOp == "<=" && maxOp == "<=" {
		standardExpr = fmt.Sprintf("%f <= %f && %f <= %f", min, value, value, max)
	} else if minOp == ">" && maxOp == ">" {
		standardExpr = fmt.Sprintf("%f > %f && %f > %f", max, value, value, min)
	} else if minOp == ">=" && maxOp == ">=" {
		standardExpr = fmt.Sprintf("%f >= %f && %f >= %f", max, value, value, min)
	} else if minOp == "<" && maxOp == "<=" {
		standardExpr = fmt.Sprintf("%f < %f && %f <= %f", min, value, value, max)
	} else if minOp == "<=" && maxOp == "<" {
		standardExpr = fmt.Sprintf("%f <= %f && %f < %f", min, value, value, max)
	} else if minOp == ">" && maxOp == ">=" {
		standardExpr = fmt.Sprintf("%f > %f && %f >= %f", max, value, value, min)
	} else if minOp == ">=" && maxOp == ">" {
		standardExpr = fmt.Sprintf("%f >= %f && %f > %f", max, value, value, min)
	}

	// Use govaluate to evaluate the expression
	evalExpr, err := govaluate.NewEvaluableExpression(standardExpr)
	if err != nil {
		return false, fmt.Errorf("error creating evaluable expression: %v", err)
	}

	result, err := evalExpr.Evaluate(nil)
	if err != nil {
		return false, fmt.Errorf("error evaluating range expression: %v", err)
	}

	// Convert the result to boolean
	boolResult, ok := result.(bool)
	if !ok {
		return false, fmt.Errorf("range expression did not evaluate to a boolean value")
	}

	return boolResult, nil
}
