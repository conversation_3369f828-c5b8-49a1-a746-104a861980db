package mqttserver

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"beacon/cloud/models"
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

// ------------------------------------------------------------------------------------------------------------------------
//
//	Message Management
//
// ------------------------------------------------------------------------------------------------------------------------
// GetTopicLatestMessage 获取主题最新的一条消息
func GetTopicLatestMessage(topic models.Topic) (*models.Message, error) {
	// 使用静态缓存键来减少对TDengine的频繁访问
	cacheKey := fmt.Sprintf("latest-msg:%s", topic.Topic)
	if cachedData, found := messageCache.Load(cacheKey); found {
		message := cachedData.(*models.Message)

		// 检查是否过期(5秒)
		if time.Since(message.Ts) < 5*time.Second {
			return message, nil
		}

		// 过期则移除缓存
		messageCache.Delete(cacheKey)
	}

	// 创建查询上下文
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// 优化SQL查询，明确指定需要的列和排序条件
	query := db.QuerySelectString(db.STables["messages"], "*",
		fmt.Sprintf("(topic_name = '%s') ORDER BY ts DESC LIMIT 1", topic.Topic))

	rows, err := db.Taos.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var message models.Message
	if rows.Next() {
		err := rows.Scan(&message.Ts, &message.ClientID, &message.Topic,
			&message.Payload, &message.QoS, &message.IsAlarm, &message.Username)
		if err != nil {
			return nil, err
		}

		// 存入缓存
		messageCache.Store(cacheKey, &message)
	} else {
		return nil, fmt.Errorf("no messages found for topic: %s", topic.Topic)
	}
	return &message, nil
}

// GetTopicMessagesWithPagination 获取主题消息的分页列表
func GetTopicMessagesWithPagination(topic models.Topic, page, pageSize int) (int64, []models.Message, error) {
	// 获取总数
	countQuery := db.QuerySelectString(db.STables["messages"], "COUNT(*)", fmt.Sprintf("(topic_name = '%s')", topic.Topic))
	countRows, err := db.Taos.Query(countQuery)
	if err != nil {
		return 0, nil, err
	}
	defer countRows.Close()

	var count int64
	if countRows.Next() {
		err := countRows.Scan(&count)
		if err != nil {
			return 0, nil, err
		}
	}
	// 获取分页数据
	offset := (page - 1) * pageSize
	query := db.QuerySelectString(db.STables["messages"], "*", fmt.Sprintf("(topic_name = '%s') ORDER BY ts DESC LIMIT %d OFFSET %d", topic.Topic, pageSize, offset))
	rows, err := db.Taos.Query(query)
	if err != nil {
		return 0, nil, err
	}
	defer rows.Close()

	var messages []models.Message
	for rows.Next() {
		var message models.Message
		err := rows.Scan(&message.Ts, &message.ClientID, &message.Topic, &message.Payload, &message.QoS, &message.IsAlarm, &message.Username)
		if err != nil {
			return 0, nil, err
		}
		messages = append(messages, message)
	}

	return count, messages, nil
}

// 获取主题时间范围内消息
func GetTopicMessagesInTimeRange(topic models.Topic, startTime, endTime string) ([]models.Message, error) {
	query := db.QuerySelectString(db.STables["messages"], "*", fmt.Sprintf("(topic_name = '%s' AND ts >= '%s' AND ts <= '%s')", topic.Topic, startTime, endTime))
	rows, err := db.Taos.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var messages []models.Message
	for rows.Next() {
		var message models.Message
		err := rows.Scan(&message.Ts, &message.ClientID, &message.Topic, &message.Payload, &message.QoS, &message.IsAlarm, &message.Username)
		if err != nil {
			return nil, err
		}
		messages = append(messages, message)
	}
	return messages, nil
}

// 消息缓存，使用sync.Map避免并发问题
var messageCache sync.Map

// GetTopicMessagesInTimeRangeWithCache 获取主题时间范围内消息，带缓存支持
func GetTopicMessagesInTimeRangeWithCache(topic models.Topic, startTime, endTime string, cacheKey string) ([]models.Message, error) {
	// 检查缓存
	if cachedData, found := messageCache.Load(cacheKey); found {
		return cachedData.([]models.Message), nil
	}

	// 使用批量预处理和参数化查询避免SQL注入
	query := db.QuerySelectString(db.STables["messages"], "*",
		fmt.Sprintf("(topic_name = '%s' AND ts >= '%s' AND ts <= '%s') ORDER BY ts DESC",
			topic.Topic, startTime, endTime))

	// 执行查询，使用上下文控制超时
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	rows, err := db.Taos.QueryContext(ctx, query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	// 预分配空间减少内存分配
	messages := make([]models.Message, 0, 100)

	for rows.Next() {
		var message models.Message
		err := rows.Scan(
			&message.Ts,
			&message.ClientID,
			&message.Topic,
			&message.Payload,
			&message.QoS,
			&message.IsAlarm,
			&message.Username,
		)
		if err != nil {
			return nil, err
		}
		messages = append(messages, message)
	}

	// 存入缓存，过期时间5分钟
	messageCache.Store(cacheKey, messages)
	go func() {
		time.Sleep(5 * time.Minute)
		messageCache.Delete(cacheKey)
	}()

	return messages, nil
}

// ------------------------------------------------------------------------------------------------------------------------
//
//	Topic Management
//
// ------------------------------------------------------------------------------------------------------------------------
// CheckTopicPermission 检查用户是否有权限访问特定主题
func CheckTopicPermission(clientId string, topic string) bool {
	collection := db.MongoDB.Collection("mqtt_topics")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 从clientId中提取用户名（假设clientId格式为 "username_randomstring"）
	username := strings.Split(clientId, "_")[0]

	// 查找该主题是否由该用户创建
	var topicDoc models.Topic
	err := collection.FindOne(ctx, bson.M{
		"topic":      topic,
		"created_by": username,
	}).Decode(&topicDoc)

	return err == nil
}

// GetTopicsCount 获取主题总数
func GetTopicsCount() (int64, error) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	count, err := db.MongoDB.Collection("mqtt_topics").CountDocuments(ctx, bson.M{})
	if err != nil {
		return 0, err
	}
	return count, nil
}

// GetTopics 获取主题列表
func GetTopics(currentUser string, role string) ([]models.Topic, error) {
	collection := db.MongoDB.Collection("mqtt_topics")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	filter := bson.M{}
	if role != "superadmin" {
		filter["created_by"] = currentUser
	}

	opts := options.Find().SetSort(bson.D{{Key: "last_updated", Value: -1}})
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return nil, err
	}
	defer cursor.Close(ctx)

	var topics []models.Topic
	if err = cursor.All(ctx, &topics); err != nil {
		return nil, err
	}
	return topics, nil
}

// GetTopicsWithPagination 获取分页的主题列表
func GetTopicsWithPagination(currentUser string, role string, page, pageSize int) (int64, []models.Topic, error) {
	collection := db.MongoDB.Collection("mqtt_topics")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	filter := bson.M{}
	if role != "superadmin" {
		filter["created_by"] = currentUser
	}

	// 获取总数
	total, err := collection.CountDocuments(ctx, filter)
	if err != nil {
		return 0, nil, err
	}

	// 设置分页选项
	skip := int64((page - 1) * pageSize)
	opts := options.Find().
		SetSort(bson.D{{Key: "last_updated", Value: -1}}).
		SetSkip(skip).
		SetLimit(int64(pageSize))

	// 获取分页数据
	cursor, err := collection.Find(ctx, filter, opts)
	if err != nil {
		return 0, nil, err
	}
	defer cursor.Close(ctx)

	var topics []models.Topic
	if err = cursor.All(ctx, &topics); err != nil {
		return 0, nil, err
	}

	return total, topics, nil
}

// CreateTopic
func CreateTopic(topic, enterpriseId, username, deviceSN, dataType string) error {
	collection := db.MongoDB.Collection("mqtt_topics")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if !isValidDataType(dataType) {
		return fmt.Errorf("invalid data type: %s", dataType)
	}

	var existingTopic models.Topic
	err := collection.FindOne(ctx, bson.M{"topic": topic}).Decode(&existingTopic)
	if err == nil {
		return fmt.Errorf("topic already exists")
	} else if err != mongo.ErrNoDocuments {
		return fmt.Errorf("failed to check topic: %v", err)
	}

	newTopic := models.Topic{
		ID:           topic,
		Topic:        topic,
		CreatedBy:    username,
		EnterpriseID: enterpriseId,
		CreatedAt:    time.Now(),
		LastUpdated:  time.Now(),
		DataType:     dataType,
		DeviceSN:     deviceSN,
	}

	_, err = collection.InsertOne(ctx, newTopic)
	if err != nil {
		return fmt.Errorf("failed to create topic: %v", err)
	}

	// 更新设备的topics列表
	deviceCollection := db.MongoDB.Collection("devices")
	_, err = deviceCollection.UpdateOne(
		ctx,
		bson.M{"device_sn": deviceSN},
		bson.M{
			"$addToSet": bson.M{
				"topics": topic,
			},
		},
	)
	if err != nil {
		return fmt.Errorf("failed to update device topics: %v", err)
	}

	return nil
}

// 删除主题
func DeleteTopic(topic string, deviceSN string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 先检查主题是否存在
	topicResult := db.MongoDB.Collection("mqtt_topics").FindOne(ctx,
		bson.M{"topic": topic, "device_sn": deviceSN})
	if topicResult.Err() == mongo.ErrNoDocuments {
		return fmt.Errorf("topic not found: %s", topic)
	}

	// 从主题列表中删除
	deleteResult, err := db.MongoDB.Collection("mqtt_topics").DeleteOne(ctx,
		bson.M{"topic": topic, "device_sn": deviceSN})
	if err != nil {
		return fmt.Errorf("failed to delete topic: %v", err)
	}
	if deleteResult.DeletedCount == 0 {
		return fmt.Errorf("no topic was deleted, topic: %s, deviceSN: %s", topic, deviceSN)
	}

	// 从设备中删除主题
	result, err := db.MongoDB.Collection("devices").UpdateOne(
		ctx,
		bson.M{"device_sn": deviceSN},
		bson.M{"$pull": bson.M{"topics": topic}},
	)
	if err != nil {
		return fmt.Errorf("failed to delete topic from device: %v", err)
	}
	if result.MatchedCount == 0 {
		return fmt.Errorf("device not found: %s", deviceSN)
	}

	// 修改 TDengine 删除语句
	tableName := strings.ReplaceAll(topic, "/", "_")    // 替换斜杠
	tableName = strings.ReplaceAll(tableName, ".", "_") // 替换点号
	query := fmt.Sprintf("DROP TABLE IF EXISTS `%s`.`%s_data`",
		config.AppConfig.TaosDB.DBName, tableName)
	_, err = db.Taos.Exec(query)
	if err != nil {
		return fmt.Errorf("failed to delete messages: %v", err)
	}

	return nil
}

// isValidDataType 检查数据类型是否有效
func isValidDataType(dataType string) bool {
	validTypes := map[string]bool{
		"number":  true,
		"string":  true,
		"boolean": true,
	}
	return validTypes[dataType]
}

//	--------------------------------------------------------
//						Warning Rules					----
// ---------------------------------------------------------

// CreateThresholdRule 创建新的阈值规则
func CreateThresholdRule(rule *models.ThresholdRule) error {
	collection := db.MongoDB.Collection("mqtt_topic_thresholds")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 使用count计数器而不是完整查询，减少IO负担
	count, err := collection.CountDocuments(ctx, bson.M{"topic": rule.Topic})
	if err != nil {
		return fmt.Errorf("查询阈值规则失败: %w", err)
	}

	if count > 0 {
		return fmt.Errorf("该主题已存在阈值规则")
	}

	// 设置规则ID和时间戳
	rule.ID = primitive.NewObjectID().Hex()
	now := time.Now()
	rule.CreatedAt = now
	rule.UpdatedAt = now

	// 设置写入选项，提高写入性能
	opts := options.InsertOne().SetBypassDocumentValidation(true)

	_, err = collection.InsertOne(ctx, rule, opts)
	if err != nil {
		return fmt.Errorf("创建阈值规则失败: %w", err)
	}

	// 更新主题阈值规则缓存
	messageCache.Delete(fmt.Sprintf("threshold:%s", rule.Topic))

	return nil
}

// UpdateThresholdRule 更新阈值规则
func UpdateThresholdRule(rule *models.ThresholdRule) error {
	collection := db.MongoDB.Collection("mqtt_topic_thresholds")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	rule.UpdatedAt = time.Now()

	_, err := collection.UpdateOne(
		ctx,
		bson.M{"_id": rule.ID},
		bson.M{"$set": rule},
	)
	return err
}

// GetThresholdRule 获取主题的阈值规则
func GetThresholdRule(topicID string) (models.ThresholdRule, error) {
	// 检查缓存
	cacheKey := fmt.Sprintf("threshold:%s", topicID)
	if cachedData, found := messageCache.Load(cacheKey); found {
		return cachedData.(models.ThresholdRule), nil
	}

	collection := db.MongoDB.Collection("mqtt_topic_thresholds")
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// 优化查询，使用投影仅获取必要字段
	var rule models.ThresholdRule
	err := collection.FindOne(
		ctx,
		bson.M{"topic": topicID},
	).Decode(&rule)

	if err != nil {
		if err == mongo.ErrNoDocuments {
			return models.ThresholdRule{}, nil
		}
		return models.ThresholdRule{}, fmt.Errorf("查询阈值规则失败: %w", err)
	}

	// 存入缓存，有效期30秒
	messageCache.Store(cacheKey, rule)
	go func() {
		time.Sleep(30 * time.Second)
		messageCache.Delete(cacheKey)
	}()

	return rule, nil
}

// DeleteThresholdRule 删除阈值规则
func DeleteThresholdRule(ruleID string) error {
	collection := db.MongoDB.Collection("mqtt_topic_thresholds")
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	_, err := collection.DeleteOne(ctx, bson.M{"_id": ruleID})
	return err
}
