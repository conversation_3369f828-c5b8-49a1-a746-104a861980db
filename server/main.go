package main

import (
	"beacon/cloud/config"
	"beacon/cloud/db"
	"beacon/cloud/routes"
	"beacon/cloud/service/mqttserver"
	"beacon/cloud/service/wireguardmgr"
	"context"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/gin-gonic/gin"
)

// InitializeApp 初始化应用程序
func InitializeApp() error {
	// load config
	if err := config.LoadConfig("config/config.yaml"); err != nil {
		return fmt.Errorf("failed to load config: %w", err)
	}

	// init databases
	if err := db.Init(); err != nil {
		return fmt.Errorf("failed to init databases: %w", err)
	}

	// ensure all necessary indexes exist
	if err := db.EnsureIndexes(); err != nil {
		return fmt.Errorf("failed to create indexes: %w", err)
	}

	// init network settings
	// if err := initNetworkSettings(); err != nil {
	// 	return fmt.Errorf("failed to apply network settings: %w", err)
	// }

	// init mqtt server
	fmt.Println("-------------------------------------MqttServer-----------------------------------")
	mqttserver.NewMQTTServer()
	if err := mqttserver.Start(); err != nil {
		return fmt.Errorf("failed to start MQTT server: %w", err)
	}
	fmt.Println("----------------------------------------------------------------------------------")

	return nil
}

// func initNetworkSettings() error {
// 	// enable net.ipv4.ip_forward
// 	if err := utils.SetSysctl("net/ipv4/ip_forward", "1"); err != nil {
// 		return err
// 	}

// 	// enable net.ipv4.conf.all.src_valid_mark
// 	if err := utils.SetSysctl("net/ipv4/conf/all/src_valid_mark", "1"); err != nil {
// 		return err
// 	}
// 	return nil
// }

func main() {
	err := InitializeApp()
	if err != nil {
		log.Fatal("Failed to InitializeApp :", err.Error())
	}

	// create a cancelable context
	ctx, cancel := context.WithCancel(context.Background())

	// start wireguard collector
	if err := wireguardmgr.StartCollector(ctx); err != nil {
		log.Printf("Warning: Failed to start WireGuard collector: %v", err)
	}

	r := gin.Default()
	routes.Routes(r)

	go func() {
		if err := r.Run(":8080"); err != nil {
			log.Fatal("Failed to start Gin server:", err)
		}
	}()

	// close
	sigs := make(chan os.Signal, 1)
	done := make(chan bool, 1)
	signal.Notify(sigs, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigs

		cancel()
		// stop wireguard collector
		wireguardmgr.StopCollector()
		done <- true
	}()

	<-done
	log.Println("Shutting down server...")
}
