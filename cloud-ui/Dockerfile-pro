# 使用 Node.js 官方镜像作为构建阶段
FROM node:16-alpine as build

# 复制构建好的 Vite 项目
COPY dist /app

# 第二阶段：使用nginx运行
FROM nginx:alpine

# 复制构建阶段的资源
COPY --from=build /app /usr/share/nginx/html

# 创建nginx配置文件目录
RUN mkdir -p /etc/nginx/templates

# 添加Nginx配置
COPY <<EOF /etc/nginx/templates/default.conf.template
server {
    listen 5000;
    
    location / {
        root /usr/share/nginx/html;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }
    
    location /api/ {
        proxy_pass http://\${API_HOST}:8080/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }
}
EOF

# 设置环境变量默认值（可以在docker-compose中覆盖）
ENV API_HOST=app
ENV NGINX_ENVSUBST_TEMPLATE_DIR=/etc/nginx/templates
ENV NGINX_ENVSUBST_OUTPUT_DIR=/etc/nginx/conf.d

# 开放端口
EXPOSE 5000

# 启动Nginx
CMD ["nginx", "-g", "daemon off;"]
