/*
 * @Author: ZXH
 * @Date: 2025-01-08 14:46:22
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2025-02-28 23:46:20
 * @Description: 
 */
import axios from 'axios';

export const BASE_URL = 'http://*********:8080';
// export const BASE_URL = window.location.origin;

const api = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('beacon_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // 检查是否是会话过期 (777) 或者认证错误 (401)
    if (error.response && (error.response.status === 777 || error.response.status === 401)) {
      // 清除本地存储的认证信息
      localStorage.removeItem('beacon_token');
      localStorage.removeItem('beacon_user');
      
      // 如果不是已经在会话过期页面，则重定向
      if (!window.location.pathname.includes('/session-expired') && 
          !window.location.pathname.includes('/login')) {
        window.location.href = '/session-expired';
      }
    }
    return Promise.reject(error);
  }
);

// 封装 GET 请求
export const get = async <T>(url: string, params?: any): Promise<T> => {
  const response = await api.get<T>(url, { params });
  return response.data;
};

// 封装文件下载请求
export const download = async (url: string, params?: any): Promise<{ data: Blob; filename?: string }> => {
  const response = await api.get(url, {
    params,
    responseType: 'blob',
    headers: {
      'Accept': 'text/csv',
    },
  });

  // Get filename from Content-Disposition header
  const contentDisposition = response.headers['content-disposition'];
  let filename;
  if (contentDisposition) {
    const filenameMatch = contentDisposition.match(/filename="?([^"]+)"?/);
    if (filenameMatch && filenameMatch[1]) {
      filename = filenameMatch[1];
    }
  }

  return {
    data: response.data,
    filename,
  };
};

// 封装 POST 请求
export const post = async <T>(url: string, data?: any): Promise<T> => {
  const response = await api.post<T>(url, data);
  return response.data;
};

// 封装 PUT 请求
export const put = async <T>(url: string, data?: any): Promise<T> => {
  const response = await api.put<T>(url, data);
  return response.data;
};

// 封装 DELETE 请求
export const del = async <T>(url: string): Promise<T> => {
  const response = await api.delete<T>(url);
  return response.data;
};

export const NewWebSocket = (url: string) => {
  // 根据当前页面协议动态选择 WebSocket 协议
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  const host = BASE_URL.replace(/^https?:\/\//, ''); // 移除http://或https://前缀
  return new WebSocket(`${protocol}//${host}${url}`);
};
