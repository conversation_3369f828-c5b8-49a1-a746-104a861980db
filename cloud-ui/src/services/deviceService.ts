import { get, post, del } from '../api/api';

// Device basic interfaces
export interface Device {
    id: string;
    name: string;
    device_sn: string;
    type: string;
    imei_code: string;
    enterprise_id: string;
    operators: string[];
    topics: string[];
    created_at: string;
    registered_at: string;
    registered_by: string;
    longitude: number;
    latitude: number;
    last_location_update: string;
}

export interface DeviceResponse {
    devices: Device[];
    total?: number;
    page?: number;
}

export interface DeviceListParams {
    page?: number;
    page_size?: number;
    mode?: string;
}

export interface DeviceRegisterParams {
    device_sn: string;
    name: string;
    type?: string;
    imei_code?: string;
}

export interface DeviceImportParams {
    name: string;
    device_sn: string;
    type: string;
    imei_code: string;
}

export interface DeviceUpdateParams {
    device_sn: string;
    name?: string;
    // Can add other optional fields here as needed
}

export interface ApiResponse {
    message?: string;
    error?: string;
}

export interface DeviceTracksResponse {
    [deviceSN: string]: Array<{
        latitude: number;
        longitude: number;
        ts: string;
    }>;
}

export interface DeviceType {
    id: string;
    name: string;
    created_at: string;
}

export interface DeviceTypeResponse {
    types: DeviceType[];
}

// Device API functions
/**
 * Get device list with optional pagination
 */
export const getDevices = async (params: DeviceListParams): Promise<DeviceResponse> => {
    const queryParams = new URLSearchParams();
    
    if (params.page) {
        queryParams.append('page', params.page.toString());
    }
    
    if (params.page_size) {
        queryParams.append('page_size', params.page_size.toString());
    }
    
    if (params.mode) {
        queryParams.append('mode', params.mode);
    }

    return get<DeviceResponse>(`/api/device/list?${queryParams.toString()}`);
};

/**
 * Get paginated device list
 */
export const getDevicesPaginated = async (params: DeviceListParams): Promise<DeviceResponse> => {
    const queryParams = new URLSearchParams();
    
    if (params.page) {
        queryParams.append('page', params.page.toString());
    }
    
    if (params.page_size) {
        queryParams.append('page_size', params.page_size.toString());
    }
    
    return get<DeviceResponse>(`/api/device/list?${queryParams.toString()}`);
};

/**
 * Register a new device
 */
export const registerDevice = async (params: DeviceRegisterParams): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/device/register', params);
};

/**
 * Import a device
 */
export const importDevice = async (params: DeviceImportParams): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/device/import', params);
};

/**
 * Delete a device
 */
export const deleteDevice = async (deviceSN: string): Promise<ApiResponse> => {
    return del<ApiResponse>(`/api/device/${deviceSN}`);
};

/**
 * Update device information
 */
export const updateDevice = async (params: DeviceUpdateParams): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/device/update', params);
};

/**
 * Get device tracks
 */
export const getDeviceTracks = async (deviceSN: string): Promise<DeviceTracksResponse> => {
    return get<DeviceTracksResponse>(`/api/device/tracks?device_sn=${deviceSN.toLowerCase()}`);
};

/**
 * Get device types
 */
export const getDeviceTypes = async (): Promise<DeviceTypeResponse> => {
    return get<DeviceTypeResponse>('/api/types');
};

/**
 * Add a new device type
 */
export const addDeviceType = async (name: string): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/types', { name });
};

/**
 * Delete a device type
 */
export const deleteDeviceType = async (typeName: string): Promise<ApiResponse> => {
    return del<ApiResponse>(`/api/types/${typeName}`);
};

/**
 * Add device (for CreateDevice.tsx)
 */
export interface AddDeviceFormData {
    device_sn: string;
    type: string;
    imei_code: string;
}

export const addDevice = async (formData: AddDeviceFormData): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/device/add', formData);
}; 