import { get, post, del } from '../api/api';

// Operator interfaces
export interface Operator {
    id: string;
    name: string;
    username: string;
    email: string;
    enterprise_id: string;
    created_at: string;
}

export interface OperatorResponse {
    operators: Operator[];
}

export interface CreateOperatorParams {
    name: string;
    username: string;
    password: string;
    email: string;
}

export interface DeviceOperatorParams {
    device_sn: string;
    operator_id: string[] | string;
}

export interface ApiResponse {
    message?: string;
    error?: string;
}

// Operator API functions
/**
 * Get operators list for the enterprise
 */
export const getOperators = async (): Promise<OperatorResponse> => {
    return get<OperatorResponse>("/api/enterprise/operators");
};

/**
 * Create a new operator
 */
export const createOperator = async (params: CreateOperatorParams): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/enterprise/operator', params);
};

/**
 * Delete an operator
 */
export const deleteOperator = async (operatorId: string, enterpriseId: string): Promise<ApiResponse> => {
    return del<ApiResponse>(`/api/enterprise/operator/${operatorId}/${enterpriseId}`);
};

/**
 * Assign operators to a device
 */
export const assignOperators = async (params: DeviceOperatorParams): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/device/assign', {
        device_sn: params.device_sn,
        operator_id: params.operator_id
    });
};

/**
 * Unassign an operator from a device
 */
export const unassignOperator = async (params: DeviceOperatorParams): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/device/unassign', {
        device_sn: params.device_sn,
        operator_id: params.operator_id
    });
}; 