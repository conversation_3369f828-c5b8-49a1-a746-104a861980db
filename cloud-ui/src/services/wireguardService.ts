import { get, post, del } from '../api/api';

// 设备接口
export interface Device {
  ID: string;
  Name: string;
  EnterpriseID: string;
  IP: string;
  CreatedAt: string;
  ListenPort: number;
  Endpoint: string;
  PublicKey: string;
  PrivateKey: string;
  Status?: 'running' | 'stopped';
}

// 带宽状态的设备接口
export interface DeviceWithBandwidth extends Device {
  totalReceived: number;
  totalSent: number;
  totalBandwidth: number;
  peersCount: number;
  status: 'running' | 'stopped';
  device_name: string;
  enterprise_id: string;
  listen_port: number;
  created_at: string;
}

// 对等点接口
export interface Peer {
  ID: string;
  Name: string;
  EnterpriseID: string;
  DNS: string;
  PrivateKey: string;
  PublicKey: string;
  AllowedIPs: string[];
  IP: string;
  Endpoint?: string;
  CreatedAt: string;
  PresharedKey: string;
}

// 对等点状态接口
export interface PeerStatus {
  PublicKey: string;
  LastHandshake: string;
  ReceiveBytes: number;
  TransmitBytes: number;
}

// 对等点响应接口
export interface PeerResponse {
  message: string;
  peer: Peer;
  config: string;
}

// 设备列表响应接口
export interface DeviceListResponse {
  data: Device[];
  total: number;
}

// 带宽限制接口
export interface RateLimitInfo {
  limit: number; // kbps
}

// 流量数据接口
export interface TrafficData {
  ts: string;
  rx_bytes: number;
  tx_bytes: number;
}

// 流量速率接口
export interface TrafficRate {
  timestamp: string;
  rxRate: number;  // bytes per second
  txRate: number;  // bytes per second
}

/**
 * 获取设备列表
 * @param page 页码
 * @param limit 每页数量
 * @returns 设备列表和总数
 */
export const listDevices = async (
  page: number, 
  limit: number
): Promise<DeviceListResponse> => {
  const response = await get<DeviceListResponse>('/api/wg/listdevices', {
    len: limit,
    page,
  });
  return response || { data: [], total: 0 };
};

/**
 * 创建新设备
 * @param name 设备名称
 * @param enterpriseId 企业ID
 * @returns 创建结果
 */
export const createDevice = async (
  name: string, 
  enterpriseId: string
): Promise<any> => {
  return await post('/api/wg/newdevice', {
    name,
    enterprise_id: enterpriseId
  });
};

/**
 * 删除设备
 * @param deviceName 设备名称
 * @param enterpriseId 企业ID
 * @param deviceId 设备ID
 * @returns 删除结果
 */
export const deleteDevice = async (
  deviceName: string, 
  enterpriseId: string, 
  deviceId: string
): Promise<any> => {
  return await post('/api/wg/device', {
    name: deviceName,
    enterprise_id: enterpriseId,
    device_id: deviceId
  });
};

/**
 * 启动设备接口
 * @param deviceName 设备名称
 * @returns 启动结果
 */
export const startDevice = async (deviceName: string): Promise<any> => {
  return await post(`/api/wg/start?device_name=${deviceName}`);
};

/**
 * 停止设备接口
 * @param deviceName 设备名称
 * @returns 停止结果
 */
export const stopDevice = async (deviceName: string): Promise<any> => {
  return await post(`/api/wg/stop?device_name=${deviceName}`);
};

/**
 * 获取带宽限制信息
 * @param deviceName 设备名称
 * @returns 带宽限制信息
 */
export const getBandwidthLimit = async (
  deviceName: string
): Promise<RateLimitInfo> => {
  return await get<RateLimitInfo>('/api/wg/bandwidth/limit', {
    device_name: deviceName
  });
};

/**
 * 设置带宽限制
 * @param deviceName 设备名称
 * @param limit 带宽限制值 (kbps)
 * @returns 设置结果
 */
export const setBandwidthLimit = async (
  deviceName: string, 
  limit: number
): Promise<any> => {
  return await post('/api/wg/bandwidth/limit', {
    device_name: deviceName,
    limit,
  });
};

/**
 * 删除带宽限制
 * @param deviceName 设备名称
 * @returns 删除结果
 */
export const removeBandwidthLimit = async (
  deviceName: string
): Promise<any> => {
  return await del('/api/wg/bandwidth/limit?device_name=' + deviceName);
};

/**
 * 获取设备和对等点信息
 * @param enterpriseId 企业ID
 * @returns 设备和对等点信息
 */
export const getDeviceAndPeers = async (
  enterpriseId: string
): Promise<{ data: Device; peers: Peer[] }> => {
  return await get<{ data: Device; peers: Peer[] }>('/api/wg/device', {
    enterprise_id: enterpriseId
  });
};

/**
 * 批量获取设备状态
 * @param devices 设备查询参数数组，包含name和enterprise_id
 * @returns 设备带宽状态数组
 */
export const getDevicesStatus = async (
  devices: { name: string; enterprise_id: string }[]
): Promise<{ data: DeviceWithBandwidth[] }> => {
  return await post<{ data: DeviceWithBandwidth[] }>('/api/wg/devices', {
    devices
  });
};

/**
 * 获取流量数据
 * @param deviceName 设备名称
 * @param startTime 可选的开始时间
 * @param endTime 可选的结束时间
 * @returns 流量数据
 */
export const getTrafficData = async (
  deviceName: string,
  startTime?: string,
  endTime?: string
): Promise<{ data: TrafficData[] }> => {
  const params: Record<string, any> = { device_name: deviceName };
  
  if (startTime) params.start_time = startTime;
  if (endTime) params.end_time = endTime;
  
  return await get<{ data: TrafficData[] }>('/api/wg/traffic', params);
};

/**
 * 获取对等点状态
 * @param deviceName 设备名称
 * @returns 对等点状态数组
 */
export const getPeersStatus = async (
  deviceName: string
): Promise<{ data: PeerStatus[] }> => {
  return await get<{ data: PeerStatus[] }>('/api/wg/status', {
    device_name: deviceName
  });
};

/**
 * 创建新对等点
 * @param name 对等点名称
 * @param enterpriseId 企业ID
 * @param deviceName 设备名称
 * @returns 创建结果和配置
 */
export const createPeer = async (
  name: string,
  enterpriseId: string,
  deviceName: string
): Promise<PeerResponse> => {
  return await post<PeerResponse>('/api/wg/newpeer', {
    name,
    enterprise_id: enterpriseId,
    device_name: deviceName
  });
};

/**
 * 删除对等点
 * @param peerId 对等点ID
 * @param deviceName 设备名称
 * @param enterpriseId 企业ID
 * @returns 删除结果
 */
export const deletePeer = async (
  peerId: string,
  deviceName: string,
  enterpriseId: string
): Promise<any> => {
  return await post('/api/wg/peer', {
    device_name: deviceName,
    enterprise_id: enterpriseId,
    peer_id: peerId
  });
};

/**
 * 获取对等点二维码
 * @param peerId 对等点ID
 * @param enterpriseId 企业ID
 * @returns 二维码SVG
 */
export const getPeerQRCode = async (
  peerId: string,
  enterpriseId: string
): Promise<{ svg: string }> => {
  return await get<{ svg: string }>('/api/wg/qrcode', {
    enterprise_id: enterpriseId,
    peer_id: peerId
  });
};

/**
 * 格式化字节数
 * 将字节数转换为人类可读的格式
 * @param bytes 字节数
 * @returns 格式化的字符串
 */
export const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
};

/**
 * 获取对等点的允许IP列表
 * @param deviceName 设备名称
 * @param peerId 对等点ID
 * @returns 允许的IP地址数组
 */
export const getPeerAllowedIPs = async (
  deviceName: string,
  peerId: string
): Promise<string[]> => {
  const response = await get<{ allowed_ips: string[] }>('/api/wg/peer/allowed-ips', {
    device_name: deviceName,
    peer_id: peerId
  });
  return response.allowed_ips || [];
};

/**
 * 保存对等点的允许IP列表
 * @param deviceName 设备名称
 * @param enterpriseId 企业ID
 * @param peerId 对等点ID
 * @param allowedIPs 允许的IP地址数组
 * @returns 保存结果
 */
export const savePeerAllowedIPs = async (
  deviceName: string,
  enterpriseId: string,
  peerId: string,
  allowedIPs: string[]
): Promise<any> => {
  return await post('/api/wg/peer/allowed-ips', {
    device_name: deviceName,
    enterprise_id: enterpriseId,
    peer_id: peerId,
    allowed_ips: allowedIPs
  });
}; 