/**
 * SCADA Service - API client for SCADA configuration system
 */

import { get, post, del } from '../api/api';

/**
 * Converts API widget data format to frontend format.
 * Handles deeply nested arrays and inconsistent key-value pairs.
 */
const convertWidgetDataForFrontend = (widgetArray: any[]): any[] => {
  if (!Array.isArray(widgetArray)) {
    console.warn("Expected widgets to be array, got:", typeof widgetArray);
    return [];
  }
  
  // Result widget array
  const result: any[] = [];
  
  try {
    // Process each widget (could be deeply nested)
    widgetArray.forEach(widget => {
      // Skip invalid data
      if (!Array.isArray(widget)) return;
      
      // Create new widget object
      const widgetObj: any = {};
      
      // First level of nesting
      widget.forEach(attributePair => {
        // Handle different formats of data
        if (Array.isArray(attributePair)) {
          // This is a pair of key-value objects like [{Key:"Key",Value:"id"}, {Key:"Value",Value:"widget-123"}]
          if (attributePair.length === 2 && 
              attributePair[0] && attributePair[1] && 
              typeof attributePair[0] === 'object' && 
              typeof attributePair[1] === 'object') {
            
            // Find which element contains the key and which contains the value
            let keyObj, valueObj;
            if (attributePair[0].Key === "Key" && attributePair[1].Key === "Value") {
              keyObj = attributePair[0];
              valueObj = attributePair[1];
            } else if (attributePair[1].Key === "Key" && attributePair[0].Key === "Value") {
              keyObj = attributePair[1];
              valueObj = attributePair[0];
            } else {
              // Standard key-value objects like {Key: "id", Value: "widget-123"}
              if (attributePair[0] && attributePair[0].Key && attributePair[0].Value !== undefined) {
                const key = attributePair[0].Key;
                const value = attributePair[0].Value;
                
                // Special handling for properties
                if (key === "properties" && Array.isArray(value)) {
                  widgetObj[key] = processProperties(value);
                } else {
                  widgetObj[key] = value;
                }
              }
              return;
            }
            
            // Extract actual key and value
            const key = keyObj.Value;
            const value = valueObj.Value;
            
            // Handle properties specially
            if (key === "properties" && Array.isArray(value)) {
              widgetObj[key] = processProperties(value);
            } else {
              widgetObj[key] = value;
            }
          } 
          // Simple key-value format like {Key: "id", Value: "widget-123"}
          else if (attributePair.length === 1 && attributePair[0] && 
                  typeof attributePair[0] === 'object' && 
                  attributePair[0].Key && attributePair[0].Value !== undefined) {
            
            const key = attributePair[0].Key;
            const value = attributePair[0].Value;
            
            // Handle properties specially
            if (key === "properties" && Array.isArray(value)) {
              widgetObj[key] = processProperties(value);
            } else {
              widgetObj[key] = value;
            }
          }
        } 
        // Direct key-value object like {Key: "id", Value: "widget-123"}
        else if (attributePair && typeof attributePair === 'object' && 
                attributePair.Key && attributePair.Value !== undefined) {
          
          const key = attributePair.Key;
          const value = attributePair.Value;
          
          // Handle properties specially
          if (key === "properties" && Array.isArray(value)) {
            widgetObj[key] = processProperties(value);
          } else {
            widgetObj[key] = value;
          }
        }
      });
      
      // Only add widget if it has required fields
      if (widgetObj.id && widgetObj.type) {
        result.push(widgetObj);
      } else {
        console.warn("Skipping incomplete widget:", widgetObj);
      }
    });
  } catch (error) {
    console.error("Error converting widget data:", error);
  }
  
  return result;
};

/**
 * Process properties field from API format to frontend object
 */
function processProperties(propsArray: any[]): any {
  const props: any = {};
  
  if (!Array.isArray(propsArray)) return props;
  
  propsArray.forEach(propPair => {
    if (Array.isArray(propPair)) {
      // Handle nested key-value pairs
      if (propPair.length === 2 && 
          propPair[0] && propPair[1] && 
          typeof propPair[0] === 'object' && 
          typeof propPair[1] === 'object') {
        
        // Find which element contains the key and which contains the value
        let keyObj, valueObj;
        if (propPair[0].Key === "Key" && propPair[1].Key === "Value") {
          keyObj = propPair[0];
          valueObj = propPair[1];
        } else if (propPair[1].Key === "Key" && propPair[0].Key === "Value") {
          keyObj = propPair[1];
          valueObj = propPair[0];
        } else {
          return;
        }
        
        // Add property to result
        if (keyObj && valueObj) {
          props[keyObj.Value] = valueObj.Value;
        }
      }
    }
    // Simple key-value format
    else if (propPair && typeof propPair === 'object' && 
            propPair.Key && propPair.Value !== undefined) {
      props[propPair.Key] = propPair.Value;
    }
  });
  
  return props;
}

/**
 * Converts frontend widget data format to API format
 */
const convertWidgetDataForAPI = (widgets: any[]): any[] => {
  if (!Array.isArray(widgets)) {
    console.warn("Expected widgets array, got:", typeof widgets);
    return [];
  }
  
  // Simple, clean format for API
  return widgets.map(widget => {
    // Standard fields as direct key-value pairs
    const result: any = {};
    
    Object.entries(widget).forEach(([key, value]) => {
      // Special handling for properties object
      if (key === 'properties' && typeof value === 'object') {
        const properties: any = {};
        
        // Convert properties to simple object
        Object.entries(value as object).forEach(([propKey, propValue]) => {
          properties[propKey] = propValue;
        });
        
        result[key] = properties;
      } else {
        // All other fields
        result[key] = value;
      }
    });
    
    return result;
  });
};

// SCADA configuration data interface
export interface SCADAConfig {
  projectId: string;
  name: string;
  description?: string;
  widgets: any[];
  createdAt?: string;
  updatedAt?: string;
  createdBy?: string;
}

/**
 * Save SCADA project to API
 */
export const saveSCADAProject = async (project: SCADAConfig): Promise<SCADAConfig> => {
  try {
    console.debug("Saving project with widgets:", project.widgets);
    
    // Convert widgets to clean format for API
    const dataToSave = {
      ...project,
      widgets: convertWidgetDataForAPI(project.widgets)
    };
    
    console.debug("Sending data to API:", dataToSave);
    
    let savedProject;

    savedProject = await post<SCADAConfig>('/api/scada/projects', dataToSave);

    
    // Ensure widgets are correctly formatted for frontend
    return {
      ...savedProject,
      widgets: Array.isArray(savedProject.widgets) ? 
        convertWidgetDataForFrontend(savedProject.widgets) : []
    };
  } catch (error) {
    console.error('Failed to save SCADA project:', error);
    throw error;
  }
};

/**
 * Load SCADA project from API
 */
export const loadSCADAProject = async (projectId: string): Promise<SCADAConfig> => {
  try {
    const data = await get<SCADAConfig>(`/api/scada/projects/${projectId}`);
    
    console.debug("Received project data from API:", data);
    
    // Convert widgets to frontend format
    const convertedData = {
      ...data,
      widgets: Array.isArray(data.widgets) ? 
        convertWidgetDataForFrontend(data.widgets) : []
    };
    
    console.debug("Converted widgets for frontend:", convertedData.widgets);
    return convertedData;
  } catch (error) {
    console.error('Failed to load SCADA project:', error);
    throw error;
  }
};

/**
 * List all SCADA projects
 */
export const listSCADAProjects = async (): Promise<SCADAConfig[]> => {
  try {
    const projects = await get<SCADAConfig[]>('/api/scada/projects');
    
    // Convert widgets in each project
    return projects.map((project: SCADAConfig) => ({
      ...project,
      widgets: Array.isArray(project.widgets) ? 
        convertWidgetDataForFrontend(project.widgets) : []
    }));
  } catch (error) {
    console.error('Failed to load SCADA projects:', error);
    throw error;
  }
};

/**
 * Delete SCADA project
 */
export const deleteSCADAProject = async (projectId: string): Promise<void> => {
  try {
    await del<void>(`/api/scada/projects/${projectId}`);
  } catch (error) {
    console.error('Failed to delete SCADA project:', error);
    throw error;
  }
};

/**
 * 获取可用的 MQTT 主题列表
 */
export interface MQTTTopic {
  id: string;
  topic: string;
  last_updated?: string;
  subscribers_count?: number;
  created_by?: string;
  enterprise_id?: string;
  device_sn?: string;
  created_at?: string;
  data_type: string;
}

export const getAvailableTopics = async (): Promise<MQTTTopic[]> => {
  try {
    const response = await get<{ topics: MQTTTopic[] }>('/api/mqtt/topics/available');
    return response.topics || [];
  } catch (error) {
    console.error('获取可用主题失败:', error);
    return [];
  }
};

/**
 * 获取SCADA项目数据
 */
export const getSCADAProject = async (projectId: string): Promise<SCADAConfig> => {
  try {
    const data = await get<SCADAConfig>(`/api/scada/projects/${projectId}`);
    
    // 转换widgets为前端格式
    const convertedData = {
      ...data,
      widgets: Array.isArray(data.widgets) ? 
        convertWidgetDataForFrontend(data.widgets) : []
    };
    
    return convertedData;
  } catch (error) {
    console.error('获取SCADA项目失败:', error);
    throw error;
  }
}; 