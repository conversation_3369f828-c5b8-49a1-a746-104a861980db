import { useState, useEffect } from 'react';
import { post } from '../api/api';
import { useMediaQuery } from '@mui/material';

/**
 * 验证密码复杂度
 * @param password 待验证的密码
 * @returns 密码是否符合复杂度要求
 */
export function validatePasswordComplexity(password: string): boolean {
  const lengthValid = password.length >= 8 && password.length <= 32;
  const hasLetters = /[a-zA-Z]/.test(password);
  const hasDigits = /\d/.test(password);
  const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(password);

  const typesCount = [hasLetters, hasDigits, hasSpecialChars].filter(Boolean).length;

  return lengthValid && typesCount >= 2;
}

/**
 * 打开侧边栏
 */
export function openSidebar() {
  if (typeof window !== 'undefined') {
    document.body.style.overflow = 'hidden';
    document.documentElement.style.setProperty('--SideNavigation-slideIn', '1');
  }
}

/**
 * 关闭侧边栏
 */
export function closeSidebar() {
  if (typeof window !== 'undefined') {
    document.documentElement.style.removeProperty('--SideNavigation-slideIn');
    document.body.style.removeProperty('overflow');
  }
}

/**
 * 切换侧边栏状态
 */
export function toggleSidebar() {
  if (typeof window !== 'undefined' && typeof document !== 'undefined') {
    const slideIn = window
      .getComputedStyle(document.documentElement)
      .getPropertyValue('--SideNavigation-slideIn');
    
    // 切换侧边栏状态
    slideIn ? closeSidebar() : openSidebar();
    
    // 触发事件供组件响应
    document.dispatchEvent(new CustomEvent('sidebarToggled', { 
      detail: { isOpen: !slideIn }
    }));
  }
}

/**
 * 倒计时Hook
 * @returns 倒计时状态和控制函数
 */
export const useCountdown = () => {
  const [countdown, setCountdown] = useState(0);

  // 从 localStorage 中获取倒计时结束时间
  const getCountdownEndTime = () => {
    const storedTime = localStorage.getItem('countdownEndTime');
    return storedTime ? parseInt(storedTime, 10) : 0;
  };

  // 设置倒计时结束时间
  const setCountdownEndTime = (time: number) => {
    localStorage.setItem('countdownEndTime', time.toString());
  };

  // 启动倒计时
  const startCountdown = (endTime: number) => {
    const interval = setInterval(() => {
      const remainingTime = Math.max(0, Math.floor((endTime - Date.now()) / 1000));
      setCountdown(remainingTime);

      if (remainingTime === 0) {
        clearInterval(interval);
        localStorage.removeItem('countdownEndTime'); // 清除倒计时
      }
    }, 1000);
  };

  // 组件加载时检查倒计时
  useEffect(() => {
    const endTime = getCountdownEndTime();
    if (endTime > Date.now()) {
      setCountdown(Math.floor((endTime - Date.now()) / 1000)); // 设置初始倒计时
      startCountdown(endTime); // 启动倒计时
    }
  }, []);

  return { countdown, setCountdown, setCountdownEndTime, startCountdown };
};

/**
 * 发送验证码
 * @param email 电子邮箱
 * @param endpoint API端点
 * @returns API响应
 */
export const sendVerificationCode = async (email: string, endpoint: string) => {
  try {
    const response = await post(endpoint + '?email=' + email, {});
    return response;
  } catch (error) {
    throw error;
  }
};

/**
 * 根据屏幕尺寸计算输入框宽度的Hook
 * @param options 不同屏幕尺寸对应的宽度选项
 * @returns 根据当前屏幕尺寸计算的宽度
 */
export const useComputeInputWidth = ({
  smallScreenWidth = "80vw",
  mediumScreenWidth = "60vw",
  largeScreenWidth = "400px",
} = {}) => {
  const isSmallScreen = useMediaQuery('(max-width: 600px)');
  const isMediumScreen = useMediaQuery('(max-width: 900px)');

  // 根据屏幕尺寸返回对应的宽度
  if (isSmallScreen) {
    return smallScreenWidth;
  } else if (isMediumScreen) {
    return mediumScreenWidth;
  } else {
    return largeScreenWidth;
  }
}; 