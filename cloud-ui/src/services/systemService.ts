import { get } from '../api/api';

// 原始系统信息接口（后端返回的格式）
export interface RawSystemInfo {
  hostname: string;
  kernel: string;
  uptime: string;
  current_time: string;
  cpu: number;
  memory: string; // 格式: "used/total"
  disk: string;   // 格式: "used/total" 
  net: string;    // 格式: "bytesSent/bytesReceived"
  interfaceList?: string[];
}

// 结构化的系统信息
export interface SystemInfo {
  basic: {
    hostname: string;
    kernel: string;
    uptime: string;
    current_time: string;
  };
  cpu: number;
  memory: {
    total: number;
    used: number;
    available: number;
    used_percent: number;
  };
  disk: {
    total: number;
    used: number;
    free: number;
    used_percent: number;
  };
  net: {
    bytes_recv: number;
    bytes_sent: number;
  };
  interfaceList?: string[];
}

// 网络数据点接口
export interface NetworkDataPoint {
  time: Date;
  recv: number;
  sent: number;
}

// 网络状态接口
export interface NetworkState {
  bytes_recv: number;
  bytes_sent: number;
  timestamp: number;
}

/**
 * 获取系统信息
 * @param params 请求参数
 * @returns 系统信息
 */
export const getSystemInfo = async (params: { init?: string; i?: string } = {}): Promise<SystemInfo> => {
  // 使用RawSystemInfo类型获取原始响应数据
  const rawResponse = await get<RawSystemInfo>("/api/sys/info", params);

  // 解析memory格式 "used/total"
  const memParts = rawResponse.memory.split('/');
  const usedMemory = parseInt(memParts[0], 10) || 0;
  const totalMemory = parseInt(memParts[1], 10) || 0;
  const availableMemory = totalMemory - usedMemory;

  // 解析disk格式 "used/total"
  const diskParts = rawResponse.disk.split('/');
  const usedDisk = parseInt(diskParts[0], 10) || 0;
  const totalDisk = parseInt(diskParts[1], 10) || 0;
  const freeDisk = totalDisk - usedDisk;

  // 解析net格式 "bytesSent/bytesReceived"
  const netParts = rawResponse.net.split('/');
  const netSent = parseInt(netParts[0], 10) || 0;
  const netRecv = parseInt(netParts[1], 10) || 0;

  // 构造符合前端SystemInfo接口的响应数据
  const response: SystemInfo = {
    basic: {
      hostname: rawResponse.hostname,
      kernel: rawResponse.kernel,
      uptime: rawResponse.uptime,
      current_time: rawResponse.current_time
    },
    cpu: rawResponse.cpu,
    memory: {
      total: totalMemory,
      used: usedMemory,
      available: availableMemory,
      used_percent: totalMemory > 0 ? (usedMemory / totalMemory) * 100 : 0,
    },
    disk: {
      total: totalDisk,
      used: usedDisk,
      free: freeDisk,
      used_percent: totalDisk > 0 ? (usedDisk / totalDisk) * 100 : 0,
    },
    net: {
      bytes_sent: netSent,
      bytes_recv: netRecv,
    },
    interfaceList: rawResponse.interfaceList,
  };

  return response;
};

/**
 * 格式化网络速度
 * @param speed 速度值 (B/s)
 * @returns 格式化后的字符串
 */
export const formatNetworkSpeed = (speed: number): string => {
  if (speed < 1024) return `${Math.round(speed)} B/s`;
  if (speed < 1024 * 1024) return `${(speed / 1024).toFixed(2)} KB/s`;
  return `${(speed / 1024 / 1024).toFixed(2)} MB/s`;
};

/**
 * 格式化存储大小
 * @param bytes 字节数
 * @returns 格式化后的字符串
 */
export const formatStorage = (bytes: number): string => {
  if (bytes < 1024) return `${bytes} KB`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(2)} MB`;
  return `${(bytes / 1024 / 1024).toFixed(2)} GB`;
}; 