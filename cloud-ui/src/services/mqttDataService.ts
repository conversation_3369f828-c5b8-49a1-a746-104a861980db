import { get, post, download, NewWebSocket } from '../api/api';
// 消息接口
export interface TopicMessage {
  topic: string;
  payload: string;
  time: string;
  is_alarm?: boolean;
  client_id?: string;
  qos?: number;
}

// 主题消息历史响应
export interface MessageHistoryResponse {
  messages: TopicMessage[];
}

// 分页消息响应
export interface MessageResponse {
  total: number;
  page: number;
  pageSize: number;
  data: TopicMessage[];
}

// 主题接口
export interface Topic {
  id: string;
  topic: string;
  last_updated: string;
  created_by: string;
  created_by_client_id: string;
  created_at: string;
  data_type: string;
  device_sn: string;
  device_name?: string;
}

// 基础设备接口
export interface BaseDevice {
  id: string;
  name: string;
  device_sn: string;
}

// 操作员可见的设备接口
export interface OperatorDevice extends BaseDevice {
  topics: Topic[];
}

// 管理员可见的设备接口
export interface AdminDevice extends BaseDevice {
  enterprise_id: string;
  registered_at: string;
  registered_by: string;
  operators: string[];
  topics: Topic[];
  created_at: string;
}

// 设备列表响应接口
export interface TopicsResponse {
  devices: (OperatorDevice | AdminDevice)[];
  page: number;
  total: number;
}

/**
 * 获取主题历史消息
 * @param topic 主题
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 主题历史消息
 */
export const getTopicHistory = async (
  topic: string,
  startTime: string | Date,
  endTime: string | Date
): Promise<TopicMessage[]> => {
  const startTimeStr = typeof startTime === 'string' ? startTime : startTime.toISOString();
  const endTimeStr = typeof endTime === 'string' ? endTime : endTime.toISOString();
  
  const response = await get<MessageHistoryResponse>(
    `/api/mqtt/topics/history?topic=${encodeURIComponent(topic)}&startTime=${startTimeStr}&endTime=${endTimeStr}`
  );
  
  return response?.messages || [];
};

/**
 * 获取主题消息列表（分页）
 * @param topic 主题
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 分页消息列表
 */
export const getTopicMessages = async (
  topic: string,
  page: number,
  pageSize: number
): Promise<MessageResponse> => {
  const url = `/api/mqtt/topics/messages?topic=${encodeURIComponent(topic)}&page=${page}&pageSize=${pageSize}`;
  const response = await get<MessageResponse>(url);
  return response || { total: 0, page: 1, pageSize: pageSize, data: [] };
};

/**
 * 导出主题历史数据为CSV
 * @param topic 主题
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 返回blob和可选的filename
 */
export const exportTopicHistory = async (
  topic: string,
  startTime: Date,
  endTime: Date
): Promise<{ data: Blob; filename?: string }> => {
  return await download(
    `/api/mqtt/topics/history?topic=${encodeURIComponent(topic)}&startTime=${startTime.toISOString()}&endTime=${endTime.toISOString()}&format=csv`
  );
};

/**
 * 发布消息到主题
 * @param topic 主题
 * @param payload 消息内容
 * @returns 发布结果
 */
export const publishMessage = async (
  topic: string,
  payload: string
): Promise<any> => {
  return await post('/api/mqtt/publish', { topic, payload });
};

/**
 * 获取带有主题信息的设备列表
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 设备列表
 */
export const getDevicesWithTopics = async (
  page: number,
  pageSize: number
): Promise<TopicsResponse> => {
  const response = await get<TopicsResponse>(
    `/api/device/list?mode=moreinfos&page=${page}&page_size=${pageSize}`
  );
  
  return response || { devices: [], page: 1, total: 0 };
};
/**
 * 创建新的WebSocket连接
 * @param onMessage 消息处理函数
 * @param onOpen 连接建立时的处理函数
 * @returns WebSocket实例和清理函数
 */
export const createMqttWebSocket = (
  onMessage?: (data: any) => void,
  onOpen?: (ws: WebSocket) => void
): { ws: WebSocket; cleanup: () => void } => {
  const wsUrl = `/api/mqtt/topics/ws?token=${localStorage.getItem('beacon_token')}`;
  const ws = NewWebSocket(wsUrl);
  
  // 保持连接活跃的ping
  const pingInterval = setInterval(() => {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify({ type: 'ping' }));
    }
  }, 30000);
  
  if (onMessage) {
    ws.addEventListener('message', (event) => {
      try {
        const data = JSON.parse(event.data);
        onMessage(data);
      } catch (error) {
        console.error('Error processing WebSocket message:', error);
      }
    });
  }
  
  if (onOpen) {
    ws.addEventListener('open', () => onOpen(ws));
  }
  
  // 清理函数
  const cleanup = () => {
    clearInterval(pingInterval);
    ws.close();
  };
  
  return { ws, cleanup };
};

/**
 * 根据数据类型格式化展示的值
 * @param value 原始值
 * @param type 数据类型
 * @returns 格式化后的值
 */
export const formatValue = (value: string, type: 'number' | 'boolean' | 'string') => {
  switch (type) {
    case 'number':
      return Number(value).toLocaleString();
    case 'boolean':
      return value === 'true' ? '✓ True' : '✗ False';
    default:
      return value;
  }
};

/**
 * 验证数据类型
 * @param type 类型字符串
 * @returns 有效的数据类型
 */
export const validateDataType = (type: string): 'number' | 'boolean' | 'string' => {
  if (type === 'number' || type === 'boolean' || type === 'string') {
    return type as 'number' | 'boolean' | 'string';
  }
  return 'string';
}; 