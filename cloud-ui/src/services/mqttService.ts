import { get } from '../api/api';

// MQTT服务器信息接口
export interface ServerInfo {
  version: string;
  started: number;
  time: number;
  uptime: number;
  bytes_received: number;
  bytes_sent: number;
  clients_connected: number;
  clients_disconnected: number;
  clients_maximum: number;
  clients_total: number;
  messages_received: number;
  messages_sent: number;
  messages_dropped: number;
  retained: number;
  inflight: number;
  inflight_dropped: number;
  subscriptions: number;
  packets_received: number;
  packets_sent: number;
  memory_alloc: number;
  threads: number;
  topics: number;
}

// 流量数据点接口
export interface TrafficDataPoint {
  time: Date;
  recv: number;
  sent: number;
}

// 服务器信息响应接口
export interface ServerInfoResponse {
  info: ServerInfo;
  topics: number;
}

/**
 * 获取MQTT服务器信息
 * @returns MQTT服务器信息
 */
export const getMqttServerInfo = async (): Promise<ServerInfoResponse> => {
  const response = await get<ServerInfoResponse>("/api/mqttinfo");
  return response;
};

/**
 * 格式化数据大小
 * @param bytes 字节数
 * @returns 格式化后的字符串
 */
export const formatBytes = (bytes: number): string => {
  if (bytes < 1024) return `${Math.round(bytes)} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / 1024 / 1024).toFixed(1)} MB`;
};

/**
 * 格式化运行时间
 * @param seconds 秒数
 * @returns 格式化后的字符串
 */
export const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / (24 * 60 * 60));
  const hours = Math.floor((seconds % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((seconds % (60 * 60)) / 60);
  return `${days}d ${hours}h ${minutes}m`;
};

/**
 * 格式化数字
 * @param num 数字
 * @returns 格式化后的字符串
 */
export const formatNumber = (num: number): string => {
  return new Intl.NumberFormat().format(num);
}; 