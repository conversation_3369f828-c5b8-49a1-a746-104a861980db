import { post } from '../api/api';

// 用户信息接口
export interface UserInfo {
  username: string;
  name: string;
  email: string;
  role: string;
  avatar: string;
  enterprise_id: string;
  token: string;
}

// 登录请求参数
export interface LoginRequest {
  username: string;
  password: string;
}

// 注册请求参数
export interface RegisterRequest {
  username: string;
  name: string;
  email: string;
  password: string;
}

/**
 * 用户登录
 * @param data 登录参数
 * @returns 用户信息
 */
export const login = async (data: LoginRequest): Promise<UserInfo> => {
  return post<UserInfo>('/api/login', data);
};

/**
 * 请求发送注册验证码
 * @param email 邮箱
 * @param username 用户名
 * @returns 响应消息
 */
export const sendRegisterCode = async (email: string, username: string): Promise<{ message: string }> => {
  return post<{ message: string }>('/api/registercode', { email, username });
};

/**
 * 用户注册
 * @param data 注册参数
 * @param code 验证码
 * @param enterpriseCode 企业代码（可选）
 * @returns 响应消息
 */
export const register = async (
  data: RegisterRequest,
  code: string,
  enterpriseCode?: string
): Promise<{ message: string }> => {
  const url = `/api/register?code=${code}${enterpriseCode ? `&enterprise_code=${enterpriseCode}` : ''}`;
  return post<{ message: string }>(url, data);
};

/**
 * 请求发送忘记密码验证码
 * @param email 邮箱
 * @returns 响应消息
 */
export const sendForgotPasswordCode = async (email: string): Promise<{ message: string }> => {
  return post<{ message: string }>('/api/forgotcode', { email });
};

/**
 * 重置密码
 * @param email 邮箱
 * @param code 验证码
 * @returns 响应消息
 */
export const resetPassword = async (email: string, code: string): Promise<{ message: string }> => {
  return post<{ message: string }>(`/api/resetpwd?email=${email}&code=${code}`, {});
};

/**
 * 保存用户信息到本地存储
 * @param userInfo 用户信息
 */
export const saveUserInfo = (userInfo: Omit<UserInfo, 'token'>): void => {
  localStorage.setItem('beacon_user', JSON.stringify(userInfo));
};

/**
 * 保存令牌到本地存储
 * @param token 令牌
 */
export const saveToken = (token: string): void => {
  localStorage.setItem('beacon_token', token);
};

/**
 * 清除认证信息
 */
export const clearAuth = (): void => {
  localStorage.removeItem('beacon_token');
  localStorage.removeItem('beacon_user');
};

/**
 * 获取当前存储的用户信息
 * @returns 用户信息或null
 */
export const getSavedUserInfo = (): Omit<UserInfo, 'token'> | null => {
  const userStr = localStorage.getItem('beacon_user');
  return userStr ? JSON.parse(userStr) : null;
};

/**
 * 获取当前存储的令牌
 * @returns 令牌或null
 */
export const getToken = (): string | null => {
  return localStorage.getItem('beacon_token');
};

/**
 * 检查用户是否已登录
 * @returns 是否已登录
 */
export const isAuthenticated = (): boolean => {
  return !!getToken();
}; 