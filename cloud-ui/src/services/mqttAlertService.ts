import { get, post, put, del, download, NewWebSocket } from '../api/api';

// 告警接口
export interface Alarm {
  time: string;
  topic: string;
  value: string;
  desc: string;
  alarm_type: string;
  alarm_contact: string;
  username: string;
}

// 告警列表响应接口
export interface AlarmResponse {
  total: number;
  alarms: Alarm[];
}

// 告警规则接口
export interface ThresholdRule {
  id: string;
  topic: string;
  expression: string;
  description: string;
  is_enabled: boolean;
  created_by: string;
  mode: 'email' | 'sms' | 'wechat';
  contact: string;
  created_at?: string;
}

// 主题接口
export interface Topic {
  device_sn: string;
  id: string;
  topic: string;
  data_type: string;
  description?: string;
}

// 创建/更新规则的请求数据
export interface ThresholdRuleData {
  topic_id: string;
  expression: string;
  description: string;
  is_enabled: boolean;
  mode: 'email' | 'sms' | 'wechat';
  contact: string;
}

/**
 * 获取告警记录列表
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 告警记录列表及总数
 */
export const getAlarms = async (
  page: number,
  pageSize: number
): Promise<AlarmResponse> => {
  const response = await get<AlarmResponse>('/api/mqtt/alarms', {
    page,
    pageSize,
  });
  return response || { total: 0, alarms: [] };
};

/**
 * 导出告警历史记录为CSV
 * @param startTime 开始时间
 * @param endTime 结束时间
 * @returns 返回blob和可选的filename
 */
export const exportAlarmHistory = async (
  startTime: Date,
  endTime: Date
): Promise<{ data: Blob; filename?: string }> => {
  return await download(
    `/api/mqtt/alarms/history?startTime=${startTime.toISOString()}&endTime=${endTime.toISOString()}&format=csv`
  );
};

/**
 * 获取所有告警规则
 * @returns 告警规则列表
 */
export const getThresholdRules = async (): Promise<ThresholdRule[]> => {
  const response = await get<{ rules: ThresholdRule[] }>('/api/mqtt/thresholds');
  return response.rules || [];
};

/**
 * 获取可用的主题列表
 * @returns 可用主题列表
 */
export const getAvailableTopics = async (): Promise<Topic[]> => {
  const response = await get<{ topics: Topic[] }>('/api/mqtt/topics/available');
  return response.topics || [];
};

/**
 * 创建告警规则
 * @param ruleData 规则数据
 * @returns 创建结果
 */
export const createThresholdRule = async (
  ruleData: ThresholdRuleData
): Promise<any> => {
  return await post('/api/mqtt/threshold', ruleData);
};

/**
 * 更新告警规则
 * @param ruleId 规则ID
 * @param ruleData 规则数据
 * @returns 更新结果
 */
export const updateThresholdRule = async (
  ruleId: string,
  ruleData: ThresholdRuleData
): Promise<any> => {
  return await put(`/api/mqtt/threshold/${ruleId}`, ruleData);
};

/**
 * 删除告警规则
 * @param ruleId 规则ID
 * @returns 删除结果
 */
export const deleteThresholdRule = async (
  ruleId: string
): Promise<any> => {
  return await del(`/api/mqtt/threshold/${ruleId}`);
};

/**
 * 获取微信二维码绑定链接
 * @param deviceId 设备ID
 * @returns 二维码图片数据
 */
export const getWechatQRCode = async (
  deviceId: string
): Promise<{ qrcode: string }> => {
  return await get<{ qrcode: string }>('/api/device/bind', {
    device_id: deviceId
  });
};

/**
 * 创建微信绑定WebSocket连接
 * @param deviceId 设备ID
 * @param callbacks 回调函数集合
 * @returns WebSocket实例和清理函数
 */
export interface WechatBindCallbacks {
  onConnect?: () => void;
  onSuccess?: (data: { nickname: string; openid: string }) => void;
  onTimeout?: () => void;
  onError?: (message?: string) => void;
  onClose?: () => void;
}

export const createWechatBindWebSocket = (
  deviceId: string,
  callbacks: WechatBindCallbacks
): { ws: WebSocket; cleanup: () => void } => {
  const ws = NewWebSocket(`/api/device/ws/bind-status?device_id=${deviceId}&token=${localStorage.getItem('beacon_token')}`);
  
  ws.onopen = () => {
    callbacks.onConnect?.();
  };
  
  ws.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      
      switch (data.status) {
        case 'connected':
          // 已连接到设备绑定WebSocket
          break;
        case 'heartbeat':
          // 收到心跳，连接仍然存活
          break;
        case 'timeout':
          callbacks.onTimeout?.();
          break;
        case 'error':
          callbacks.onError?.(data.message);
          break;
        case 'closing':
          // WebSocket连接关闭中
          break;
        case 'success':
          callbacks.onSuccess?.({
            nickname: data.nickname,
            openid: data.openid
          });
          break;
      }
    } catch (error) {
      console.error('WebSocket消息处理错误:', error);
      callbacks.onError?.('消息格式错误');
    }
  };
  
  ws.onerror = (error) => {
    console.error('WebSocket错误:', error);
    callbacks.onError?.();
  };
  
  ws.onclose = () => {
    callbacks.onClose?.();
  };
  
  // 清理函数
  const cleanup = () => {
    if (ws.readyState === WebSocket.OPEN || ws.readyState === WebSocket.CONNECTING) {
      ws.close();
    }
  };
  
  return { ws, cleanup };
}; 