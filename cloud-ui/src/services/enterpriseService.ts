import { get, post, del } from '../api/api';

// 企业代码接口
export interface EnterpriseCode {
  ID: string;
  Code: string;
  Used: boolean;
  CreatedAt: string;
  UsedAt: string | null;
}

// 分页数据接口
export interface PaginationData {
  current: number;
  pageSize: number;
  total: number;
}

// 企业代码响应接口
export interface EnterpriseCodes {
  codes: EnterpriseCode[];
  pagination: PaginationData;
}

/**
 * 获取企业代码列表
 * @param page 页码
 * @param pageSize 每页大小
 * @returns 企业代码列表和分页信息
 */
export const getEnterpriseCodes = async (
  page: number,
  pageSize: number
): Promise<EnterpriseCodes> => {
  const response = await get<EnterpriseCodes>(`/api/enterprise/codes?page=${page}&limit=${pageSize}`);

  // 确保返回有效结构
  return {
    codes: response.codes || [],
    pagination: response.pagination || {
      current: page,
      pageSize: pageSize,
      total: 0
    }
  };
};

/**
 * 生成新的企业代码
 * @returns 生成结果
 */
export const generateEnterpriseCode = async (): Promise<{ code: string; message: string }> => {
  return await post<{ code: string; message: string }>('/api/enterprise/code');
};

/**
 * 删除企业代码
 * @param code 要删除的代码
 * @returns 删除结果
 */
export const deleteEnterpriseCode = async (code: string): Promise<any> => {
  return await del(`/api/enterprise/code/${code}`);
}; 