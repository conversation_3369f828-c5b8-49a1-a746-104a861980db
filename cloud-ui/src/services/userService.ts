import { get, post, del } from '../api/api';

// 用户基本信息接口
export interface UserBasic {
  id: string;
  name: string;
  username: string;
  email: string;
  role: string;
  enterprise_id: string;
}

// 用户详细信息接口
export interface UserDetail {
  email: string;
  role: string;
  username: string;
  name?: string;
}

// 用户列表响应接口
export interface UserListResponse {
  total: number;
  users: UserBasic[];
}

// 更新密码请求接口
export interface UpdatePasswordRequest {
  old_password: string;
  new_password: string;
}

// 更新个人资料请求接口
export interface UpdateProfileRequest {
  name: string;
  email?: string;
}

// 更新头像响应接口
export interface UpdateAvatarResponse {
  message: string;
  avatar: string;
}

/**
 * 获取用户列表
 * @param page 页码（从1开始）
 * @param limit 每页数量
 * @returns 用户列表和总数
 */
export const getUsers = async (
  page: number,
  limit: number
): Promise<UserListResponse> => {
  const response = await get<UserListResponse>('/api/user/getusers', {
    page,
    limit,
  });
  return response || { total: 0, users: [] };
};

/**
 * 获取单个用户信息
 * @param userId 用户ID
 * @returns 用户详细信息
 */
export const getUserInfo = async (userId: string): Promise<UserDetail> => {
  const response = await get<UserDetail>(`/api/user/info/${userId}`);
  return response;
};

/**
 * 更新用户信息
 * @param userId 用户ID
 * @param userData 用户数据
 * @returns 更新结果
 */
export const updateUser = async (
  userId: string,
  userData: Partial<UserDetail>
): Promise<any> => {
  return await post(`/api/user/updateuser/${userId}`, userData);
};

/**
 * 删除用户
 * @param userId 用户ID
 * @returns 删除结果
 */
export const deleteUser = async (userId: string): Promise<any> => {
  return await del(`/api/user/${userId}`);
};

/**
 * 更新密码
 * @param passwordData 密码数据
 * @returns 更新结果
 */
export const updatePassword = async (
  passwordData: UpdatePasswordRequest
): Promise<any> => {
  return await post('/api/user/updatepwd', passwordData);
};

/**
 * 强制重置用户密码
 * @param email 用户邮箱
 * @returns 重置结果
 */
export const forceResetPassword = async (email: string): Promise<any> => {
  return await post(`/api/user/forceresetpwd/${email}`);
};

/**
 * 发送邮箱验证码
 * @param email 需要验证的邮箱
 * @returns 发送结果
 */
export const sendVerificationCode = async (email: string): Promise<any> => {
  return await post(`/api/user/updateprofile?mode=verify&email=${email}`, {});
};

/**
 * 更新个人资料
 * @param profileData 资料数据
 * @param verificationCode 可选的验证码（邮箱变更时需要）
 * @returns 更新结果
 */
export const updateProfile = async (
  profileData: UpdateProfileRequest,
  verificationCode?: string
): Promise<any> => {
  return await post(
    `/api/user/updateprofile${verificationCode ? `?code=${verificationCode}` : ''}`,
    profileData
  );
};

/**
 * 更新头像
 * @param avatarBase64 Base64编码的头像数据
 * @returns 更新结果，包含新头像URL
 */
export const updateAvatar = async (
  avatarBase64: string
): Promise<UpdateAvatarResponse> => {
  return await post<UpdateAvatarResponse>('/api/user/updateavatar', { 
    avatar: avatarBase64 
  });
}; 