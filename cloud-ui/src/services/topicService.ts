import { get, post, del } from '../api/api';

// Topic interfaces
export interface TopicInfo {
    _id: string;
    topic: string;
    last_updated: string;
    created_by: string;
    created_by_client_id: string;
    device_sn: string;
    created_at: string;
    data_type: string;
}

export interface TopicInfoResponse {
    topic: TopicInfo;
}

export interface TopicCreateParams {
    topic: string;
    data_type: string;
    device_sn: string;
    user_name: string;
    client_id: string;
}

export interface TopicDeleteParams {
    topic: string;
    device_sn: string;
}

export interface ApiResponse {
    message?: string;
    error?: string;
}

export interface DataType {
    value: string;
    label: string;
    color: 'primary' | 'secondary' | 'success' | 'warning' | 'default';
}

export interface MQTTTopic {
    id: string;
    topic: string;
    last_updated?: string;
    subscribers_count?: number;
    created_by?: string;
    enterprise_id?: string;
    device_sn?: string;
    created_at?: string;
    data_type: string;
}

// Available data types
export const DATA_TYPES: DataType[] = [
    { value: 'number', label: 'Number', color: 'primary' },
    { value: 'string', label: 'String', color: 'secondary' },
    { value: 'boolean', label: 'Boolean', color: 'success' },
    { value: 'json', label: 'JSON', color: 'warning' },
]; 

// Topic API functions
/**
 * Get topic information
 */
export const getTopicInfo = async (deviceSN: string, topic: string): Promise<TopicInfoResponse> => {
    return get<TopicInfoResponse>('/api/device/topicinfo', {
        device_sn: deviceSN,
        topic: topic
    });
};

/**
 * Create a new topic
 */
export const createTopic = async (params: TopicCreateParams): Promise<ApiResponse> => {
    return post<ApiResponse>('/api/mqtt/topics', params);
};

/**
 * Delete a topic
 */
export const deleteTopic = async (params: TopicDeleteParams): Promise<ApiResponse> => {
    return del<ApiResponse>(`/api/device/topic?device_sn=${encodeURIComponent(params.device_sn)}&topic=${encodeURIComponent(params.topic)}`);
}; 

/**
 * 获取所有可用的MQTT主题
 * @returns MQTT主题列表
 */
export const getAvailableTopics = async (): Promise<MQTTTopic[]> => {
    try {
        const response = await get<{ topics: MQTTTopic[] }>('/api/mqtt/topics/available');
        return response.topics || [];
    } catch (error) {
        console.error('获取可用主题失败:', error);
        return [];
    }
}; 