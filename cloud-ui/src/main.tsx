import { createRoot } from 'react-dom/client';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { SnackbarProvider } from './context/SnackbarContext';
import { GlobalDialogProvider } from './context/GlobalDialog';
import { ThemeProvider } from './component/theme/theme';
import { AvatarProvider } from './context/UserContext';
import { MqttDataProvider } from './context/MqttDataContext';
import ProtectedRoute from './context/ProtectedRoute';
import NotFound from './component/otherpage/Notfound';
import Dashboard from './component/dashboard/Home';
import { RouteConfig, useRoutes } from './config/routes';
import './i18n/i18n';

// 递归渲染路由配置
const renderRoutes = (routeConfig: any) => {
  if (routeConfig.items) {
    return routeConfig.items.map((item: any) => {
      if (item.items) {
        return renderRoutes(item);
      }
      return (
        <Route
          key={item.path}
          path={item.path}
          element={
            item.roles ? 
              <ProtectedRoute requiredRole={item.roles}>{item.element}</ProtectedRoute> :
              item.element
          }
        />
      );
    }).flat();
  }
  return (
    <Route
      key={routeConfig.path}
      path={routeConfig.path}
      element={
        routeConfig.roles ? 
          <ProtectedRoute requiredRole={routeConfig.roles}>{routeConfig.element}</ProtectedRoute> :
          routeConfig.element
      }
    />
  );
};

function App() {
  const routes = useRoutes();
  
  return (
    <ThemeProvider>
      <CssBaseline />
      <SnackbarProvider>
        <GlobalDialogProvider>
          <AvatarProvider>
            <MqttDataProvider>
              <Router>
                <Routes>
                  {/* 认证路由 */}
                  {routes.auth.map((route: RouteConfig) => (
                    <Route key={route.path} path={route.path} element={route.element} />
                  ))}

                  {routes.scada.map((route: RouteConfig) => (
                    <Route key= {route.path} path={route.path} element={route.element} />
                  ))}

                  {/* 仪表板路由 */}
                  <Route path="/dashboard" element={<ProtectedRoute><Dashboard /></ProtectedRoute>}>
                    <Route index element={<Navigate to="/dashboard/users/profile" replace />}/>
                    {routes.dashboard.map((route: RouteConfig) => renderRoutes(route))}
                  </Route>

                  {/* 404 路由 */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
              </Router>
            </MqttDataProvider>
          </AvatarProvider>
        </GlobalDialogProvider>
      </SnackbarProvider>
    </ThemeProvider>
  );
}

createRoot(document.getElementById('root')!).render(<App />);