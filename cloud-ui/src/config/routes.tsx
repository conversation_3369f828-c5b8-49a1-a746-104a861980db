import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import {
    HomeRounded, Podcasts, CastOutlined, Topic as TopicIcon,
    AccountCircleRounded, ManageAccountsRounded, BarChart,
    GppMaybe, AdminPanelSettings, Group, Router as RouterIcon,
    ManageAccounts,
    Computer,
    EventRepeat,
    MapOutlined
} from '@mui/icons-material';
import SignIn from '../component/Index';
import MyProfile from '../component/dashboard/users/MyProfile';
import WireguardManager from '../component/dashboard/wireguard/WireguardManager';
import SessionExpired from '../component/auth/SessionExpired';
import Unauthorized from '../component/otherpage/Unauthorized';
import ManagePage from '../component/dashboard/users/Manage';
import SystemInfoDashboard from '../component/dashboard/sysinfo';
import ServerInfo from "../component/dashboard/mqtt/ServerInfo";
import ManageDevices from '../component/dashboard/devices/ManageDevice';
import DataVisualization from '../component/dashboard/mqtt/dataview';
import MqttAlarms from '../component/dashboard/mqtt/threshold/AlertLog';
import DeviceAdmin from '../component/dashboard/devices/device-admin';
import ManageOperator from '../component/dashboard/devices/ManageOperator';
import DataViewDetail from '../component/dashboard/mqtt/dataview/detail';
import ManageAlert from '../component/dashboard/mqtt/threshold/manageAlert';
import { useTranslation } from 'react-i18next';
import WireguardPeers from '../component/dashboard/wireguard/WireguardPeers';
import DeviceMap from '../component/dashboard/devices/deviceMap';
import SCADA from '../component/SCADA';
import SCADAPreview from '../component/SCADA/components/SCADAPreview';

interface RouteConfig {
  id: string;
  path: string;
  label: string;
  element: ReactNode;
  icon?: ReactNode;
  roles?: string[];
  items?: RouteConfig[];
  showInMenu?: boolean;
}

// 创建一个hook来获取路由配置
export const useRoutes = () => {
  const { t } = useTranslation();

  const routes = {
    // 认证相关路由
    auth: [
      { id: 'login', path: '/login', label: 'Login', element: <SignIn /> },
      { id: 'session-expired', path: '/session-expired', label: 'Session Expired', element: <SessionExpired /> },
      { id: 'unauthorized', path: '/unauthorized', label: 'Unauthorized', element: <Unauthorized /> },
      { id: 'root', path: '/', label: 'Root', element: <Navigate to="/login" replace /> },
    ],
    scada: [
      {
        id: 'scada-preview',
        path: '/:projectId',
        label: 'SCADA Preview',
        element: <SCADAPreview />,
        roles: ['admin', 'superadmin', 'enterprise', 'operator'],
      },
    ],
    // 仪表板路由和菜单项
    dashboard: [
      {
        id: 'home',
        path: 'sysinfo',
        label: t('routes.home'),
        element: <SystemInfoDashboard />,
        icon: <HomeRounded />,
        roles: ['admin', 'superadmin'],
      },
      {
        id: 'wireguard-manager',
        path: 'wireguard-manager',
        label: t('routes.wireguard'),
        element: <WireguardManager />,
        icon: <Podcasts />,
        roles: ['admin', 'superadmin']
      },
      {
        id: 'wireguard-peers',
        path: 'wireguard-peers/:enterpriseId',
        label: 'WireguardPeers',
        element: <WireguardPeers />,
        icon: <Podcasts />,
        roles: ['admin', 'superadmin', 'enterprise'],
        // showInMenu: false
      },
      {
        id: 'devices',
        path: 'devices',
        label: t('routes.devices.title'),
        icon: <Computer />,
        element: null,
        items: [
          {
            id: 'device-admin',
            path: 'device/admin',
            label: t('routes.devices.deviceAdmin'),
            element: <DeviceAdmin />,
            icon: <AdminPanelSettings />,
            roles: ['superadmin', 'admin']
          },
          {
            id: 'mqtt-manage-devices',
            path: 'manage/devices',
            label: t('routes.devices.manageDevices'),
            element: <ManageDevices />,
            icon: <TopicIcon />,
            roles: ['admin', 'superadmin', 'enterprise']
          },
          {
            id: 'operators',
            path: 'operators',
            label: t('routes.devices.operators'),
            element: <ManageOperator />,
            icon: <Group />,
            roles: ['superadmin', 'admin', 'enterprise']
          },
          {
            id: 'device-map',
            path: 'device-map',
            label: t('routes.devices.deviceMap'),
            element: <DeviceMap />,
            icon: <MapOutlined />,
            roles: ['superadmin', 'admin', 'enterprise']
          }
        ]
      },
      {
        id: 'mqtt',
        path: 'mqtt',
        label: t('routes.mqtt.title'),
        icon: <CastOutlined />,
        element: null,
        items: [
          {
            id: 'mqtt-server',
            path: 'mqtt',
            label: t('routes.mqtt.serverInfo'),
            element: <ServerInfo />,
            icon: <RouterIcon />,
            roles: ['admin', 'superadmin']
          },
          {
            id: 'mqtt-dataview',
            path: 'dataview',
            label: t('routes.mqtt.dataView'),
            element: <DataVisualization />,
            icon: <BarChart />,
            roles: ['admin', 'superadmin', 'enterprise', 'operator']
          },
          {
            id: 'mqtt-dataview-detail',
            path: 'dataview/detail/:topic',
            label: 'Data View Detail',
            element: <DataViewDetail />,
            roles: ['admin', 'superadmin', 'enterprise', 'operator'],
            showInMenu: false
          },
          {
            id: 'mqtt-threshold-alert',
            path: 'mqtt/threshold/alert',
            label: t('routes.mqtt.manageAlert'),
            element: <ManageAlert />,
            icon: <GppMaybe />,
            roles: ['admin', 'superadmin', 'enterprise', 'operator']
          },
          {
            id: 'mqtt-alarms',
            path: 'mqtt/alarms',
            label: t('routes.mqtt.alarmlogs'),
            element: <MqttAlarms />,
            icon: <EventRepeat />,
            roles: ['admin', 'superadmin', 'enterprise', 'operator']
          },
        ]
      },
      {
        id: 'scada',
        path: 'scada',
        label: t('routes.scada.title'),
        element: <SCADA />,
        icon: <BarChart />,
        roles: ['admin', 'superadmin', 'enterprise', 'operator']
      },
      {
        id: 'users',
        path: 'users',
        label: t('routes.users'),
        icon: <ManageAccounts />,
        element: null,
        items: [
          {
            id: 'profile',
            path: 'users/profile',
            label: t('routes.myProfile'),
            element: <MyProfile />,
            icon: <AccountCircleRounded />
          },
          {
            id: 'manage',
            path: 'users/manage',
            label: t('routes.users'),
            element: <ManagePage />,
            icon: <ManageAccountsRounded />,
            roles: ['admin', 'superadmin']
          }
        ]
      },
    ],

  };

  return routes;
};

export type { RouteConfig };
