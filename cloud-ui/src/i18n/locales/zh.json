{"common": {"confirm": "确认", "cancel": "取消", "create": "创建", "delete": "删除", "update": "更新", "edit": "编辑", "save": "保存", "loading": "加载中...", "noData": "暂无数据", "backToHome": "返回首页", "search": "搜索", "clear": "清除", "add": "添加", "status": {"text": "状态", "enabled": "已启用", "disabled": "已禁用", "active": "活跃", "inactive": "不活跃", "running": "运行中", "stopped": "已停止", "connected": "已连接", "disconnected": "已断开"}, "copy": "复制", "copied": "已复制到剪贴板", "actions": "操作", "close": "关闭", "error": "错误：{{error}}", "unknownError": "未知错误", "refresh": "刷新", "pagination": {"rowsPerPage": "每页行数：", "displayedRows": "{{from}}-{{to}} / {{count}}"}, "fields": {"name": "姓名", "username": "用户名", "password": "密码", "email": "邮箱", "createdAt": "创建时间", "enterpriseId": "企业ID", "topic": "主题", "dataType": "数据类型", "lastUpdated": "最后更新时间", "createdBy": "创建人", "status": "状态"}, "validation": {"required": "{{field}}不能为空", "invalidFormat": "{{field}}格式不正确"}, "messages": {"createSuccess": "{{item}}创建成功", "createError": "创建{{item}}失败", "deleteSuccess": "{{item}}删除成功", "deleteError": "删除{{item}}失败", "updateSuccess": "{{item}}更新成功", "updateError": "更新{{item}}失败", "fetchError": "获取{{item}}失败：{{error}}", "copySuccess": "{{item}}已复制到剪贴板"}, "deleteConfirm": {"title": "删除{{item}}", "message": "确定要删除此{{item}}吗？", "warning": "此操作无法撤销。"}, "noItems": "暂无{{item}}"}, "time": {"second": "秒", "seconds": "秒", "minute": "分钟", "minutes": "分钟", "hour": "小时", "hours": "小时", "day": "天", "days": "天", "ago": "前", "never": "从未"}, "notFound": {"title": "404", "subtitle": "页面未找到", "message": "抱歉，您请求的页面不存在。"}, "operator": {"management": "操作员管理", "addOperator": "添加操作员", "deleteOperator": "删除操作员", "deleteConfirm": "确定要删除操作员：{{name}} ({{username}})？", "createNew": "创建新操作员", "noOperators": "暂无操作员", "emptyState": "创建操作员以允许他们访问您的设备", "managementDescription": "管理可以访问设备的操作员", "loadingMessage": "正在加载操作员数据...", "validation": {"usernameRequired": "请输入用户名", "usernameInvalid": "用户名必须以字母开头，只能包含字母和数字，且不超过{{max}}个字符", "passwordRequired": "请输入密码", "passwordInvalid": "密码长度必须在{{min}}-{{max}}个字符之间，且至少包含以下两种：字母、数字、特殊字符({{chars}})", "emailRequired": "请输入邮箱", "emailInvalid": "邮箱格式不正确", "nameRequired": "姓名不能为空", "usernameExists": "用户名已存在", "emailExists": "邮箱已存在"}}, "device": {"management": "设备管理", "register": "注册设备", "createTopic": "创建主题", "refresh": "立即刷新", "noDevices": "未找到设备。注册您的第一个设备以开始使用。", "unnamedDevice": "未命名设备", "noTopics": "暂无主题", "details": "设备详情", "editName": "编辑设备名称", "namePlaceholder": "输入设备名称", "map": {"searchDevice": "搜索设备", "showTracks": "显示设备轨迹", "noTracksData": "暂无轨迹数据可显示", "trackInfo": {"toggleTrack": "切换轨迹显示", "start": "起点", "end": "终点", "device": "设备", "lastUpdate": "最后更新", "longitude": "经度", "latitude": "纬度"}}, "add": {"title": "添加设备", "instruction": "输入设备信息以添加新设备", "add": "添加设备", "adding": "添加中..."}, "admin": {"emptyState": "点击添加设备按钮向系统添加设备"}, "validation": {"snRequired": "序列号不能为空", "snAlphanumeric": "序列号只能包含字母和数字", "snLength": "序列号不能超过24个字符", "snHelp": "请输入一个唯一的字母数字序列号（最多24个字符）", "imeiRequired": "IMEI码不能为空", "imeiAlphanumeric": "IMEI码只能包含字母和数字", "imeiLength": "IMEI码不能超过24个字符", "imeiHelp": "请输入一个唯一的字母数字IMEI码（最多24个字符）", "typeRequired": "设备类型不能为空"}, "fields": {"name": "设备名称", "sn": "序列号", "registeredAt": "注册时间", "registeredBy": "注册人", "unregistered": "未注册", "operators": "操作员", "topics": "主题", "clientId": "您的MQTT客户端ID", "type": "设备类型", "imei": "IMEI码", "longitude": "经度", "latitude": "纬度"}, "type": {"manageTitle": "管理设备类型", "manageInfo": "添加或删除可在添加新设备时使用的设备类型", "nameLabel": "类型名称", "nameRequired": "类型名称不能为空", "add": "添加类型", "existingTypes": "现有类型", "noTypes": "未找到设备类型", "noTypesAvailable": "没有可用的设备类型。请至少添加一种类型。", "name": "类型名称", "manageTypes": "管理类型"}, "operators": {"noResults": "未找到操作员", "assign": "分配操作员", "none": "未分配操作员", "select": "选择操作员", "selectHint": "选择要分配给设备：< {{name}} > 的操作员", "chooseOperators": "选择操作员", "selected": "已选择 {{count}} 个操作员"}, "topics": {"create": {"title": "创建新主题", "name": "主题名称", "placeholder": "sensors/temperature", "finalTopic": "最终主题：{{deviceSn}}/{{topic}}", "selectDevice": "选择设备", "prefixInfo": "• 您的主题将以 {{deviceSn}}/ 为前缀(设备序列号)", "formatInfo": "• 主题名称只能包含字母和数字"}, "details": "主题详情", "none": "暂无可用主题", "loadError": "加载主题信息失败"}, "clientId": {"show": "显示客户端ID", "hide": "隐藏客户端ID", "copy": "复制客户端ID"}}, "auth": {"unauthorized": {"title": "访问被拒绝", "message": "您没有权限访问此页面。"}, "sessionExpired": {"title": "☹ 会话已过期", "message": "您的登录会话已过期，请重新登录以继续访问。", "loginButton": "登 录"}, "signIn": {"title": "登录", "emailLabel": "邮箱 / 用户名", "passwordLabel": "密码", "rememberMe": "记住我", "forgotPassword": "忘记密码？", "submitButton": "登 录", "newUser": "还没有账号？", "signUpLink": "立即注册！", "loginError": "登录失败：{{error}}"}, "signUp": {"title": "注册", "fields": {"name": "姓名/公司名称", "username": "用户名", "password": "密码", "confirmPassword": "确认密码", "email": "邮箱", "enterpriseCode": "企业注册码"}, "enterpriseUser": "注册为企业用户", "sendCode": "发送验证码", "resendCode": "{{seconds}}秒后重新发送", "alreadyHaveAccount": "已有账号？", "signInLink": "立即登录！", "validation": {"waitForCode": "请等待{{seconds}}秒后再次发送验证码。", "emailRequired": "请输入邮箱。", "usernameTooLong": "用户名不能超过12个字符！", "passwordMismatch": "两次输入的密码不一致！", "passwordComplexity": "密码长度至少为8个字符，且必须包含大写字母、小写字母、数字和特殊字符！"}, "messages": {"sendCodeError": "发送验证码失败：{{error}}", "registrationError": "注册失败：{{error}}"}}, "forgotPassword": {"title": "忘记密码", "emailPlaceholder": "请输入邮箱", "sendCode": "发送验证码", "resendCode": "{{seconds}}秒后重新发送", "validation": {"waitForCode": "请等待{{seconds}}秒后再次发送验证码。", "emailRequired": "请输入邮箱地址！"}, "messages": {"sendCodeError": "发送验证码失败：{{error}}", "resetError": "重置密码失败：{{error}}"}}, "verifyEmail": {"title": "验证邮箱", "enterCode": "请输入发送到您邮箱的验证码"}}, "profile": {"title": "个人信息", "subtitle": "自定义您的个人资料信息在网络中的显示方式。", "actions": {"verify": "验证", "verifying": "验证中...", "updatePassword": "更新密码"}, "password": {"title": "更新密码", "oldPassword": "旧密码", "newPassword": "新密码", "updating": "更新中...", "update": "更新", "requirements": "新密码不符合要求：密码必须包含至少两种字符类型（字母、数字、特殊字符）且长度在8-20个字符之间。"}, "verification": {"title": "验证", "message": "请输入发送到您邮箱的6位验证码", "emailMessage": "新的验证码将发送到您的新邮箱。请查收！", "verify": "验证", "verifying": "验证中...", "enterAllDigits": "请输入完整的6位数字"}, "messages": {"passwordUpdateSuccess": "密码更新成功，即将重新登录", "updateFailed": "更新失败：{{error}}", "nameRequired": "姓名不能为空", "emailVerificationRequired": "请先验证新邮箱", "verifyCodeFailed": "验证码发送失败：{{error}}", "avatarSizeLimit": "头像图片大小不能超过2MB", "avatarUploadSuccess": "头像上传成功", "avatarUploadFailed": "头像上传失败", "invalidImageType": "不支持的图片格式，请使用JPG、PNG、GIF或WebP格式", "fileReadError": "文件读取失败，请重试"}}, "routes": {"users": "用户", "myProfile": "个人资料", "home": "首页", "wireguard": "Wireguard", "devices": {"title": "设备", "deviceAdmin": "设备管理", "manageDevices": "管理设备", "operators": "操作员", "deviceMap": "设备地图"}, "mqtt": {"title": "MQTT服务器", "serverInfo": "服务器信息", "dataView": "数据视图", "alarmlogs": "告警日志", "manageAlert": "告警管理"}, "scada": {"title": "SCADA"}, "manage": "用户管理"}, "mqtt": {"threshold": {"title": "告警规则", "manageAlert": {"loading": "加载阈值规则中...", "pleaseWait": "请稍候，正在加载告警规则", "title": "告警规则管理", "description": "管理基于阈值的MQTT主题告警规则", "noRules": "暂无告警规则", "emptyDescription": "创建告警规则以监控设备数据，当值超过阈值时获得通知", "noDescription": "暂无描述", "createdBy": "创建者：{{name}}", "selectTopicFirst": "请先选择一个主题", "fields": {"topic": "主题", "expression": "表达式", "description": "描述", "notification": "通知方式", "createdBy": "创建者", "actions": "操作"}, "createNew": "创建新规则", "dialog": {"title": {"create": "创建新阈值规则", "edit": "编辑阈值规则"}, "fields": {"topic": "主题", "notificationMode": "通知方式", "email": "邮箱地址", "sms": "手机号码", "wechat": "微信账号", "minValue": "最小值", "maxValue": "最大值", "description": "描述", "descriptionTooltip": "该描述将在微信通知中显示，最多显示20个字符，超过则会被截断", "enableRule": "启用规则", "expressionType": "表达式类型", "generatedExpression": "生成的表达式"}, "expressionBuilder": {"simple": "简单比较", "range": "范围检查", "value": "值", "outsideRange": "当值在此范围外时触发警报", "alertWhenLessThan": "当值 <", "alertWhenGreaterThan": "或值 >", "helperText": "这是将被评估的表达式"}, "modes": {"email": "邮件", "sms": "短信", "wechat": "微信"}, "buttons": {"bindWechat": "绑定微信账号", "binding": "绑定中..."}, "qrCode": {"title": "扫描二维码绑定微信", "instruction": "请使用微信扫描二维码绑定账号", "connecting": "正在连接服务器...", "processing": "正在处理绑定，请稍候...", "timeoutWarning": "如果5分钟内未操作，连接将超时", "timeout": "绑定超时，请重试", "error": "绑定过程中发生错误：{{error}}", "connectionError": "WebSocket连接错误", "connectionClosed": "连接意外关闭", "fetchError": "获取二维码失败：{{error}}", "success": "微信账号绑定成功"}}}, "alarmLog": {"loading": "加载中...", "noRecords": "暂无告警记录", "title": "告警日志", "exportButton": "导出告警记录", "exportDialog": {"title": "导出告警历史"}, "table": {"time": "时间", "topic": "主题", "value": "数值", "description": "描述", "alarmType": "告警类型", "contactInfo": "联系方式"}}}, "dataview": {"publish": {"publishButton": "发布", "dataTypeInfo": "此主题期望数据类型为：{{type}}", "messageLabel": "消息内容", "true": "✓ True", "false": "✗ False"}, "loading": "正在加载主题列表...", "error": "加载主题失败，请稍后重试。", "noTopics": "暂无可用主题", "createTopicHint": "为您的设备创建主题以开始使用", "selectTopicsHint": "请从主题选择区域选择要监控的主题", "viewDetails": "查看详情", "searchPlaceholder": "搜索主题...", "viewModes": {"grid": "网格视图", "table": "表格视图"}, "alarm": "告警", "table": {"monitor": "监控", "latestValue": "最新数值"}, "detail": {"noData": "所选时间范围内无数据", "tryDifferentTime": "请尝试选择不同的时间范围", "chartsOnlyForNumeric": "图表仅适用于数值类型数据", "switchToTable": "请切换到表格视图查看非数值类型数据", "refresh": "刷新数据", "fullscreen": "全屏显示", "exitFullscreen": "退出全屏", "timeRange": {"label": "时间范围", "lastHour": "最近1小时", "last6Hours": "最近6小时", "last24Hours": "最近24小时", "last7Days": "最近7天"}, "stats": {"latest": "最新值", "average": "平均值", "minimum": "最小值", "maximum": "最大值"}, "chartType": {"label": "图表类型", "line": "折线图", "area": "面积图", "bar": "柱状图", "scatter": "散点图", "pie": "饼图", "gauge": "仪表盘"}, "viewMode": {"chart": "图表", "table": "表格"}}, "messageHistory": {"noTopic": "未指定主题", "noMessages": "该主题暂无消息", "exportButton": "导出历史数据", "expectDataType": "此主题期望数据类型为：", "exportDialog": {"title": "导出历史数据", "startTime": "开始时间", "endTime": "结束时间", "errors": {"selectTime": "请选择开始和结束时间", "invalidRange": "结束时间必须晚于开始时间"}}, "table": {"time": "时间", "payload": "消息内容", "qos": "QoS", "clientId": "客户端ID"}}}}, "wireguard": {"common": {"listenPort": "监听端口", "ip": "IP地址", "deviceName": "设备名称", "endpoint": "端点", "publicKey": "公钥", "privateKey": "私钥", "deviceDetails": "设备详情", "totalReceived": "总接收量", "totalSent": "总发送量", "lastHandshake": "最近握手时间", "received": "已接收", "transmitted": "已发送", "traffic": "流量", "peers": "对等点"}, "manager": {"title": "Wireguard 接口管理", "newDevice": "新建设备", "interfaces": "接口", "createDevice": "创建新 Wireguard 设备", "enterpriseIdHint": "输入您的企业ID", "peersCount": "对等点", "startInterface": "启动接口", "stopInterface": "停止接口", "managePeers": "管理对等点", "deleteDevice": "删除设备", "deleteConfirm": "确定要删除设备 \"{{name}}\" 吗?", "deleteWarning": "这将同时删除所有关联的对等点。此操作无法撤销。", "interfaceStarted": "接口启动成功", "interfaceStopped": "接口停止成功", "deviceCreated": "设备创建成功", "deviceDeleted": "设备删除成功", "createPeer": "为 {name} 创建新对等点", "peerName": "对等点名称", "peerCreated": "对等点创建成功", "peerDeleted": "对等点删除成功", "peerConfiguration": "{{name}} 配置", "copyConfig": "复制配置", "noPeers": "未找到对等点。创建一个新对等点开始使用。", "connectedPeers": "已连接对等点", "viewTrafficDetails": "查看流量详情", "trafficDistribution": "流量分布", "sentData": "已发送", "receivedData": "已接收", "totalData": "总计", "lastHourTraffic": "最近一小时的流量", "noTrafficData": "没有可用的流量数据", "trafficDetails": "流量详情"}, "bandwidth": {"limitTitle": "带宽限制设置", "manageLimit": "管理带宽限制", "deviceInfo": "设备信息", "currentLimit": "当前限制", "noLimit": "无限制", "enableLimit": "启用带宽限制", "limitValue": "带宽限制", "presets": "预设值", "limitDescription": "带宽限制适用于此 WireGuard 接口的总体流量。更改将立即生效并在系统重启后保持。", "limitUpdated": "带宽限制更新成功"}, "peers": {"title": "对等点管理", "newPeer": "新建对等点", "createPeer": "创建对等点", "peerConfig": "对等点配置", "copyConfig": "复制配置", "showQRCode": "显示二维码", "downloadConfig": "下载配置", "deleteConfirm": "确定要删除对等点 \"{{name}}\" 吗?", "qrCode": "二维码", "configCopied": "配置已复制到剪贴板", "editPeer": "编辑对等点", "saveChanges": "保存更改", "allowedIPs": "允许的IP", "peerIP": "对等点IP", "dns": "DNS服务器", "bytesSent": "已发送", "bytesReceived": "已接收", "configDownloaded": "配置文件已下载", "manageAllowedIPs": "管理允许的IP", "allowedIPsTitle": "管理允许的IP地址", "managePeerIPs": "管理对等点 \"{{name}}\" 的允许IP", "currentIPs": "当前允许的IP", "newAllowedIP": "新增允许的IP", "ipFormatHelp": "格式: ***********/24", "noAllowedIPs": "暂无允许的IP地址", "invalidIPFormat": "IP地址格式无效，正确格式: ***********/24", "ipAlreadyExists": "此IP地址已存在", "allowedIPsSaved": "允许的IP地址已保存", "failedToSaveIPs": "保存允许的IP地址失败", "failedToFetchIPs": "获取允许的IP地址失败"}}, "users": {"editUser": "编辑用户", "noUsers": "暂无用户", "saveChanges": "保存更改", "resetPassword": "重置密码", "tabs": {"users": "用户", "enterpriseCodes": "企业注册码"}, "fields": {"role": "角色"}, "roles": {"superadmin": "超级管理员", "admin": "管理员", "enterprise": "企业用户", "operator": "操作员", "user": "普通用户"}}, "enterprise": {"title": "企业注册码管理", "noCodes": "暂无企业注册码", "statusUsed": "已使用", "statusAvailable": "可用", "fields": {"code": "注册码", "status": "状态", "usedAt": "使用时间"}, "actions": {"generate": "生成新注册码", "createFirst": "创建第一个注册码"}}, "scada": {"editor": {"title": "SCADA编辑器", "untitled": "SCADA控制面板", "welcome": "欢迎使用SCADA组态编辑器", "welcomeMessage": "从左侧项目列表选择一个项目进行编辑，或者创建一个新项目开始设计", "projectList": "项目列表", "componentLibrary": "组件库", "myProjects": "我的SCADA项目", "noProjects": "暂无项目，点击上方 + 按钮创建", "backToLibrary": "返回组件库", "newProject": "创建新项目", "projectName": "项目名称", "projectNamePlaceholder": "例如：泵站监控系统", "createProject": "创建新SCADA项目", "openProject": "打开项目", "deleteProject": "删除项目", "deleteConfirm": "确定要删除项目\"{{name}}\"吗？", "saved": "(已保存)", "operationSuccess": "操作成功！", "autoSaving": "自动保存SCADA项目...", "loadError": "加载项目列表失败，请重试", "emptyProjectName": "项目名称不能为空", "createError": "创建失败，请重试", "saveError": "项目ID不存在，无法保存", "deleteError": "删除失败，请重试"}, "preview": {"title": "SCADA预览", "loading": "加载中...", "noProjectId": "未指定项目ID", "loadError": "加载项目失败", "projectNotFound": "项目不存在", "backToEditor": "返回编辑器"}, "toolbar": {"save": "保存", "saving": "保存中...", "preview": "进入预览模式", "edit": "返回编辑模式", "newWindow": "在新窗口预览", "undo": "撤销", "redo": "重做", "delete": "删除所选", "grid": "显示/隐藏网格", "zoomIn": "放大", "zoomOut": "缩小", "resetZoom": "重置缩放"}, "components": {"categories": {"dataDisplay": "数据展示", "controlDevice": "控制和设备组件", "industrialIcons": "工业图标"}, "types": {"gauge": "仪表盘", "chart": "图表", "sensor": "传感器", "text": "文本", "table": "数据表", "image": "图片", "button": "按钮", "switch": "开关", "slider": "滑块", "tank": "水箱", "pipe": "管道", "led": "指示灯", "thermometer": "温度计", "progressbar": "进度条", "motor": "电机", "label": "标签"}, "icons": {"pump": "泵", "valve": "阀门", "tank": "储罐", "motor": "电机", "fan": "风扇", "heater": "加热器", "filter": "过滤器", "reactor": "反应器", "compressor": "压缩机", "battery": "电池", "gauge": "仪表盘", "controlValve": "控制阀", "switch": "开关", "transformer": "变压器", "pipeConnection": "管道连接"}}, "properties": {"liveData": "实时数据", "basicProperties": "基本属性", "dataTag": "数据标签", "selectDataTag": "选择数据标签", "noDataTag": "无数据标签", "noAvailableTopics": "没有可用的主题", "panelTitle": "属性面板", "selectComponent": "请选择一个组件", "expandPanel": "展开面板", "collapsePanel": "折叠面板", "closePanel": "关闭面板", "imageUrl": "图片URL", "imageUrlPlaceholder": "https://example.com/image.jpg 或 data:image/...", "imageHelperText": "输入图片URL或上传文件", "uploadImage": "上传图片", "preview": "预览", "noImage": "未选择图片", "imageUploadTip": "最大文件大小：5MB。建议分辨率：1920x1080或更小。", "fileSizeLimit": "文件大小不能超过{{size}}", "onlyImageFiles": "只允许图片文件", "imageSizeLimit": "图片尺寸不应超过{{width}}x{{height}}像素", "imageProcessError": "处理图片时出错", "uploadError": "上传图片失败", "backgroundColor": "背景颜色", "common": {"id": "ID", "type": "类型", "title": "标题", "label": "标签", "minValue": "最小值", "maxValue": "最大值", "currentValue": "当前值", "value": "值", "unit": "单位", "precision": "精度", "showValue": "显示数值", "color": "颜色", "fillColor": "填充颜色", "size": "大小", "small": "小", "medium": "中", "large": "大", "horizontal": "水平", "vertical": "垂直", "rotation": "旋转角度", "scale": "刻度", "showScale": "显示刻度", "standard": "标准", "filled": "填充", "outlined": "轮廓", "leftAlign": "左对齐", "center": "居中", "rightAlign": "右对齐", "variant": "变体", "showLabels": "显示标签", "orientation": "方向", "runningState": "运行状态", "speed": "速度", "compact": "紧凑", "comfortable": "舒适"}, "components": {"button": "按钮", "chart": "图表", "gauge": "仪表盘", "industrialIcon": "工业图标", "label": "标签", "led": "指示灯", "motor": "电机", "pipe": "管道", "progressbar": "进度条", "sensor": "传感器", "slider": "滑块", "switch": "开关", "table": "数据表", "tank": "水箱", "text": "文本", "thermometer": "温度计", "severity": "严重程度", "info": "信息", "warning": "警告", "error": "错误", "message": "消息内容", "showIcon": "显示图标", "buttonText": "按钮文本", "actionType": "操作类型", "toggle": "切换", "set": "设置", "reset": "重置", "targetTag": "目标标签", "chartType": "图表类型", "lineChart": "折线图", "barChart": "柱状图", "areaChart": "面积图", "dataSource": "数据源", "iconType": "图标类型", "iconColor": "图标颜色", "textContent": "文本内容", "fontSize": "字体大小", "textColor": "文本颜色", "alignment": "对齐方式", "flowDirection": "流动方向", "flowSpeed": "流动速度", "pipeColor": "管道颜色", "fluidColor": "流体颜色", "right": "右", "left": "左", "up": "上", "down": "下", "lightState": "指示灯状态", "onColor": "开启颜色", "offColor": "关闭颜色", "rotationDirection": "旋转方向", "clockwise": "顺时针", "counterclockwise": "逆时针", "primary": "主要", "secondary": "次要", "success": "成功", "genericSensor": "通用传感器", "temperatureSensor": "温度传感器", "pressureSensor": "压力传感器", "flowSensor": "流量传感器", "powerSensor": "功率传感器", "sensorType": "传感器类型", "step": "步长", "currentState": "当前状态", "density": "密度", "rowsPerPage": "每页行数", "paginationPosition": "分页位置", "showIndex": "显示序号", "sortable": "启用排序", "showTitle": "显示标题", "selectable": "可选择行", "bottom": "底部", "top": "顶部", "both": "顶部和底部", "none": "无", "celsius": "摄氏度", "fahrenheit": "华氏度", "kelvin": "开尔文"}}}}