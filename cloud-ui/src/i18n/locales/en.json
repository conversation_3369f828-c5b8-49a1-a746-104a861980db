{"common": {"confirm": "Confirm", "cancel": "Cancel", "create": "Create", "delete": "Delete", "update": "Update", "edit": "Edit", "save": "Save", "loading": "Loading...", "noData": "No data available", "backToHome": "Back to Home", "search": "Search", "clear": "Clear", "add": "Add", "status": {"text": "Status", "enabled": "Enabled", "disabled": "Disabled", "active": "Active", "inactive": "Inactive", "running": "Running", "stopped": "Stopped", "connected": "Connected", "disconnected": "Disconnected"}, "copy": "Copy", "copied": "Copied to clipboard", "actions": "Actions", "close": "Close", "error": "Error: {{error}}", "unknownError": "Unknown Error", "refresh": "Refresh", "pagination": {"rowsPerPage": "Rows per page:", "displayedRows": "{{from}}-{{to}} of {{count}}"}, "fields": {"name": "Name", "username": "Username", "password": "Password", "email": "Email", "createdAt": "Created At", "enterpriseId": "Enterprise ID", "topic": "Topic", "dataType": "Data Type", "lastUpdated": "Last Updated", "createdBy": "Created By", "status": "Status"}, "validation": {"required": "{{field}} is required", "invalidFormat": "Invalid {{field}} format"}, "messages": {"createSuccess": "{{item}} created successfully", "createError": "Failed to create {{item}}", "deleteSuccess": "{{item}} deleted successfully", "deleteError": "Failed to delete {{item}}", "updateSuccess": "{{item}} updated successfully", "updateError": "Failed to update {{item}}", "fetchError": "Failed to fetch {{item}}: {{error}}", "copySuccess": "{{item}} copied to clipboard"}, "deleteConfirm": {"title": "Delete {{item}}", "message": "Are you sure you want to delete this {{item}}?", "warning": "This action cannot be undone."}, "noItems": "No {{item}} found"}, "time": {"second": "second", "seconds": "seconds", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "ago": "ago", "never": "Never"}, "notFound": {"title": "404", "subtitle": "Not Found", "message": "Sorry, the page you requested does not exist."}, "operator": {"management": "Operator Management", "addOperator": "Add Operator", "deleteOperator": "Delete Operator", "deleteConfirm": "Are you sure you want to delete operator: {{name}} ({{username}})?", "createNew": "Create New Operator", "noOperators": "No operators found", "emptyState": "Create operators to allow them access to your devices", "managementDescription": "Manage operators who can access devices", "loadingMessage": "Loading operator data...", "validation": {"usernameRequired": "Username is required", "usernameInvalid": "Userna<PERSON> must start with a letter, contain only letters and numbers, and be no longer than {{max}} characters", "passwordRequired": "Password is required", "passwordInvalid": "Password must be {{min}}-{{max}} characters long and contain at least two types of the following: letters, numbers, special characters ({{chars}})", "emailRequired": "Email is required", "emailInvalid": "Invalid email format", "nameRequired": "Name is required", "usernameExists": "Username already exists", "emailExists": "Email already exists"}}, "device": {"management": "Device Management", "register": "Register Device", "createTopic": "Create Topic", "refresh": "Refresh Now", "noDevices": "No devices found. Register your first device to get started.", "unnamedDevice": "Unnamed Device", "noTopics": "No topics", "details": "Details", "editName": "Edit device name", "namePlaceholder": "Enter device name", "map": {"searchDevice": "Search Device", "showTracks": "Show Device Tracks", "noTracksData": "No tracks data available", "trackInfo": {"toggleTrack": "Toggle track visibility", "start": "Start", "end": "End", "device": "<PERSON><PERSON>", "lastUpdate": "Last update", "longitude": "Longitude", "latitude": "Latitude"}}, "add": {"title": "Add <PERSON>", "instruction": "Enter device information to add a new device", "add": "Add <PERSON>", "adding": "Adding..."}, "admin": {"emptyState": "Click the Add Device button to add devices to your system"}, "validation": {"snRequired": "Serial number is required", "snAlphanumeric": "Serial number must contain only letters and numbers", "snLength": "Serial number must not exceed 24 characters", "snHelp": "Enter a unique alphanumeric serial number (max 24 characters)", "imeiRequired": "IMEI code is required", "imeiAlphanumeric": "IMEI code must contain only letters and numbers", "imeiLength": "IMEI code must not exceed 24 characters", "imeiHelp": "Enter a unique alphanumeric IMEI code (max 24 characters)", "typeRequired": "Device type is required"}, "fields": {"name": "Device Name", "sn": "Serial Number", "registeredAt": "Registered At", "registeredBy": "Registered By", "unregistered": "Unregistered", "operators": "Operators", "topics": "Topics", "clientId": "Your MQTT Client ID", "type": "Device Type", "imei": "IMEI Code", "longitude": "Longitude", "latitude": "Latitude"}, "type": {"manageTitle": "Manage Device Types", "manageInfo": "Add or remove device types that can be used when adding new devices", "nameLabel": "Type Name", "nameRequired": "Type name cannot be empty", "add": "Add Type", "existingTypes": "Existing Types", "noTypes": "No device types found", "noTypesAvailable": "No device types available. Please add at least one type.", "name": "Type Name", "manageTypes": "Manage Types"}, "operators": {"noResults": "No operators found", "assign": "Assign", "none": "No operators assigned", "select": "Select Operators", "selectHint": "Select operators to assign to device: < {{name}} >", "chooseOperators": "Choose operators", "selected": "{{count}} operator(s) selected"}, "topics": {"create": {"title": "Create New Topic", "name": "Topic Name", "placeholder": "sensors/temperature", "finalTopic": "Final topic: {{deviceSn}}/{{topic}}", "selectDevice": "Select Device", "prefixInfo": "• Your topic will be prefixed with {{deviceSn}}/", "formatInfo": "• Topic name can only contain letters and numbers"}, "details": "Topic Details", "none": "No topics available", "loadError": "Failed to load topic information"}, "clientId": {"show": "Show Client ID", "hide": "Hide Client ID", "copy": "Copy Client ID"}}, "auth": {"unauthorized": {"title": "Access Denied", "message": "You don't have permission to access this page."}, "sessionExpired": {"title": "☹ Session Expired", "message": "Your login session has expired. Please sign in again to continue.", "loginButton": "Sign In"}, "signIn": {"title": "Sign in", "emailLabel": "Email / UserName", "passwordLabel": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot your password?", "submitButton": "Sign in", "newUser": "New to Beacon Cloud?", "signUpLink": "Sign up!", "loginError": "Failed to login: {{error}}"}, "signUp": {"title": "Sign up", "fields": {"name": "Name/Company Name", "username": "Username", "password": "Password", "confirmPassword": "Confirm Password", "email": "Email", "enterpriseCode": "Enterprise Registration Code"}, "enterpriseUser": "Register as Enterprise User", "sendCode": "Send Verification Code", "resendCode": "Resend in {{seconds}}s", "alreadyHaveAccount": "Already have an account?", "signInLink": "Sign in!", "validation": {"waitForCode": "Please wait {{seconds}} seconds before sending another code.", "emailRequired": "Please input email.", "usernameTooLong": "<PERSON>rna<PERSON> must not exceed 12 characters!", "passwordMismatch": "Passwords do not match!", "passwordComplexity": "Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character!"}, "messages": {"sendCodeError": "Failed to send verification code: {{error}}", "registrationError": "Registration failed: {{error}}"}}, "forgotPassword": {"title": "Forgot Password", "emailPlaceholder": "Enter your email", "sendCode": "Send Verification Code", "resendCode": "Resend in {{seconds}}s", "validation": {"waitForCode": "Please wait {{seconds}} seconds before sending another code.", "emailRequired": "Please enter your email address!"}, "messages": {"sendCodeError": "Failed to send verification code: {{error}}", "resetError": "Password reset failed: {{error}}"}}, "verifyEmail": {"title": "<PERSON><PERSON><PERSON>", "enterCode": "Enter the verification code sent to your email"}}, "profile": {"title": "Personal info", "subtitle": "Customize how your profile information will appear to the networks.", "actions": {"verify": "Verify", "verifying": "Verifying...", "updatePassword": "Update Password"}, "password": {"title": "Update Password", "oldPassword": "Old Password", "newPassword": "New Password", "updating": "Updating...", "requirements": "The new password does not meet the requirements: it must contain at least two types of characters (letters, numbers, special characters) and be between 8 and 20 characters long."}, "verification": {"title": "Verification", "message": "Enter the 6-digit verification code sent to your email", "emailMessage": "A new captcha will be sent to your new email. Please check your inbox!", "verify": "Verify", "verifying": "Verifying...", "enterAllDigits": "Please enter all 6 digits"}, "messages": {"passwordUpdateSuccess": "Password updated successfully. Redirecting to login page...", "updateFailed": "Update Failed: {{error}}", "verifyCodeFailed": "Send verify code failed: {{error}}", "avatarSizeLimit": "Avatar file size cannot exceed 2MB", "avatarUploadSuccess": "Avatar uploaded successfully", "avatarUploadFailed": "Failed to upload avatar"}}, "routes": {"users": "Users", "myProfile": "My Profile", "home": "Home", "wireguard": "Wireguard", "devices": {"title": "Devices", "deviceAdmin": "Devices", "manageDevices": "Manage Devices", "operators": "Operators", "deviceMap": "Device Map"}, "mqtt": {"title": "MQTT Server", "serverInfo": "Server Info", "dataView": "DataView", "alarmlogs": "Alarms Logs", "manageAlert": "Manage Al<PERSON>"}, "scada": {"title": "SCADA"}, "manage": "User Management"}, "mqtt": {"threshold": {"title": "Alert <PERSON>", "manageAlert": {"loading": "Loading threshold rules...", "pleaseWait": "Please wait while we load the alert rules", "title": "Alert Rules Management", "description": "Manage threshold-based alert rules for MQTT topics", "noRules": "No alert rules found", "emptyDescription": "Create alert rules to monitor your device data and get notifications when values exceed thresholds", "noDescription": "No description provided", "createdBy": "Created by {{name}}", "selectTopicFirst": "Please select a topic first", "fields": {"topic": "Topic", "expression": "Expression", "description": "Description", "notification": "Notification", "createdBy": "Created By", "actions": "Actions"}, "createNew": "Create New Rule", "dialog": {"title": {"create": "Create New Threshold Rule", "edit": "<PERSON><PERSON><PERSON>"}, "fields": {"topic": "Topic", "notificationMode": "Notification Mode", "email": "Email Address", "sms": "Phone Number", "wechat": "<PERSON><PERSON><PERSON> Account", "minValue": "Minimum Value", "maxValue": "Maximum Value", "description": "Description", "descriptionTooltip": "The description will be displayed in the wechat notification,max 20 characters,if exceed,it will be truncated", "enableRule": "Enable Rule", "expressionType": "Expression Type", "generatedExpression": "Generated Expression"}, "expressionBuilder": {"simple": "Simple Comparison", "range": "Range Check", "value": "value", "outsideRange": "Trigger alert when value is OUTSIDE this range", "alertWhenLessThan": "value <", "alertWhenGreaterThan": "or value >", "helperText": "This is the expression that will be evaluated"}, "modes": {"email": "Email", "sms": "SMS", "wechat": "WeChat"}, "buttons": {"bindWechat": "Bind <PERSON><PERSON><PERSON> Account", "binding": "Binding..."}, "qrCode": {"title": "Scan QR Code to Bind WeChat", "instruction": "Please scan the QR code with WeChat to bind your account"}}}, "alarmLog": {"loading": "Loading...", "noRecords": "No alarm records found", "title": "Alarm Logs", "exportButton": "Export Alarm History", "exportDialog": {"title": "Export Alarm History"}, "table": {"time": "Time", "topic": "Topic", "value": "Value", "description": "Description", "alarmType": "Alarm Type", "contactInfo": "Contact Info"}}}, "dataview": {"publish": {"publishButton": "Publish", "dataTypeInfo": "This topic expects data of type: {{type}}", "messageLabel": "Message Payload", "true": "✓ True", "false": "✗ False"}, "loading": "Loading topic list...", "error": "Failed to load topics. Please try again later.", "noTopics": "No topics available", "createTopicHint": "Create a topic for your device to get started", "selectTopicsHint": "Please select topics from the topic selection area", "viewDetails": "View Details", "searchPlaceholder": "Search topics...", "viewModes": {"grid": "Grid View", "table": "Table View"}, "alarm": "Alarm", "table": {"monitor": "Monitor", "latestValue": "Latest Value"}, "detail": {"noData": "No data available for the selected time range", "tryDifferentTime": "Try selecting a different time range", "chartsOnlyForNumeric": "Charts are only available for numeric data", "switchToTable": "Please switch to table view to see non-numeric data", "refresh": "Refresh data", "fullscreen": "Fullscreen", "exitFullscreen": "Exit fullscreen", "timeRange": {"label": "Time Range", "lastHour": "Last Hour", "last6Hours": "Last 6 Hours", "last24Hours": "Last 24 Hours", "last7Days": "Last 7 Days"}, "stats": {"latest": "Latest Value", "average": "Average", "minimum": "Minimum", "maximum": "Maximum"}, "chartType": {"label": "Chart Type", "line": "Line Chart", "area": "Area Chart", "bar": "Bar Chart", "scatter": "Scatter Plot", "pie": "Pie Chart", "gauge": "Gauge Chart"}, "viewMode": {"chart": "Chart", "table": "Table"}}, "messageHistory": {"noTopic": "No topic specified", "noMessages": "No messages available for this topic", "exportButton": "Export Historical Data", "expectDataType": "This topic expects data of type: ", "exportDialog": {"title": "Export Historical Data", "startTime": "Start Time", "endTime": "End Time", "errors": {"selectTime": "Please select both start and end times", "invalidRange": "End time must be after start time"}}, "table": {"time": "Time", "payload": "Payload", "qos": "QoS", "clientId": "Client ID"}}}}, "wireguard": {"common": {"deviceName": "Device Name", "ip": "IP Address", "listenPort": "Listen Port", "endpoint": "Endpoint", "publicKey": "Public Key", "privateKey": "Private Key", "deviceDetails": "<PERSON>ce Det<PERSON>", "totalReceived": "Total Received", "totalSent": "Total Sent", "lastHandshake": "Last Handshake", "received": "Received", "transmitted": "Transmitted", "traffic": "Traffic", "peers": "Peers"}, "manager": {"title": "Wireguard Interface Manager", "newDevice": "New Device", "interfaces": "Interfaces", "createDevice": "Create New Wireguard Device", "enterpriseIdHint": "Enter your enterprise ID", "peersCount": "Peers", "startInterface": "Start Interface", "stopInterface": "Stop Interface", "managePeers": "Manage Peers", "deleteDevice": "Delete Device", "deleteConfirm": "Are you sure you want to delete device \"{{name}}\"?", "deleteWarning": "This will also delete all associated peers. This action cannot be undone.", "interfaceStarted": "Interface started successfully", "interfaceStopped": "Interface stopped successfully", "deviceCreated": "<PERSON>ce created successfully", "deviceDeleted": "<PERSON>ce deleted successfully", "createPeer": "Create New Peer for {name}", "peerName": "Peer Name", "peerCreated": "Peer created successfully", "peerDeleted": "<PERSON><PERSON> deleted successfully", "peerConfiguration": "{{name}} Configuration", "copyConfig": "<PERSON><PERSON> Config", "noPeers": "No peers found for this device. Create a new peer to get started.", "connectedPeers": "Connected Peers", "viewTrafficDetails": "View Traffic Details", "trafficDistribution": "Traffic Distribution", "sentData": "<PERSON><PERSON>", "receivedData": "Received", "totalData": "Total", "lastHourTraffic": "Traffic over the last hour", "noTrafficData": "No traffic data available", "trafficDetails": "Traffic Details"}, "bandwidth": {"limitTitle": "Bandwidth Limit Settings", "manageLimit": "Manage Bandwidth Limit", "deviceInfo": "Device Information", "currentLimit": "Current Limit", "noLimit": "No limit", "enableLimit": "Enable bandwidth limiting", "limitValue": "Bandwidth Limit", "presets": "Presets", "limitDescription": "Bandwidth limiting applies to the overall traffic through this WireGuard interface. Changes take effect immediately and persist across system restarts.", "limitUpdated": "Bandwidth limit updated successfully"}, "peers": {"title": "Peers Manager", "newPeer": "New Peer", "createPeer": "Create New Peer", "peerConfig": "Peer Configuration", "copyConfig": "Copy Configuration", "showQRCode": "Show QR Code", "downloadConfig": "Download Configuration", "deleteConfirm": "Are you sure you want to delete peer \"{{name}}\"?", "qrCode": "QR Code", "configCopied": "Configuration copied to clipboard", "editPeer": "<PERSON>", "saveChanges": "Save Changes", "allowedIPs": "Allowed IPs", "peerIP": "Peer IP", "dns": "DNS Servers", "bytesSent": "<PERSON><PERSON>", "bytesReceived": "Received", "configDownloaded": "Configuration file downloaded", "manageAllowedIPs": "Manage Allowed IPs", "allowedIPsTitle": "Manage Allowed IP Addresses", "managePeerIPs": "Manage Allowed IPs for <PERSON><PERSON> \"{{name}}\"", "currentIPs": "Current Allowed IPs", "newAllowedIP": "New Allowed IP", "ipFormatHelp": "Format: ***********/24", "noAllowedIPs": "No allowed IP addresses", "invalidIPFormat": "Invalid IP format, correct format: ***********/24", "ipAlreadyExists": "This IP address already exists", "allowedIPsSaved": "Allowed IP addresses saved successfully", "failedToSaveIPs": "Failed to save allowed IP addresses", "failedToFetchIPs": "Failed to fetch allowed IP addresses"}}, "users": {"editUser": "Edit User", "noUsers": "No users found", "saveChanges": "Save Changes", "resetPassword": "Reset Password", "tabs": {"users": "Users", "enterpriseCodes": "Enterprise Codes"}, "fields": {"role": "Role"}, "roles": {"superadmin": "Super Admin", "admin": "Admin", "enterprise": "Enterprise User", "operator": "Operator", "user": "Regular User"}}, "enterprise": {"title": "Enterprise Registration Codes", "noCodes": "No enterprise codes found", "statusUsed": "Used", "statusAvailable": "Available", "fields": {"code": "Code", "status": "Status", "usedAt": "Used At"}, "actions": {"generate": "Generate New Code", "createFirst": "Create first code"}}, "scada": {"editor": {"title": "SCADA Editor", "untitled": "Untitled SCADA Panel", "welcome": "Welcome to SCADA Configuration Editor", "welcomeMessage": "Select a project from the project list on the left, or create a new project to start designing your SCADA interface.", "projectList": "Project List", "componentLibrary": "Component Library", "myProjects": "My SCADA Projects", "noProjects": "No projects yet. Click the '+' button to create a new one.", "backToLibrary": "Back to Component Library", "newProject": "New Project", "projectName": "Project Name", "projectNamePlaceholder": "e.g., Pump Station Monitoring System", "createProject": "Create New SCADA Project", "openProject": "Open Project", "deleteProject": "Delete Project", "deleteConfirm": "Are you sure you want to delete project \"{{name}}\"?", "saved": "(Saved)", "operationSuccess": "Operation successful!", "autoSaving": "Auto-saving SCADA project...", "loadError": "Failed to load project list, please try again.", "emptyProjectName": "Project name cannot be empty.", "createError": "Creation failed, please try again.", "saveError": "Project ID does not exist, cannot save.", "deleteError": "Deletion failed, please try again."}, "preview": {"title": "SCADA Preview", "loading": "Loading...", "noProjectId": "No project ID specified", "loadError": "Failed to load project", "projectNotFound": "Project not found", "backToEditor": "Back to Editor"}, "toolbar": {"save": "Save", "saving": "Saving...", "preview": "Preview Mode", "newWindow": "New Window Preview", "edit": "Return to Edit Mode", "undo": "Undo", "redo": "Redo", "delete": "Delete Selected", "grid": "Show/Hide Grid", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "resetZoom": "Reset Zoom"}, "components": {"categories": {"dataDisplay": "Data Display", "controlDevice": "Control & Device Components", "industrialIcons": "Industrial Icons"}, "types": {"gauge": "Gauge", "chart": "Chart", "sensor": "Sensor", "text": "Text", "table": "Table", "image": "Image", "button": "<PERSON><PERSON>", "switch": "Switch", "slider": "Slide<PERSON>", "tank": "Tank", "pipe": "<PERSON><PERSON>", "led": "LED", "thermometer": "Thermometer", "progressbar": "Progress Bar", "motor": "Motor", "label": "Label"}, "icons": {"pump": "Pump", "valve": "Valve", "tank": "Tank", "motor": "Motor", "fan": "Fan", "heater": "Heater", "filter": "Filter", "reactor": "Reactor", "compressor": "Compressor", "battery": "Battery", "gauge": "Gauge", "controlValve": "Control Valve", "switch": "Switch", "transformer": "Transformer", "pipeConnection": "Pipe Connection"}}, "properties": {"liveData": "Live Data", "noDataTag": "No data tag selected", "noAvailableTopics": "No available topics", "panelTitle": "Properties", "basicProperties": "Basic Properties", "selectComponent": "Please select a component to view its properties", "expandPanel": "Expand properties panel", "collapsePanel": "Collapse panel", "closePanel": "Close panel", "dataTag": "Data Tag", "selectDataTag": "Select Data Tag", "imageUrl": "Image URL", "imageUrlPlaceholder": "https://example.com/image.jpg or data:image/...", "imageHelperText": "Enter an image URL or upload a file", "uploadImage": "Upload Image", "preview": "Preview", "noImage": "No image selected", "imageUploadTip": "Max file size: 5MB. Recommended resolution: 1920x1080 or less.", "fileSizeLimit": "File size cannot exceed {{size}}", "onlyImageFiles": "Only image files are allowed", "imageSizeLimit": "Image dimensions should not exceed {{width}}x{{height}} pixels", "imageProcessError": "Error processing image", "uploadError": "Failed to upload image", "backgroundColor": "Background Color", "common": {"id": "ID", "type": "Type", "title": "Title", "label": "Label", "minValue": "Minimum Value", "maxValue": "Maximum Value", "currentValue": "Current Value", "value": "Value", "unit": "Unit", "precision": "Precision", "showValue": "Show Value", "color": "Color", "fillColor": "Fill Color", "size": "Size", "small": "Small", "medium": "Medium", "large": "Large", "horizontal": "Horizontal", "vertical": "Vertical", "rotation": "Rotation", "scale": "Scale", "showScale": "Show Scale", "standard": "Standard", "filled": "Filled", "outlined": "Outlined", "leftAlign": "Left", "center": "Center", "rightAlign": "Right", "variant": "<PERSON><PERSON><PERSON>", "showLabels": "Show Labels", "orientation": "Orientation", "runningState": "Running State", "speed": "Speed", "compact": "Compact", "comfortable": "Comfortable"}, "components": {"button": "<PERSON><PERSON>", "chart": "Chart", "gauge": "Gauge", "industrialIcon": "Industrial Icon", "label": "Label", "led": "LED", "motor": "Motor", "pipe": "<PERSON><PERSON>", "progressbar": "Progress Bar", "sensor": "Sensor", "slider": "Slide<PERSON>", "switch": "Switch", "table": "Table", "tank": "Tank", "text": "Text", "thermometer": "Thermometer", "severity": "Severity Level", "info": "Information", "warning": "Warning", "error": "Error", "message": "Message", "showIcon": "Show Icon", "buttonText": "Button Text", "actionType": "Action Type", "toggle": "Toggle", "set": "Set", "reset": "Reset", "targetTag": "Target Tag", "chartType": "Chart Type", "lineChart": "Line Chart", "barChart": "Bar Chart", "areaChart": "Area Chart", "dataSource": "Data Source", "iconType": "Icon Type", "iconColor": "Icon Color", "textContent": "Text Content", "fontSize": "Font Size", "textColor": "Text Color", "alignment": "Alignment", "flowDirection": "Flow Direction", "flowSpeed": "Flow Speed", "pipeColor": "Pipe Color", "fluidColor": "Fluid Color", "right": "Right", "left": "Left", "up": "Up", "down": "Down", "lightState": "Light State", "onColor": "ON Color", "offColor": "OFF Color", "rotationDirection": "Rotation Direction", "clockwise": "Clockwise", "counterclockwise": "Counterclockwise", "primary": "Primary", "secondary": "Secondary", "success": "Success", "genericSensor": "Generic Sensor", "temperatureSensor": "Temperature Sensor", "pressureSensor": "Pressure Sensor", "flowSensor": "Flow Sensor", "powerSensor": "Power Sensor", "sensorType": "Sensor Type", "step": "Step", "currentState": "Current State", "density": "Density", "rowsPerPage": "Rows Per Page", "paginationPosition": "Pagination Position", "showIndex": "Show Index", "sortable": "Enable Sorting", "showTitle": "Show Title", "selectable": "Selectable Rows", "bottom": "Bottom", "top": "Top", "both": "Both", "none": "None", "celsius": "<PERSON><PERSON><PERSON>", "fahrenheit": "Fahrenheit", "kelvin": "<PERSON><PERSON>"}}}}