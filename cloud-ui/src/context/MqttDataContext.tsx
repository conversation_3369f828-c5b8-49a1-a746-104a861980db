import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import { createMqttWebSocket } from '../services/mqttDataService';

// MQTT数据类型定义
export interface MqttValue {
  topic: string;
  value: string | number | boolean;
  timestamp: number;
  dataType: 'number' | 'boolean' | 'string';
  raw: string;
}

// 上下文状态
interface MqttDataContextState {
  topicValues: { [topic: string]: MqttValue };
  subscribeToTopic: (topic: string) => void;
  unsubscribeFromTopic: (topic: string) => void;
  getTopicValue: (topic: string) => MqttValue | null;
  publishToTopic: (topic: string, value: string) => Promise<void>;
  batchSubscribe: (topics: string[]) => void;
  batchUnsubscribe: (topics: string[]) => void;
}

// 创建上下文
const MqttDataContext = createContext<MqttDataContextState>({
  topicValues: {},
  subscribeToTopic: () => { },
  unsubscribeFromTopic: () => { },
  getTopicValue: () => null,
  publishToTopic: async () => { },
  batchSubscribe: () => { },
  batchUnsubscribe: () => { },
});

// MQTT数据提供者组件
export const MqttDataProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 存储主题值的状态
  const [topicValues, setTopicValues] = useState<{ [topic: string]: MqttValue }>({});

  // 订阅主题列表
  const subscribedTopics = useRef<Set<string>>(new Set());
  
  // 用于批量处理订阅请求的队列和定时器
  const pendingSubscriptionTopics = useRef<Set<string>>(new Set());
  const pendingUnsubscriptionTopics = useRef<Set<string>>(new Set());
  const subscriptionDebounceTimer = useRef<NodeJS.Timeout | null>(null);
  const wsReadyRef = useRef<boolean>(false);
  const pendingBatchSent = useRef<boolean>(false);

  // WebSocket引用
  const wsRef = useRef<{ ws: WebSocket; cleanup: () => void } | null>(null);

  // 处理防抖批量订阅/取消订阅
  const processPendingSubscriptions = useCallback(() => {
    if (subscriptionDebounceTimer.current) {
      clearTimeout(subscriptionDebounceTimer.current);
      subscriptionDebounceTimer.current = null;
    }
    
    // 标记批处理已发送
    pendingBatchSent.current = true;
    
    const topicsToSubscribe = Array.from(pendingSubscriptionTopics.current);
    const topicsToUnsubscribe = Array.from(pendingUnsubscriptionTopics.current);
    
    // 清空等待队列
    pendingSubscriptionTopics.current.clear();
    pendingUnsubscriptionTopics.current.clear();
    
    //console.log('[MQTT] 处理批量订阅队列，订阅主题数:', topicsToSubscribe.length, '取消订阅主题数:', topicsToUnsubscribe.length);
    
    // 处理批量订阅
    if (topicsToSubscribe.length > 0) {
      // 过滤掉已订阅的主题
      const newTopics = topicsToSubscribe.filter(topic => !subscribedTopics.current.has(topic));
      
      if (newTopics.length > 0) {
        // 添加到订阅集合
        newTopics.forEach(topic => {
          subscribedTopics.current.add(topic);
        });
        
        // 如果WebSocket已连接，发送批量订阅请求
        if (wsRef.current && wsRef.current.ws.readyState === WebSocket.OPEN) {
          //console.log('[MQTT] 发送合并批量订阅请求，主题数:', newTopics.length, '主题列表:', newTopics);
          wsRef.current.ws.send(JSON.stringify({
            type: 'subscribe',
            topics: newTopics,
            mode: 'realtime',
            interval: 300
          }));
        } else {
          console.log('[MQTT] WebSocket未连接，无法发送批量订阅');
        }
      }
    }
    
    // 处理批量取消订阅
    if (topicsToUnsubscribe.length > 0) {
      // 过滤出已订阅的主题
      const validTopics = topicsToUnsubscribe.filter(topic => subscribedTopics.current.has(topic));
      
      if (validTopics.length > 0) {
        // 从订阅集合中移除
        validTopics.forEach(topic => {
          subscribedTopics.current.delete(topic);
        });
        
        // 如果WebSocket已连接，发送批量取消订阅请求
        if (wsRef.current && wsRef.current.ws.readyState === WebSocket.OPEN) {
          //console.log('[MQTT] 发送合并批量取消订阅请求:', validTopics);
          wsRef.current.ws.send(JSON.stringify({
            type: 'unsubscribe',
            topics: validTopics
          }));
        }
      }
    }
  }, []);

  // 安排定时处理批量订阅
  const scheduleSubscriptionProcessing = useCallback(() => {
    // 如果已经有计划的处理，重置它
    if (subscriptionDebounceTimer.current) {
      clearTimeout(subscriptionDebounceTimer.current);
    }
    
    // 增加防抖时间到300ms，给更多的订阅请求时间合并
    subscriptionDebounceTimer.current = setTimeout(() => {
      processPendingSubscriptions();
    }, 300);
  }, [processPendingSubscriptions]);

  // 发送当前所有订阅
  const sendSubscriptions = useCallback(() => {
    if (!wsRef.current || wsRef.current.ws.readyState !== WebSocket.OPEN || subscribedTopics.current.size === 0) {
      return;
    }

    //console.log('[MQTT] 发送所有活跃订阅请求:', Array.from(subscribedTopics.current));

    // 使用更低的interval值，并设置为实时模式
    wsRef.current.ws.send(JSON.stringify({
      type: 'subscribe',
      topics: Array.from(subscribedTopics.current),
      mode: 'realtime',  // 使用实时模式而不是stream模式
      interval: 300      // 降低间隔到300毫秒
    }));
  }, []);

  // 初始化WebSocket连接
  useEffect(() => {
    // 心跳计时器
    let pingTimer: NodeJS.Timeout | null = null;
    // 连接监控计时器
    let connectionCheckTimer: NodeJS.Timeout | null = null;
    // 连接重试次数
    // let reconnectAttempts = 0;

    // 上次接收数据的时间
    let lastDataReceived = Date.now();

    // 处理WebSocket消息
    const handleMessage = (data: any) => {
      // 更新最后接收数据的时间
      lastDataReceived = Date.now();
      
      // 检查是否为批量消息格式
      if (Array.isArray(data)) {
        // 处理批量数据
        const updates: { [topic: string]: MqttValue } = {};

        data.forEach(item => {
          if (item && item.latestMessage) {
            const message = item.latestMessage;
            const topic = message.topic;

            if (topic && message.payload !== undefined && subscribedTopics.current.has(topic)) {
              // 推测数据类型
              let value: string | number | boolean = message.payload;
              let dataType: 'number' | 'boolean' | 'string' = 'string';

              // 尝试转换数值
              if (!isNaN(Number(message.payload))) {
                value = Number(message.payload);
                dataType = 'number';
              }
              // 尝试转换布尔值
              else if (message.payload.toLowerCase() === 'true' || message.payload.toLowerCase() === 'false') {
                value = message.payload.toLowerCase() === 'true';
                dataType = 'boolean';
              }

              updates[topic] = {
                topic,
                value,
                timestamp: message.time || Date.now(),
                dataType,
                raw: message.payload
              };
            }
          }
        });

        if (Object.keys(updates).length > 0) {
          // 添加调试日志，记录更新的主题
          //console.log(`[MQTT] 收到 ${Object.keys(updates).length} 个主题更新，延迟: ${Date.now() - lastDataReceived}ms`);
          
          // 使用异步处理以避免性能瓶颈
          setTimeout(() => {
            setTopicValues(prev => ({
              ...prev,
              ...updates
            }));
          }, 0);
        }
      }
      // 单条消息格式
      else if (data.type === 'message' && data.topic && data.payload !== undefined) {
        const topic = data.topic;

        // 如果该主题被订阅了，则更新值
        if (subscribedTopics.current.has(topic)) {
          // 推测数据类型
          let value: string | number | boolean = data.payload;
          let dataType: 'number' | 'boolean' | 'string' = 'string';

          // 尝试转换数值
          if (!isNaN(Number(data.payload))) {
            value = Number(data.payload);
            dataType = 'number';
          }
          // 尝试转换布尔值
          else if (data.payload.toLowerCase() === 'true' || data.payload.toLowerCase() === 'false') {
            value = data.payload.toLowerCase() === 'true';
            dataType = 'boolean';
          }

          //console.log(`[MQTT] 收到单条消息: ${topic} = ${value}, 延迟: ${Date.now() - lastDataReceived}ms`);
          
          // 使用异步处理更新状态
          setTimeout(() => {
            setTopicValues(prev => ({
              ...prev,
              [topic]: {
                topic,
                value,
                timestamp: Date.now(),
                dataType,
                raw: data.payload
              }
            }));
          }, 0);
        }
      }
      // 处理系统消息
      else if (data.type === 'system') {
        //console.log('[MQTT] 系统消息:', data.message);
        
        // 如果是重连消息，重新发送所有订阅
        if (data.message === 'reconnected' && subscribedTopics.current.size > 0) {
          //console.log('[MQTT] 连接已恢复，重新发送所有订阅');
          sendSubscriptions();
        }
        // 处理心跳回复
        else if (data.type === 'pong') {
          //console.log('[MQTT] 收到心跳响应');
        }
      }
    };

    // 尝试建立WebSocket连接
    const connectWebSocket = () => {
      //console.log(`[MQTT] 尝试连接WebSocket, 尝试次数: ${reconnectAttempts + 1}`);
      
      // 连接WebSocket
      const { ws, cleanup } = createMqttWebSocket(handleMessage, (ws) => {
        //console.log('[MQTT] WebSocket连接成功，订阅主题数量:', subscribedTopics.current.size);
        // 重置重连计数
        // reconnectAttempts = 0;
        
        // 设置WebSocket已准备好
        wsReadyRef.current = true;
        
        // 如果等待队列中有主题，立即处理
        if (pendingSubscriptionTopics.current.size > 0 || pendingUnsubscriptionTopics.current.size > 0) {
          //console.log('[MQTT] WebSocket连接后处理待处理的订阅，订阅队列大小:', pendingSubscriptionTopics.current.size);
          processPendingSubscriptions();
        }
        // 否则发送所有活跃订阅
        else if (subscribedTopics.current.size > 0 && !pendingBatchSent.current) {
          //console.log('[MQTT] WebSocket连接后重新发送所有活跃订阅');
          sendSubscriptions();
        }
        
        // 设置定期心跳检测，每15秒发送一次心跳
        pingTimer = setInterval(() => {
          if (ws.readyState === WebSocket.OPEN) {
            //console.log('[MQTT] 发送心跳');
            ws.send(JSON.stringify({ type: 'ping' }));
          }
        }, 15000);
        
        // 设置连接状态检查，检测数据是否长时间未更新
        connectionCheckTimer = setInterval(() => {
          const currentTime = Date.now();
          const dataAge = currentTime - lastDataReceived;
          
          // 如果超过30秒没有收到数据，且有活跃订阅，则尝试重新订阅
          if (dataAge > 30000 && subscribedTopics.current.size > 0) {
            //console.log(`[MQTT] 长时间未收到数据 (${dataAge}ms)，重新发送订阅`);
            sendSubscriptions();
          }
        }, 20000);
      });

      wsRef.current = { ws, cleanup };
    };

    // 初始连接
    connectWebSocket();

    // 组件卸载时清理WebSocket和计时器
    return () => {
      if (pingTimer) {
        clearInterval(pingTimer);
      }
      
      if (connectionCheckTimer) {
        clearInterval(connectionCheckTimer);
      }
      
      if (wsRef.current) {
        wsRef.current.cleanup();
        wsRef.current = null;
      }
    };
  }, [sendSubscriptions, processPendingSubscriptions]);

  // 批量订阅主题 - 改进批量处理
  const batchSubscribe = useCallback((topics: string[]) => {
    if (!topics || topics.length === 0) return;

    // 过滤掉空主题
    const validTopics = topics.filter(topic => topic && topic.trim() !== '');
    
    if (validTopics.length === 0) return;
    
    // 日志包含主题列表，帮助调试
    //console.log('[MQTT] 添加到订阅队列:', validTopics);
    
    // 添加到待处理队列
    validTopics.forEach(topic => {
      pendingSubscriptionTopics.current.add(topic);
      // 如果同一主题在取消订阅队列中，则移除
      pendingUnsubscriptionTopics.current.delete(topic);
    });
    
    // 如果WebSocket已准备好并且没有正在处理的批处理，立即处理
    if (wsReadyRef.current && wsRef.current?.ws.readyState === WebSocket.OPEN && !subscriptionDebounceTimer.current) {
      scheduleSubscriptionProcessing();
    } else {
      // 否则安排延迟处理
      scheduleSubscriptionProcessing();
    }
  }, [scheduleSubscriptionProcessing]);

  // 批量取消订阅
  const batchUnsubscribe = useCallback((topics: string[]) => {
    if (!topics || topics.length === 0) return;
    
    // 过滤有效主题
    const validTopics = topics.filter(Boolean);
    
    if (validTopics.length === 0) return;
    
    //console.log('[MQTT] 添加到取消订阅队列:', validTopics);
    
    // 添加到待处理取消订阅队列
    validTopics.forEach(topic => {
      pendingUnsubscriptionTopics.current.add(topic);
      // 如果同一主题在订阅队列中，则移除
      pendingSubscriptionTopics.current.delete(topic);
    });
    
    // 安排处理
    scheduleSubscriptionProcessing();
  }, [scheduleSubscriptionProcessing]);

  // 订阅单个主题 - 内部使用批量机制
  const subscribeToTopic = useCallback((topic: string) => {
    if (!topic) return;
    
    // 使用批量订阅方法实现
    batchSubscribe([topic]);
  }, [batchSubscribe]);

  // 取消订阅单个主题 - 内部使用批量机制
  const unsubscribeFromTopic = useCallback((topic: string) => {
    if (!topic) return;
    
    // 使用批量取消订阅方法实现
    batchUnsubscribe([topic]);
  }, [batchUnsubscribe]);

  // 获取主题值
  const getTopicValue = useCallback((topic: string): MqttValue | null => {
    return topicValues[topic] || null;
  }, [topicValues]);

  // 发布消息到主题
  const publishToTopic = useCallback(async (topic: string, value: string) => {
    if (wsRef.current && wsRef.current.ws.readyState === WebSocket.OPEN) {
      wsRef.current.ws.send(JSON.stringify({
        type: 'publish',
        topic,
        payload: value
      }));
    }
  }, []);

  // 提供上下文值
  const contextValue: MqttDataContextState = {
    topicValues,
    subscribeToTopic,
    unsubscribeFromTopic,
    getTopicValue,
    publishToTopic,
    batchSubscribe,
    batchUnsubscribe
  };

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      if (subscriptionDebounceTimer.current) {
        clearTimeout(subscriptionDebounceTimer.current);
      }
    };
  }, []);

  return (
    <MqttDataContext.Provider value={contextValue}>
      {children}
    </MqttDataContext.Provider>
  );
};

// 自定义钩子，用于在组件中使用MQTT数据
export const useMqttData = () => useContext(MqttDataContext);

// 便捷钩子，用于订阅单个主题并获取其值
export const useTopicValue = (topic: string | undefined | null): MqttValue | null => {
  const { batchSubscribe, topicValues, getTopicValue } = useMqttData();
  const [value, setValue] = useState<MqttValue | null>(null);
  const topicRef = useRef<string | null>(null);
  const initialized = useRef<boolean>(false);

  // 只在主题改变时重新订阅
  useEffect(() => {
    if (!topic) {
      topicRef.current = null;
      setValue(null);
      return;
    }

    // 如果主题变化了，重置状态
    if (topicRef.current !== topic) {
      topicRef.current = topic;
      setValue(null);
    }

    // 使用批量订阅方式来订阅单个主题
    batchSubscribe([topic]);
    initialized.current = true;

    // 立即获取当前值
    const currentValue = getTopicValue(topic);
    if (currentValue) {
      setValue(currentValue);
    }

    // 不需要取消订阅，因为HMICanvas.tsx会管理订阅生命周期
  }, [topic, batchSubscribe, getTopicValue]);

  // 监听topicValues变化以获取最新值
  useEffect(() => {
    if (!topic || !initialized.current) return;
    
    const currentValue = topicValues[topic];
    if (currentValue && (!value || currentValue.timestamp > value.timestamp)) {
      setValue(currentValue);
    }
  }, [topic, topicValues, value]);

  return value;
};

// 批量订阅主题并获取所有值
export const useMultipleTopicValues = (topics: string[]): { [topic: string]: MqttValue | null } => {
  const { batchSubscribe, topicValues } = useMqttData();
  const [values, setValues] = useState<{ [topic: string]: MqttValue | null }>({});
  const topicsRef = useRef<string[]>([]);
  const initialized = useRef<boolean>(false);

  // 仅在主题列表变化时重新订阅
  useEffect(() => {
    const validTopics = topics.filter(Boolean);
    
    // 检查主题列表是否变化
    const topicsChanged = !initialized.current || 
      validTopics.length !== topicsRef.current.length || 
      validTopics.some((t, i) => t !== topicsRef.current[i]);
    
    if (validTopics.length === 0) {
      topicsRef.current = [];
      setValues({});
      return;
    }

    if (topicsChanged) {
      topicsRef.current = [...validTopics];
      
      // 批量订阅
      batchSubscribe(validTopics);
      initialized.current = true;
      
      // 初始化值
      const initialValues: { [topic: string]: MqttValue | null } = {};
      validTopics.forEach(topic => {
        initialValues[topic] = topicValues[topic] || null;
      });
      setValues(initialValues);
    }

    // 不需要取消订阅，因为HMICanvas.tsx会管理订阅生命周期
  }, [topics, batchSubscribe, topicValues]);

  // 当主题值变化时更新
  useEffect(() => {
    if (!initialized.current || topicsRef.current.length === 0) return;

    const updatedValues: { [topic: string]: MqttValue | null } = {};
    let hasUpdates = false;

    topicsRef.current.forEach(topic => {
      const currentValue = topicValues[topic] || null;
      const previousValue = values[topic];
      
      if (!previousValue && currentValue) {
        updatedValues[topic] = currentValue;
        hasUpdates = true;
      } else if (previousValue && currentValue && previousValue.timestamp !== currentValue.timestamp) {
        updatedValues[topic] = currentValue;
        hasUpdates = true;
      } else {
        updatedValues[topic] = previousValue;
      }
    });

    if (hasUpdates) {
      setValues(updatedValues);
    }
  }, [topicValues, values]);

  return values;
};

export default MqttDataContext; 