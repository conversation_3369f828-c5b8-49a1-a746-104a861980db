import { Navigate } from 'react-router-dom';
import { getUserInfoFromStorage } from './UserContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string[];
}

export default function ProtectedRoute({ children, requiredRole }: ProtectedRouteProps) {
  const token = localStorage.getItem('beacon_token');
  const userInfo = getUserInfoFromStorage();
  // 检查是否登录
  if (!token) {
    return <Navigate to="/login" replace />;
  }

  // 如果需要角色检查且用户没有所需角色
  if (requiredRole && (!userInfo.role || !requiredRole.includes(userInfo.role))) {
    return <Navigate to="/unauthorized" replace />;
  }

  return <>{children}</>;
}