import React, { ReactNode, createContext, useState, useContext } from 'react';
import { 
    Dialog, 
    DialogTitle, 
    DialogContent, 
    IconButton, 
    DialogActions, 
    Button, 
    Box, 
    Typography,
    Divider
} from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { alpha } from '@mui/material/styles';
import { t } from 'i18next';

interface DialogContextType {
    openDialog: (content: React.ReactNode, title: string, icon?: React.ReactNode) => void;
    closeDialog: () => void;
    confirm: (title: string, content: React.ReactNode, icon?: React.ReactNode) => Promise<boolean>;
}

export const DialogContext = createContext<DialogContextType | null>(null);

// 添加useDialogContext钩子，使DialogContext更容易使用
export const useDialogContext = () => {
    const context = useContext(DialogContext);
    if (!context) {
        throw new Error('useDialogContext必须在GlobalDialogProvider内部使用');
    }
    return context;
};

type GlobalDialogProviderProps = {
    children: ReactNode;
};

export const GlobalDialogProvider = ({ children }: GlobalDialogProviderProps) => {
    const theme = useTheme();
    const [open, setOpen] = useState(false);
    const [content, setContent] = useState<React.ReactNode>(null);
    const [title, setTitle] = useState('');
    const [icon, setIcon] = useState<React.ReactNode>(null);
    const [isConfirm, setIsConfirm] = useState(false);
    const [resolveConfirm, setResolveConfirm] = useState<((value: boolean) => void) | null>(null);

    const openDialog = (content: React.ReactNode, title: string, icon?: React.ReactNode) => {
        setContent(content);
        setTitle(title);
        setIcon(icon);
        setOpen(true);
        setIsConfirm(false);
    };

    const closeDialog = () => {
        setOpen(false);
        if (resolveConfirm) {
            resolveConfirm(false);
            setResolveConfirm(null);
        }
    };

    const confirm = (title: string, content: React.ReactNode, icon?: React.ReactNode): Promise<boolean> => {
        return new Promise((resolve) => {
            setTitle(title);
            setContent(content);
            setIcon(icon);
            setIsConfirm(true);
            setResolveConfirm(() => resolve);
            setOpen(true);
        });
    };

    const handleConfirm = () => {
        setOpen(false);
        if (resolveConfirm) {
            resolveConfirm(true);
            setResolveConfirm(null);
        }
    };

    const handleExited = () => {
        setContent(null);
        setTitle('');
        setIcon(null);
        setIsConfirm(false);
    };

    return (
        <DialogContext.Provider value={{ openDialog, closeDialog, confirm }}>
            {children}
            <Dialog
                open={open}
                onClose={closeDialog}
                TransitionProps={{
                    onExited: handleExited,
                    onExiting: () => {
                        document.body.focus();
                    }
                }}
                maxWidth="sm"
                fullWidth
                PaperProps={{
                    sx: {
                        borderRadius: 3,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.08)}`,
                        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                        maxWidth: '90vw',
                        maxHeight: '90vh',
                        width: 'auto',
                        height: 'auto',
                        overflow: 'hidden'
                    },
                }}
                disableRestoreFocus
                disablePortal={false}
                keepMounted={false}
                disableEnforceFocus={false}
                hideBackdrop={false}
                aria-modal={true}
            >
                {title !== "" && (
                    <>
                        <DialogTitle 
                            sx={{ 
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                pb: 1,
                                backgroundColor: alpha(theme.palette.primary.main, 0.03),
                            }}
                        >
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                {icon && icon}
                                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                                    {title}
                                </Typography>
                            </Box>
                            <IconButton 
                                aria-label="close"
                                onClick={closeDialog}
                                sx={{
                                    backgroundColor: alpha(theme.palette.primary.main, 0.05),
                                    '&:hover': {
                                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                        transform: 'scale(1.1)',
                                    },
                                    transition: 'all 0.2s ease'
                                }}
                            >
                                <CloseIcon fontSize="small" />
                            </IconButton>
                        </DialogTitle>
                        <Divider />
                    </>
                )}
                <DialogContent 
                    sx={{
                        p: 0,
                        display: 'flex',
                        flexDirection: 'column',
                        overflow: 'auto'
                    }}
                >
                    {content}
                </DialogContent>
                {isConfirm && (
                    <DialogActions sx={{ 
                        px: 1, 
                        pb: 1,
                        backgroundColor: alpha(theme.palette.background.default, 0.5),
                        borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}>
                        <Button 
                            onClick={closeDialog} 
                            variant="outlined"
                            sx={{ 
                                borderRadius: 2,
                                textTransform: 'none',
                                fontWeight: 500,
                                '&:hover': {
                                    backgroundColor: alpha(theme.palette.text.primary, 0.05)
                                }
                            }}
                        >
                            {t('common.cancel')}
                        </Button>
                        <Button 
                            onClick={handleConfirm} 
                            variant="contained" 
                            color="primary"
                            sx={{ 
                                borderRadius: 2,
                                textTransform: 'none',
                                fontWeight: 500,
                                boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
                                '&:hover': {
                                    boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.4)}`,
                                    transform: 'translateY(-2px)'
                                },
                                transition: 'all 0.2s ease'
                            }}
                        >
                            {t('common.confirm')}
                        </Button>
                    </DialogActions>
                )}
            </Dialog>
        </DialogContext.Provider>
    );
};