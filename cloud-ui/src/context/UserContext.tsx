import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

// Define user info structure
interface UserInfo {
  username: string;
  name: string;
  email: string;
  avatar: string;
  role: string;
  enterprise_id: string;
}

// Default values when user is not logged in
const defaultUserInfo: UserInfo = {
  username: '',
  name: 'User',
  email: '',
  avatar: '/logo.png',
  role: '',
  enterprise_id: ''
};

// Define context type
interface UserContextType {
  userInfo: UserInfo;
  updateUserInfo: (updates: Partial<UserInfo>) => void;
  isLoggedIn: boolean;
  logout: () => void;
}

// Create the context
const UserContext = createContext<UserContextType | null>(null);

// Storage key
const USER_INFO_STORAGE_KEY = 'beacon_user';

// Helper to get user info from localStorage
export const getUserInfoFromStorage = (): UserInfo => {
  const storedInfo = localStorage.getItem(USER_INFO_STORAGE_KEY);
  if (storedInfo) {
    try {
      return JSON.parse(storedInfo);
    } catch (e) {
      console.error('Failed to parse user info from localStorage:', e);
    }
  }
  
  // If no stored info exists or it's invalid, check for individual items (for backward compatibility)
  return {
    username: localStorage.getItem('beacon_c_name') || defaultUserInfo.username,
    name: localStorage.getItem('beacon_c_display_name') || defaultUserInfo.name,
    email: localStorage.getItem('beacon_c_email') || defaultUserInfo.email,
    avatar: localStorage.getItem('beacon_c_avatar') || defaultUserInfo.avatar,
    role: localStorage.getItem('beacon_c_role') || defaultUserInfo.role,
    enterprise_id: localStorage.getItem('beacon_enterpriseid') || defaultUserInfo.enterprise_id
  };
};

// Hook to use the user context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// For backward compatibility with code that might still use avatar-specific hooks
export const useAvatar = () => {
  const { userInfo, updateUserInfo } = useUser();
  return {
    avatar: userInfo.avatar,
    updateAvatar: (newAvatar: string) => updateUserInfo({ avatar: newAvatar })
  };
};

interface UserProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<UserProviderProps> = ({ children }) => {
  // Initialize state from localStorage
  const [userInfo, setUserInfo] = useState<UserInfo>(getUserInfoFromStorage);
  
  // Determine if user is logged in based on whether token exists
  const [isLoggedIn, setIsLoggedIn] = useState<boolean>(
    () => !!localStorage.getItem('beacon_token')
  );
  
  // Update user info and persist to localStorage
  const updateUserInfo = (updates: Partial<UserInfo>) => {
    setUserInfo(prev => {
      const updated = { ...prev, ...updates };
      localStorage.setItem(USER_INFO_STORAGE_KEY, JSON.stringify(updated));
      return updated;
    });
  };
  
  // Handle logout - clear all user data
  const logout = () => {
    localStorage.clear();
    setUserInfo(defaultUserInfo);
    setIsLoggedIn(false);
    window.location.href = '/';
  };
  
  // Sync with localStorage if changed in another tab/window
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === USER_INFO_STORAGE_KEY && e.newValue) {
        try {
          setUserInfo(JSON.parse(e.newValue));
        } catch (error) {
          console.error('Failed to parse updated user info:', error);
        }
      } else if (e.key === 'beacon_token') {
        setIsLoggedIn(!!e.newValue);
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <UserContext.Provider value={{ userInfo, updateUserInfo, isLoggedIn, logout }}>
      {children}
    </UserContext.Provider>
  );
};

// For backward compatibility
export const AvatarProvider = UserProvider;
