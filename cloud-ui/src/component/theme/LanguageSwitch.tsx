import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { 
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  alpha,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { GB, CN } from 'country-flag-icons/react/3x2';
import { TranslateRounded } from '@mui/icons-material';

const LanguageSwitch: React.FC = () => {
  const { i18n } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const theme = useTheme();
  const isMdUp = useMediaQuery(theme.breakpoints.up('md'));

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLanguageChange = (lang: string) => {
    i18n.changeLanguage(lang);
    handleClose();
  };

  return (
    <>
      <Menu
        id="language-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        onClick={handleClose}
        PaperProps={{
          sx: {
            mt: { xs: 1.5, md: 0 },
            mb: { xs: 0, md: 1.5 },
            minWidth: 180,
            borderRadius: 2,
            boxShadow: 3,
            '& .MuiMenuItem-root': {
              px: 2,
              py: 1,
              borderRadius: 1,
              mx: 0.5,
              my: 0.25,
              '&:hover': {
                bgcolor: (theme) => alpha(theme.palette.primary.main, 0.08)
              }
            }
          }
        }}
        transformOrigin={{
          horizontal: 'right',
          vertical: isMdUp ? 'bottom' : 'top'
        }}
        anchorOrigin={{
          horizontal: 'right',
          vertical: isMdUp ? 'top' : 'bottom'
        }}
      >
        <MenuItem
          onClick={() => handleLanguageChange('en')}
          selected={i18n.language === 'en'}
        >
          <ListItemIcon sx={{ minWidth: 36 }}>
            <GB style={{ width: 24, height: 16 }} />
          </ListItemIcon>
          <ListItemText>English</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => handleLanguageChange('zh')}
          selected={i18n.language === 'zh'}
        >
          <ListItemIcon sx={{ minWidth: 36 }}>
            <CN style={{ width: 24, height: 16 }} />
          </ListItemIcon>
          <ListItemText>中文</ListItemText>
        </MenuItem>
      </Menu>
      <IconButton
        onClick={handleClick}
        size="small"
        aria-controls={open ? 'language-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
      >
        <TranslateRounded fontSize="small" />
      </IconButton>
    </>
  );
};

export default LanguageSwitch; 