import { IconButton, IconButtonProps } from '@mui/material';
import DarkModeRoundedIcon from '@mui/icons-material/DarkModeRounded';
import LightModeRoundedIcon from '@mui/icons-material/LightModeRounded';
import { useThemeMode } from './theme'; // 引入自定义 Hook

export default function ColorSchemeToggle(props: IconButtonProps) {
  const { onClick, sx, ...other } = props;
  const { mode, toggleColorScheme } = useThemeMode(); // 获取主题模式和切换函数

  return (
    <IconButton
      aria-label="toggle light/dark mode"
      size="small"
      onClick={(event) => {
        toggleColorScheme(); // 切换主题
        onClick?.(event);
      }}
      {...other}
    >
      {/* 根据主题模式显示对应的图标 */}
      {mode === 'light' ? (
        <DarkModeRoundedIcon fontSize="small" />
      ) : (
        <LightModeRoundedIcon fontSize="small" />
      )}
    </IconButton>
  );
}