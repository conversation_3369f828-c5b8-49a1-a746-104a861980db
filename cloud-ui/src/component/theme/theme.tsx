import React, { createContext, useContext, useState } from 'react';
import { createTheme, ThemeProvider as MuiThemeProvider } from '@mui/material/styles';

type ThemeMode = 'light' | 'dark';

// 创建 Context
const ThemeContext = createContext<{
  mode: ThemeMode;
  toggleColorScheme: () => void;
} | null>(null);

// 自定义 Hook
export const useThemeMode = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useThemeMode must be used within a ThemeProvider');
  }
  return context;
};

// ThemeProvider 组件
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // 从 localStorage 中读取主题模式，如果不存在则默认为 'light'
  const [mode, setMode] = useState<ThemeMode>(() => {
    const savedMode = localStorage.getItem('themeMode') as ThemeMode;
    return savedMode || 'light';
  });

  // 切换主题
  const toggleColorScheme = () => {
    setMode((prevMode) => {
      const newMode = prevMode === 'light' ? 'dark' : 'light';
      localStorage.setItem('themeMode', newMode); // 将新主题模式存储到 localStorage
      return newMode;
    });
  };

  // 创建主题
  const theme = createTheme({
    palette: {
      mode, // 动态主题模式
    },
  });

  return (
    <ThemeContext.Provider value={{ mode, toggleColorScheme }}>
      <MuiThemeProvider theme={theme}>{children}</MuiThemeProvider>
    </ThemeContext.Provider>
  );
};