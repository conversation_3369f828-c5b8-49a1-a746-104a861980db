import React from 'react';
import ReactECharts from 'echarts-for-react';
import { Card, CardContent, Stack, Typography, Chip } from '@mui/material';

interface Series {
    id: string;
    label: string;
    data: number[];
    stack?: string;
}

interface XAxis {
    data: string[];
    scaleType?: 'band' | 'linear';
}

interface EChartsBarProps {
    title: string;
    subtitle?: string;
    mainMetric?: string | number;
    chipLabel?: string;
    chipColor?: 'success' | 'error' | 'warning' | 'info' | 'primary';
    series: Series[];
    xAxis: XAxis;
    height?: number;
}

const EChartsBar: React.FC<EChartsBarProps> = ({
    title,
    subtitle,
    mainMetric,
    chipLabel,
    chipColor = 'success',
    series,
    xAxis,
    height = 350
}) => {
    const option = {
        grid: {
            left: '3%',
            right: '4%',
            top: '8%',
            bottom: '15%',
            containLabel: true
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: series.map(s => s.label),
            bottom: 0
        },
        xAxis: {
            type: 'category',
            data: xAxis.data,
            axisLabel: {
                interval: 0,
                rotate: 0
            }
        },
        yAxis: {
            type: 'value'
        },
        series: series.map(s => ({
            name: s.label,
            type: 'bar',
            stack: s.stack,
            emphasis: {
                focus: 'series'
            },
            data: s.data,
            itemStyle: {
                borderRadius: [4, 4, 0, 0]
            },
            barMaxWidth: 50
        }))
    };

    return (
        <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent>
                <Stack spacing={3}>
                    <Stack spacing={1}>
                        <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="space-between"
                            spacing={1}
                        >
                            <Typography variant="subtitle2">{title}</Typography>
                            {chipLabel && (
                                <Chip
                                    size="small"
                                    label={chipLabel}
                                    color={chipColor}
                                />
                            )}
                        </Stack>

                        {subtitle && (
                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                {subtitle}
                            </Typography>
                        )}

                        {mainMetric && (
                            <Typography variant="h4" sx={{ my: 1 }}>
                                {mainMetric}
                            </Typography>
                        )}
                    </Stack>

                    <ReactECharts
                        option={option}
                        style={{ height }}
                        notMerge={true}
                        lazyUpdate={true}
                    />
                </Stack>
            </CardContent>
        </Card>
    );
};

export default EChartsBar; 