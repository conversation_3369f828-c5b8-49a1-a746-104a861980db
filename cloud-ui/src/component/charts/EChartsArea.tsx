import React from 'react';
import ReactECharts from 'echarts-for-react';
import { Card, CardContent, Stack, Typography, Chip } from '@mui/material';

interface Series {
    id: string;
    label: string;
    data: number[];
}

interface XAxis {
    data: Date[];
    tickInterval?: (index: number) => boolean;
}

interface EChartsAreaProps {
    title: string;
    subtitle?: string;
    mainMetric?: string | number;
    chipLabel?: string;
    chipColor?: 'success' | 'error' | 'warning' | 'info' | 'primary';
    series: Series[];
    xAxis: XAxis;
    height?: number;
}

const EChartsArea: React.FC<EChartsAreaProps> = ({
    title,
    subtitle,
    mainMetric,
    chipLabel,
    chipColor = 'success',
    series,
    xAxis,
    height = 350
}) => {
    const option = {
        grid: {
            left: '3%',
            right: '4%',
            top: '15%',
            bottom: '3%',
            containLabel: true
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                label: {
                    backgroundColor: '#6a7985'
                }
            }
        },
        legend: {
            data: series.map(s => s.label),
            top: 0,
            textStyle: {
                fontSize: 12
            }
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xAxis.data.map(date => date.toLocaleTimeString()),
            axisLabel: {
                interval: (index: number) => xAxis.tickInterval ? xAxis.tickInterval(index) : true
            }
        },
        yAxis: {
            type: 'value',
            scale: true
        },
        series: series.map((s, _) => ({
            name: s.label,
            type: 'line',
            smooth: true,
            showSymbol: false,
            areaStyle: {
                opacity: 0.1
            },
            emphasis: {
                focus: 'series'
            },
            data: s.data,
            lineStyle: {
                width: 2
            }
        }))
    };

    return (
        <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent>
                <Stack spacing={3}>
                    <Stack spacing={1}>
                        <Stack
                            direction="row"
                            alignItems="center"
                            justifyContent="space-between"
                            spacing={1}
                        >
                            <Typography variant="subtitle2">{title}</Typography>
                            {chipLabel && (
                                <Chip
                                    size="small"
                                    label={chipLabel}
                                    color={chipColor}
                                />
                            )}
                        </Stack>

                        {subtitle && (
                            <Typography variant="caption" sx={{ color: 'text.secondary' }}>
                                {subtitle}
                            </Typography>
                        )}

                        {mainMetric && (
                            <Typography variant="h4" sx={{ my: 1 }}>
                                {mainMetric}
                            </Typography>
                        )}
                    </Stack>

                    <ReactECharts
                        option={option}
                        style={{ height }}
                        notMerge={true}
                        lazyUpdate={true}
                    />
                </Stack>
            </CardContent>
        </Card>
    );
};

export default EChartsArea; 