import React, { useState } from 'react';
import { Box, Button, Stack, Typography, Input, CircularProgress } from '@mui/material';
import { useSnackbar } from '../../context/SnackbarContext';

interface VerificationCodeInputProps {
  onSubmit: (code: string) => Promise<void>;
  isLoading?: boolean;
  title?: string;
}

export default function VerificationCodeInput({ onSubmit, isLoading = false, title = "Enter the 6-digit verification code" }: VerificationCodeInputProps) {
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [submitting, setSubmitting] = useState(false);
  const inputRefs = Array(6).fill(0).map(() => React.useRef<HTMLInputElement>(null));
  const { showSnackbar } = useSnackbar();

  const handleChange = (index: number, value: string) => {
    if (value.length > 1) return;
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);
    
    if (value && index < 5) {
      inputRefs[index + 1].current?.focus();
    }
  };

  const handleKeyDown = (index: number, e: any) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs[index - 1].current?.focus();
    }
  };

  const handleSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const verificationCode = code.join('');
    if (verificationCode.length !== 6) {
      showSnackbar('Please enter all 6 digits', 'warning');
      return;
    }

    try {
      setSubmitting(true);
      await onSubmit(verificationCode);
    } catch (error) {
      console.error('Verification failed:', error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <Box sx={{p:2, minWidth:'300px'}}>
      <form onSubmit={handleSubmit} autoComplete="off">
        <Stack spacing={2}>
          <Typography variant="body2" sx={{ mb: 2 }}>
            {title}
          </Typography>
          <Stack direction="row" spacing={1} justifyContent="center">
            {code.map((digit, index) => (
              <Input
                key={index}
                inputRef={inputRefs[index]}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                disabled={submitting}
                sx={{
                  fontSize: '20px',
                  width: '50px',
                  height: '50px',
                  textAlign: 'center',
                  '& input': { textAlign: 'center' }
                }}
                inputProps={{
                  maxLength: 1,
                  style: { textAlign: 'center' }
                }}
              />
            ))}
          </Stack>
          <Button 
            variant="contained" 
            type="submit" 
            disabled={isLoading || submitting || code.join('').length !== 6}
            sx={{ mt: 2 }}
          >
            {submitting ? (
              <Stack direction="row" spacing={1} alignItems="center">
                <CircularProgress size={20} color="inherit" />
                <span>Verifying...</span>
              </Stack>
            ) : (
              'Verify'
            )}
          </Button>
        </Stack>
      </form>
    </Box>
  );
} 