import React, { useState } from 'react';
import { Box, Stack, Alert } from '@mui/material';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs, { Dayjs } from 'dayjs';

interface ExportDialogContentProps {
    onValidate?: (startTime: Dayjs | null, endTime: Dayjs | null) => string | null;
}

const ExportDialogContent: React.FC<ExportDialogContentProps> = ({ onValidate }) => {
    const [startTime, setStartTime] = useState<Dayjs | null>(dayjs().subtract(24, 'hour'));
    const [endTime, setEndTime] = useState<Dayjs | null>(dayjs());
    const [error, setError] = useState<string>('');

    const handleTimeChange = (newStartTime: Dayjs | null, newEndTime: Dayjs | null) => {
        setStartTime(newStartTime);
        setEndTime(newEndTime);
        setError('');

        if (onValidate) {
            const validationError = onValidate(newStartTime, newEndTime);
            if (validationError) {
                setError(validationError);
            }
        } else {
            // Default validation
            if (!newStartTime || !newEndTime) {
                setError('Please select both start and end times');
            } else if (newEndTime.isBefore(newStartTime)) {
                setError('End time must be after start time');
            }
        }
    };

    if (!startTime || !endTime) {
        return null;
    }

    return (
        <Box sx={{ p:2 }}>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
                <Stack spacing={3} direction="row">
                    <DateTimePicker
                        label="Start Time"
                        value={startTime}
                        onChange={(newValue) => handleTimeChange(newValue, endTime)}
                    />
                    <DateTimePicker
                        label="End Time"
                        value={endTime}
                        onChange={(newValue) => handleTimeChange(startTime, newValue)}
                    />
                </Stack>
            </LocalizationProvider>
            {error && (
                <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                </Alert>
            )}
        </Box>
    );
};

export default ExportDialogContent; 