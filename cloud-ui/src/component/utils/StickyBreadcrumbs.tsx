import React from 'react';
import { Box, Breadcrumbs, Link, Typography } from '@mui/material';
import ChevronRightRoundedIcon from '@mui/icons-material/ChevronRightRounded';
import HomeRoundedIcon from '@mui/icons-material/HomeRounded';

interface BreadcrumbItem {
    label: string;
    href?: string;
    isCurrent?: boolean;
}

interface StickyBreadcrumbsProps {
    items: BreadcrumbItem[];
}

const StickyBreadcrumbs: React.FC<StickyBreadcrumbsProps> = ({ items }) => {
    return (
        <Box
            sx={{
                position: 'sticky',
                top: { sm: -100, md: -110 },
                bgcolor: 'background.default', // 使用 Material UI 的背景色
            }}
        >
            <Box sx={{ px: 2 }}>
                <Breadcrumbs
                    aria-label="breadcrumbs"
                    separator={<ChevronRightRoundedIcon fontSize="small" />}
                    sx={{ pl: 0 }}
                >
                    {/* 首页链接 */}
                    <Link
                        underline="none"
                        color="inherit"
                        href="/dashboard"
                        aria-label="Home"
                    >
                        <HomeRoundedIcon />
                    </Link>

                    {/* 动态生成面包屑导航项 */}
                    {items.map((item, index) =>
                        item.isCurrent ? (
                            <Typography
                                key={index}
                                color="primary"
                                sx={{ fontWeight: 500, fontSize: 12 }}
                            >
                                {item.label}
                            </Typography>
                        ) : (
                            <Link
                                key={index}
                                underline="hover"
                                color="inherit"
                                href={item.href || '#'}
                                sx={{ fontSize: 12, fontWeight: 500 }}
                            >
                                {item.label}
                            </Link>
                        )
                    )}
                </Breadcrumbs>
            </Box>
        </Box>
    );
};

export default StickyBreadcrumbs;