import { Box, Button, Typography } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import BlockIcon from '@mui/icons-material/Block';
import { useTranslation } from 'react-i18next';

export default function Unauthorized() {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        gap: 2
      }}
    >
      <BlockIcon color="error" sx={{ fontSize: 60 }} />
      <Typography variant="h4" gutterBottom>
        {t('auth.unauthorized.title')}
      </Typography>
      <Typography variant="body1" color="text.secondary" gutterBottom>
        {t('auth.unauthorized.message')}
      </Typography>
      <Button
        variant="contained"
        onClick={() => navigate('/dashboard')}
      >
        {t('auth.unauthorized.backToHome')}
      </Button>
    </Box>
  );
} 