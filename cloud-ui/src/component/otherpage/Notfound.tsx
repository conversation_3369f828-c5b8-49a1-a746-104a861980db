import { Box, Typography, Button } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

const NotFound = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      minHeight="100vh"
      textAlign="center"
      px={3}
    >
      <Typography variant="h1" component="h1" gutterBottom>
        {t('notFound.title')}
      </Typography>
      <Typography variant="h4" component="h2" gutterBottom>
        {t('notFound.subtitle')}
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        {t('notFound.message')}
      </Typography>
      <Button
        variant="contained"
        color="primary"
        onClick={() => navigate('/')}
        sx={{ mt: 2 }}
      >
        {t('common.backToHome')}
      </Button>
    </Box>
  );
};

export default NotFound;
