import React, { useState } from 'react';
import {
  Box,
  Tab,
  Paper,
  useTheme,
  alpha
} from '@mui/material';
import TabContext from '@mui/lab/TabContext';
import TabList from '@mui/lab/TabList';
import TabPanel from '@mui/lab/TabPanel';
import StickyBreadcrumbs from '../../utils/StickyBreadcrumbs';
import ManageUsers from "./ManageUsers";
import ManageEnterprise from "./ManageEnterprise";
import { useTranslation } from 'react-i18next';
import { Group, Badge } from '@mui/icons-material';

export default function ManageTabs() {
  const [value, setValue] = useState('1');
  const { t } = useTranslation();
  const theme = useTheme();

  const breadcrumbItems = [
    { label: t('routes.users'), href: '#users' },
    { label: t('routes.manage'), isCurrent: true },
  ];

  const handleChange = (_event: React.SyntheticEvent, newValue: string) => {
    setValue(newValue);
  };
    
  return (
    <Box sx={{ width: '100%' }}>
      <StickyBreadcrumbs items={breadcrumbItems} />
      <Box sx={{ width: '100%', typography: 'body1', px: { xs: 1, md: 2 }, py: 2 }}>
        <Paper 
          elevation={0} 
          sx={{ 
            borderRadius: 2,
            overflow: 'hidden',
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        >
          <TabContext value={value}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <TabList 
                onChange={handleChange}
                sx={{ 
                  '.MuiTabs-indicator': {
                    backgroundColor: theme.palette.primary.main,
                    height: 3,
                  }
                }}
              >
                <Tab 
                  label={t('users.tabs.users')} 
                  value="1" 
                  icon={<Group fontSize="small" />}
                  iconPosition="start"
                  sx={{ 
                    minHeight: 48,
                    textTransform: 'none',
                    fontWeight: value === '1' ? 600 : 400,
                    color: value === '1' ? theme.palette.primary.main : theme.palette.text.primary,
                    '&.Mui-selected': {
                      color: theme.palette.primary.main
                    }
                  }}
                />
                <Tab 
                  label={t('users.tabs.enterpriseCodes')} 
                  value="2" 
                  icon={<Badge fontSize="small" />}
                  iconPosition="start"
                  sx={{ 
                    minHeight: 48,
                    textTransform: 'none',
                    fontWeight: value === '2' ? 600 : 400,
                    color: value === '2' ? theme.palette.primary.main : theme.palette.text.primary,
                    '&.Mui-selected': {
                      color: theme.palette.primary.main
                    }
                  }}
                />
              </TabList>
            </Box>
            <TabPanel value="1" sx={{ padding: 0 }}>
              <ManageUsers />
            </TabPanel>
            <TabPanel value="2" sx={{ padding: 0 }}>
              <ManageEnterprise />
            </TabPanel>
          </TabContext>
        </Paper>
      </Box>
    </Box>
  );
}