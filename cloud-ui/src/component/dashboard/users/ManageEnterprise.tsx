import React, { useState, useEffect, useCallback, memo } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Tooltip,
  Typography,
  CircularProgress,
  Chip,
  useTheme,
  alpha,
  Stack
} from '@mui/material';
import { 
  Add as AddIcon, 
  ContentCopy as ContentCopyIcon, 
  Delete as DeleteIcon,
} from '@mui/icons-material';
import { useSnackbar } from '../../../context/SnackbarContext';
import { useDialogContext } from '../../../context/GlobalDialog';
import { useTranslation } from 'react-i18next';
import {
  EnterpriseCode,
  PaginationData,
  getEnterpriseCodes,
  generateEnterpriseCode,
  deleteEnterpriseCode
} from '../../../services/enterpriseService';

// 企业代码行组件
const CodeRow = memo(({ 
  code, 
  onCopy, 
  onDelete 
}: { 
  code: EnterpriseCode; 
  onCopy: (code: string) => void; 
  onDelete: (code: string) => void; 
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <TableRow
      hover
      sx={{ 
        '&:hover': { 
          backgroundColor: alpha(theme.palette.primary.main, 0.05),
        },
        transition: 'background-color 0.2s'
      }}
    >
      <TableCell sx={{ fontFamily: 'monospace', fontWeight: 500 }}>
        {code.Code}
      </TableCell>
      <TableCell>
        <Chip
          size="small"
          label={code.Used ? t('enterprise.statusUsed') : t('enterprise.statusAvailable')}
          color={code.Used ? 'error' : 'success'}
          sx={{ fontWeight: 500 }}
        />
      </TableCell>
      <TableCell>
        {new Date(code.CreatedAt).toLocaleString()}
      </TableCell>
      <TableCell>
        {code.UsedAt ? new Date(code.UsedAt).toLocaleString() : '-'}
      </TableCell>
      <TableCell>
        <Stack direction="row" spacing={1}>
          <Tooltip title={t('common.copy')}>
            <span>
              <IconButton
                size="small"
                onClick={() => onCopy(code.Code)}
                disabled={code.Used}
                color="primary"
                sx={{
                  backgroundColor: code.Used ? 'transparent' : alpha(theme.palette.primary.main, 0.05),
                  '&:hover': {
                    backgroundColor: code.Used ? 'transparent' : alpha(theme.palette.primary.main, 0.1),
                  }
                }}
              >
                <ContentCopyIcon fontSize="small" />
              </IconButton>
            </span>
          </Tooltip>
          <Tooltip title={t('common.delete')}>
            <IconButton
              size="small"
              onClick={() => onDelete(code.Code)}
              color="error"
              sx={{
                backgroundColor: alpha(theme.palette.error.main, 0.05),
                '&:hover': {
                  backgroundColor: alpha(theme.palette.error.main, 0.1),
                }
              }}
            >
              <DeleteIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Stack>
      </TableCell>
    </TableRow>
  );
});

export default function ManageEnterprise() {
  const { showSnackbar } = useSnackbar();
  const { confirm } = useDialogContext();
  const [codes, setCodes] = useState<EnterpriseCode[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<PaginationData>({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const { t } = useTranslation();
  const theme = useTheme();

  const fetchCodes = useCallback(async (page: number, pageSize: number) => {
    setLoading(true);
    try {
      // 使用服务获取企业代码列表
      const response = await getEnterpriseCodes(page, pageSize);
      
      // 确保我们有有效数据
      setCodes(response.codes || []);
      setPagination({
        current: response.pagination?.current || 1,
        pageSize: response.pagination?.pageSize || 10,
        total: response.pagination?.total || 0,
      });
    } catch (error: any) {
      showSnackbar(
        t('enterprise.messages.fetchError', { error: error.response?.data?.error || t('common.unknownError') }), 
        'error'
      );
      // 重置为空状态
      setCodes([]);
      setPagination({
        current: 1,
        pageSize: 10,
        total: 0,
      });
    } finally {
      setLoading(false);
    }
  }, [showSnackbar, t]);

  useEffect(() => {
    fetchCodes(pagination.current, pagination.pageSize);
  }, []);

  const handleChangePage = useCallback((_event: unknown, newPage: number) => {
    const nextPage = newPage + 1;
    setPagination(prev => ({ ...prev, current: nextPage }));
    fetchCodes(nextPage, pagination.pageSize);
  }, [fetchCodes, pagination.pageSize]);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newPageSize = parseInt(event.target.value, 10);
    setPagination(prev => ({ ...prev, pageSize: newPageSize, current: 1 }));
    fetchCodes(1, newPageSize);
  }, [fetchCodes]);

  const handleGenerateCode = useCallback(async () => {
    try {
      // 使用服务生成企业代码
      const response = await generateEnterpriseCode();
      showSnackbar(response.message || t('enterprise.messages.generateSuccess'), 'success');
      fetchCodes(pagination.current, pagination.pageSize);
    } catch (error: any) {
      showSnackbar(
        t('enterprise.messages.generateError', { error: error.response?.data?.error || t('common.unknownError') }), 
        'error'
      );
    }
  }, [fetchCodes, pagination.current, pagination.pageSize, showSnackbar, t]);

  const handleCopyCode = useCallback(async (code: string) => {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(code);
      } else {
        // 回退方案，适用于不支持剪贴板API的浏览器
        const textArea = document.createElement('textarea');
        textArea.value = code;
        textArea.style.position = 'fixed';
        textArea.style.left = '-9999px';
        textArea.style.top = '0';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
          document.execCommand('copy');
        } catch (err) {
          console.error('Fallback: Oops, unable to copy', err);
          throw new Error('Copy failed');
        } finally {
          document.body.removeChild(textArea);
        }
      }
      showSnackbar(t('enterprise.messages.codeCopied'), 'success');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      showSnackbar(t('enterprise.messages.copyError'), 'error');
    }
  }, [showSnackbar, t]);

  const handleDeleteCode = useCallback(async (code: string) => {
    const confirmed = await confirm(
      t('common.deleteConfirm.title', { item: 'code' }),
      <Box sx={{ p: 2 ,minWidth:'300px'}}>
        <Typography variant="body1" gutterBottom>
          {t('common.deleteConfirm.message', { item: 'code' })}
        </Typography>
        <Typography variant="body2" color="error">
          {t('common.deleteConfirm.warning')}
        </Typography>
      </Box>,
      <DeleteIcon sx={{ color: theme.palette.error.main }} />
    );
    
    if (!confirmed) {
      return;
    }

    try {
      // 使用服务删除企业代码
      await deleteEnterpriseCode(code);
      showSnackbar(t('common.messages.deleteSuccess', { item: 'code' }), 'success');
      fetchCodes(pagination.current, pagination.pageSize);
    } catch (error: any) {
      showSnackbar(
        t('enterprise.messages.deleteError', { error: error.response?.data?.error || t('common.unknownError') }), 
        'error'
      );
    }
  }, [confirm, fetchCodes, pagination.current, pagination.pageSize, showSnackbar, t, theme.palette.error.main]);
  return (
    <Paper elevation={0}>
      <TableContainer sx={{ 
        maxHeight: '70vh',
        opacity: loading ? 0.5 : 1,
        transition: 'opacity 0.3s'
      }}>
        <Table stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>{t('enterprise.fields.code')}</TableCell>
              <TableCell>{t('enterprise.fields.status')}</TableCell>
              <TableCell>{t('common.fields.createdAt')}</TableCell>
              <TableCell>{t('enterprise.fields.usedAt')}</TableCell>
              <TableCell>{t('common.actions')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {loading && codes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                    <CircularProgress size={40} sx={{ color: theme.palette.primary.main }} />
                  </Box>
                </TableCell>
              </TableRow>
            ) : codes.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5}>
                  <Stack direction="column" spacing={1} alignItems="center" sx={{ py: 4 }}>
                    <Typography color="text.secondary" align="center">
                      {t('enterprise.noCodes')}
                    </Typography>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<AddIcon />}
                      onClick={handleGenerateCode}
                      sx={{ mt: 1, borderRadius: 1, textTransform: 'none' }}
                    >
                      {t('enterprise.actions.createFirst')}
                    </Button>
                  </Stack>
                </TableCell>
              </TableRow>
            ) : (
              codes.map((code) => (
                <CodeRow 
                  key={code.ID} 
                  code={code}
                  onCopy={handleCopyCode}
                  onDelete={handleDeleteCode}
                />
              ))
            )}
          </TableBody>
        </Table>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center',pl:1}}>
          <Button
            size="small"
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleGenerateCode}
            disabled={loading}
            sx={{
              borderRadius: 1,
              textTransform: 'none',
              px: 2,
              py: 0.75,
              backgroundColor: theme.palette.primary.main,
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.9),
              }
            }}
          >
            {t('enterprise.actions.generate')}
          </Button>
          <TablePagination
            count={pagination.total}
            page={pagination.current - 1}
            onPageChange={handleChangePage}
            rowsPerPage={pagination.pageSize}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[5, 10, 25, 50]}
            colSpan={5}
            labelRowsPerPage={t('common.pagination.rowsPerPage')}
            labelDisplayedRows={({ from, to, count }) =>
              t('common.pagination.displayedRows', { from, to, count })
            }
          />
        </Box>
      </TableContainer>
    </Paper>
  );
} 