import React, { useState, useCallback, memo } from 'react';
import { 
  Box, 
  Button, 
  Divider, 
  FormControl, 
  Input, 
  Stack, 
  Typography, 
  Card, 
  CardActions, 
  CardContent, 
  CircularProgress,
  Avatar,
  Paper,
  TextField,
  alpha,
  useTheme,
} from '@mui/material';
import { Lock } from '@mui/icons-material';
import { useDialogContext } from "../../../context/GlobalDialog";
import { useSnackbar } from '../../../context/SnackbarContext';
import { updatePassword, updateProfile, sendVerificationCode, updateAvatar, UpdatePasswordRequest, UpdateProfileRequest } from '../../../services/userService';
import StickyBreadcrumbs from '../../utils/StickyBreadcrumbs';
import { useTranslation } from 'react-i18next';
import EmailIcon from '@mui/icons-material/Email';
import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import BadgeIcon from '@mui/icons-material/Badge';
import EditIcon from '@mui/icons-material/Edit';
import imageCompression from 'browser-image-compression';
import { useUser } from '../../../context/UserContext';

const PwdComponent = memo(() => {
  const [isLoading, setLoading] = useState(false);
  const { showSnackbar } = useSnackbar();
  const { closeDialog } = useDialogContext();
  const { t } = useTranslation();
  const theme = useTheme();

  const handleSubmit = useCallback(async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    setLoading(true);
    const formData = new FormData(event.currentTarget);
    const data: UpdatePasswordRequest = {
      old_password: formData.get('old_password') as string,
      new_password: formData.get('new_password') as string,
    };
    try {
      const response = await updatePassword(data);
      showSnackbar((response as any).message, "success");
      closeDialog();
      showSnackbar(t('profile.messages.passwordUpdateSuccess'), 'success');
      // 清除存储并在2秒后重定向到登录页
      setTimeout(() => {
        localStorage.clear();
        window.location.href = '/';
      }, 2000);
    } catch (error) {
      showSnackbar(t('profile.messages.updateFailed', { error: (error as any).response.data.error }), 'warning');
    } finally {
      setLoading(false);
    }
  }, [showSnackbar, closeDialog, t]);

  return (
    <Paper elevation={0} sx={{ minWidth: '350px', p: 3 }}>
      <form onSubmit={handleSubmit}>
        <Stack spacing={3}>
          <TextField
            name="old_password"
            label={t('profile.password.oldPassword')}
            type="password"
            variant="outlined"
            fullWidth
            required
            autoFocus
            InputProps={{
              sx: { borderRadius: 1 }
            }}
          />
          <TextField
            name="new_password"
            label={t('profile.password.newPassword')}
            type="password"
            variant="outlined"
            fullWidth
            required
            InputProps={{
              sx: { borderRadius: 1 }
            }}
          />
          <Button 
            variant="contained" 
            type="submit" 
            disabled={isLoading}
            fullWidth
            sx={{ 
              mt: 2, 
              py: 1.2,
              bgcolor: theme.palette.primary.main,
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.8)
              }
            }}
          >
            {isLoading ? (
              <Stack direction="row" spacing={1} alignItems="center">
                <CircularProgress size={20} color="inherit" />
                <span>{t('profile.password.updating')}</span>
              </Stack>
            ) : (
              t('common.update')
            )}
          </Button>
        </Stack>
      </form>
    </Paper>
  );
});

// 验证码组件
const ValidCode = memo(({ onVerifySuccess }: { onVerifySuccess: (code: string) => Promise<void> }) => {
  const { t } = useTranslation();
  const [code, setCode] = useState(['', '', '', '', '', '']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const inputRefs = Array(6).fill(0).map(() => React.useRef<HTMLInputElement>(null));
  const { showSnackbar } = useSnackbar();
  const { closeDialog } = useDialogContext();
  const theme = useTheme();

  const handleChange = useCallback((index: number, value: string) => {
    if (value.length > 1) return; // 仅允许单个字符
    
    const newCode = [...code];
    newCode[index] = value;
    setCode(newCode);
    
    // 自动聚焦下一个输入框
    if (value && index < 5) {
      inputRefs[index + 1].current?.focus();
    }
  }, [code]);

  const handleKeyDown = useCallback((index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !code[index] && index > 0) {
      inputRefs[index - 1].current?.focus();
    }
  }, [code]);

  const handleSubmit = useCallback(async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    const verificationCode = code.join('');
    if (verificationCode.length !== 6) {
      showSnackbar(t('profile.verification.enterAllDigits'), 'warning');
      return;
    }

    setIsSubmitting(true);
    try {
      await onVerifySuccess(verificationCode);
      closeDialog();
    } catch (error: any) {
      showSnackbar(t('profile.messages.updateFailed', { error: error.response?.data?.error || '验证失败' }), "warning");
    } finally {
      setIsSubmitting(false);
    }
  }, [code, onVerifySuccess, showSnackbar, t, closeDialog]);

  return (
    <Paper elevation={0} sx={{ p: 3, minWidth: '350px' }}>
      <Typography variant="h6" gutterBottom sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
        <EmailIcon sx={{ mr: 1 }} /> {t('profile.verification.title')}
      </Typography>
      <Divider sx={{ mb: 3 }} />
      
      <form onSubmit={handleSubmit} autoComplete="off">
        <Stack spacing={3}>
          <Typography variant="body2">
            {t('profile.verification.message')}
          </Typography>
          
          <Stack direction="row" spacing={1} justifyContent="center" sx={{ my: 2 }}>
            {code.map((digit, index) => (
              <Input
                key={index}
                inputRef={inputRefs[index]}
                value={digit}
                onChange={(e) => handleChange(index, e.target.value)}
                onKeyDown={(e) => handleKeyDown(index, e)}
                sx={{
                  width: '40px',
                  height: '48px',
                  textAlign: 'center',
                  '& input': { textAlign: 'center', fontSize: '1.2rem' },
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 1
                }}
                inputProps={{
                  maxLength: 1,
                  style: { textAlign: 'center' }
                }}
              />
            ))}
          </Stack>
          
          <Button 
            variant="contained" 
            type="submit" 
            disabled={isSubmitting || code.join('').length !== 6}
            fullWidth
            sx={{ 
              mt: 2, 
              py: 1.2,
              bgcolor: theme.palette.primary.main,
              '&:hover': {
                bgcolor: alpha(theme.palette.primary.main, 0.8)
              }
            }}
          >
            {isSubmitting ? (
              <CircularProgress size={20} color="inherit" />
            ) : (
              t('profile.verification.verify')
            )}
          </Button>
        </Stack>
      </form>
    </Paper>
  );
});

export default function MyProfile() {
  const { userInfo, updateUserInfo } = useUser();
  const { username, name: displayName, email: userEmail, role, avatar } = userInfo;
  
  const [name, setName] = useState(displayName || '');
  const [currentEmail, setEmail] = useState(userEmail || '');
  const { showSnackbar } = useSnackbar();
  const [isLoading, setLoading] = useState(false);
  const { t } = useTranslation();
  const theme = useTheme();
  const [avatarUploading, setAvatarUploading] = useState(false);
  
  const breadcrumbItems = [
    { label: t('routes.users'), href: '#users' },
    { label: t('routes.myProfile'), isCurrent: true },
  ];
  
  const { openDialog } = useDialogContext();

  const handleUpdatePwd = useCallback(() => {
    openDialog(<PwdComponent />, t('profile.password.title'),<Lock sx={{ mr: 1 }} />);
  }, [openDialog, t]);

  const handleUpdateProfile = useCallback(async (verificationCode?: string) => {
    setLoading(true);
    try {
      const data: UpdateProfileRequest = { name };
      
      if (currentEmail !== userEmail) {
        if (!verificationCode) {
          showSnackbar(t('profile.messages.emailVerificationRequired'), 'warning');
          setLoading(false);
          return;
        }
        data.email = currentEmail;
      }

      const response = await updateProfile(data, verificationCode);
      showSnackbar((response as any).message, 'success');
      
      // Update user information in context
      updateUserInfo({
        name,
        email: data.email || userEmail
      });
      
    } catch (error: any) {
      showSnackbar(t('profile.messages.updateFailed', { error: error.response?.data?.error }), 'warning');
    } finally {
      setLoading(false);
    }
  }, [currentEmail, name, showSnackbar, t, updateUserInfo, userEmail]);

  // 修改验证码发送处理
  const handleSendCode = useCallback(async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    if (currentEmail === userEmail) {
      return; // 如果邮箱没有修改，不需要发送验证码
    }
    
    setLoading(true);
    try {
      await sendVerificationCode(currentEmail);
      showSnackbar(t('profile.verification.emailMessage'));
      openDialog(
        <ValidCode onVerifySuccess={handleUpdateProfile} />, 
        t('profile.verification.title')
      );
    } catch (error: any) {
      showSnackbar(t('profile.messages.verifyCodeFailed', { error: error.response?.data?.error }), "warning");
    } finally {
      setLoading(false);
    }
  }, [currentEmail, openDialog, showSnackbar, t, handleUpdateProfile, userEmail]);

  const handleUpdateName = useCallback(async () => {
    if (!name.trim()) {
      showSnackbar(t('profile.messages.nameRequired'), 'warning');
      return;
    }
    
    setLoading(true);
    try {
      const response = await updateProfile({ name });
      showSnackbar((response as any).message, 'success');
      updateUserInfo({ name });
    } catch (error: any) {
      showSnackbar(t('profile.messages.updateFailed', { error: error.response?.data?.error }), 'warning');
    } finally {
      setLoading(false);
    }
  }, [name, showSnackbar, t, updateUserInfo]);

  // 头像上传处理
  const handleAvatarChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // 检查文件类型
    const validImageTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validImageTypes.includes(file.type)) {
      showSnackbar(t('profile.messages.invalidImageType'), 'warning');
      return;
    }
    
    if (file.size > 2 * 1024 * 1024) {
      showSnackbar(t('profile.messages.avatarSizeLimit'), 'warning');
      return;
    }
    
    setAvatarUploading(true);
    try {
      // 前端压缩
      const options = {
        maxSizeMB: 0.5,
        maxWidthOrHeight: 256,
        useWebWorker: true,
        fileType: file.type // 保持原始文件类型
      };
      
      const compressed = await imageCompression(file, options);
      const reader = new FileReader();
      reader.onloadend = async () => {
        try {
          const base64 = reader.result as string;
          // 调用API上传
          const res = await updateAvatar(base64);
          updateUserInfo({ avatar: res.avatar });
          showSnackbar(t('profile.messages.avatarUploadSuccess'), 'success');
        } catch (uploadError: any) {
          console.error('Avatar upload error:', uploadError);
          showSnackbar(uploadError.response?.data?.error || t('profile.messages.avatarUploadFailed'), 'warning');
        }
      };
      reader.onerror = () => {
        showSnackbar(t('profile.messages.fileReadError'), 'warning');
      };
      reader.readAsDataURL(compressed);
    } catch (err) {
      console.error('Avatar compression error:', err);
      showSnackbar(t('profile.messages.avatarUploadFailed'), 'warning');
    } finally {
      setAvatarUploading(false);
    }
  };

  return (
    <Box>
      <StickyBreadcrumbs items={breadcrumbItems} />
      <Box sx={{ maxWidth: '800px', p: { xs: 2, md: 3 }, mx: 'auto' }}>
        <Card 
          elevation={0}
          sx={{ 
            borderRadius: 2,
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`
          }}
        >
          <CardContent>
            <Stack spacing={3}>
              {/* 头部信息 */}
              <Stack 
                direction={{ xs: 'column', sm: 'row' }} 
                alignItems={{ xs: 'flex-start', sm: 'center' }} 
                spacing={3}
                sx={{ mb: 2 }}
              >
                <label htmlFor="avatar-upload">
                  <input
                    id="avatar-upload"
                    type="file"
                    accept="image/*"
                    style={{ display: 'none' }}
                    onChange={handleAvatarChange}
                  />
                  <Box
                    sx={{
                      position: 'relative',
                      '&:hover .edit-overlay': {
                        opacity: 1,
                      },
                    }}
                  >
                    <Avatar
                      src={avatar}
                      alt={name || username}
                      sx={{ width: 80, height: 80, boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}` }}
                    />
                    <Box
                      className="edit-overlay"
                      sx={{
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        width: '100%',
                        height: '100%',
                        backgroundColor: 'rgba(0, 0, 0, 0.5)',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        borderRadius: '50%',
                        opacity: 0,
                        transition: 'opacity 0.3s ease',
                        cursor: 'pointer',
                      }}
                    >
                      <EditIcon sx={{ color: 'white' }} />
                    </Box>
                    {avatarUploading && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          width: '100%',
                          height: '100%',
                          backgroundColor: 'rgba(0, 0, 0, 0.5)',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          borderRadius: '50%',
                        }}
                      >
                        <CircularProgress size={24} sx={{ color: 'white' }} />
                      </Box>
                    )}
                  </Box>
                </label>
                <Stack spacing={1} flex={1}>
                  <Typography variant="h5">{name || username}</Typography>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <BadgeIcon fontSize="small" color="primary" />
                    <Typography variant="body2" color="text.secondary">
                      {role}
                    </Typography>
                  </Stack>
                </Stack>
              </Stack>

              <Divider />

              {/* 用户资料表单 */}
              <Stack spacing={3}>
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <FormControl fullWidth>
                    <TextField
                      label={t('common.fields.username')}
                      value={username}
                      InputProps={{
                        readOnly: true,
                        startAdornment: <AccountCircleIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                        sx: { borderRadius: 1 }
                      }}
                      variant="outlined"
                      fullWidth
                    />
                  </FormControl>
                  
                  <FormControl fullWidth>
                    <TextField
                      label={t('common.fields.name')}
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      InputProps={{
                        endAdornment: (
                          <Button
                            onClick={handleUpdateName}
                            disabled={isLoading || !name.trim()}
                            sx={{ minWidth: '80px' }}
                          >
                            {isLoading ? (
                              <CircularProgress size={20} />
                            ) : (
                              t('common.update')
                            )}
                          </Button>
                        ),
                        sx: { borderRadius: 1 }
                      }}
                      variant="outlined"
                      fullWidth
                    />
                  </FormControl>
                </Stack>

                <form onSubmit={handleSendCode}>
                  <FormControl fullWidth>
                    <TextField
                      type="email"
                      label={t('common.fields.email')}
                      value={currentEmail}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                      InputProps={{
                        startAdornment: <EmailIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                        endAdornment: (
                          <Button
                            type="submit"
                            disabled={isLoading || currentEmail === userEmail || !currentEmail.includes('@')}
                            sx={{ minWidth: '80px' }}
                          >
                            {isLoading ? (
                              <CircularProgress size={20} />
                            ) : (
                              t('profile.actions.verify')
                            )}
                          </Button>
                        ),
                        sx: { borderRadius: 1 }
                      }}
                      variant="outlined"
                      fullWidth
                    />
                  </FormControl>
                </form>
              </Stack>
            </Stack>
          </CardContent>
          
          <CardActions sx={{ justifyContent: 'space-between', p: 2 }}>
            <Button 
              variant="contained" 
              onClick={handleUpdatePwd}
              startIcon={<Lock />}
              sx={{ 
                py: 1.2,
                bgcolor: theme.palette.primary.main,
                '&:hover': {
                  bgcolor: alpha(theme.palette.primary.main, 0.8)
                }
              }}
            >
              {t('profile.actions.updatePassword')}
            </Button>
          </CardActions>
        </Card>
      </Box>
    </Box>
  );
}