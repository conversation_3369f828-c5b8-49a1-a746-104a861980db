import React, { useState, useEffect, useCallback, memo } from 'react';
import {
    TextField,
    Select,
    MenuItem,
    FormControl,
    InputLabel,
    Button,
    CircularProgress,
    Paper,
    Stack,
    Skeleton,
    useTheme,
    alpha
} from '@mui/material';
import { SelectChangeEvent } from '@mui/material/Select';
import { getUserInfo, updateUser, forceResetPassword, UserDetail } from '../../../services/userService';
import { useSnackbar } from '../../../context/SnackbarContext';
import { useDialogContext } from '../../../context/GlobalDialog';
import KeyIcon from '@mui/icons-material/Key';
import { t } from 'i18next';

interface UserFormProps {
    userId: string;
    onUpdate?: () => void; // 添加刷新回调函数
}

const UserForm: React.FC<UserFormProps> = memo(({ userId, onUpdate }) => {
    const [user, setUser] = useState<UserDetail>({
        email: '',
        role: '',
        username: ''
    });
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [resettingPwd, setResettingPwd] = useState(false);
    const { showSnackbar } = useSnackbar();
    const { closeDialog } = useDialogContext();
    const theme = useTheme();

    // 根据 userId 获取用户信息
    useEffect(() => {
        const fetchUserData = async () => {
            try {
                const response = await getUserInfo(userId);
                // 检查response是否存在且包含必要的字段
                if (response && typeof response === 'object') {
                    setUser({
                        email: response.email || '',
                        role: response.role || '',
                        username: response.username || ''
                    });
                } else {
                    // 如果响应为空或无效，显示提示信息
                    showSnackbar(t('common.messages.fetchError', { item: userId, error: 'user not found' }), 'warning');
                    closeDialog();
                }
            } catch (error: any) {
                const errorMessage = error.response?.data?.error || t('common.messages.fetchError', { item: userId, error: 'failed to fetch user data' });
                showSnackbar(errorMessage, 'warning');
                closeDialog();
            } finally {
                setLoading(false);
            }
        };

        fetchUserData();
    }, [userId, showSnackbar, closeDialog]);

    // 处理 TextField 变化
    const handleTextFieldChange = useCallback((field: keyof UserDetail) => (event: React.ChangeEvent<HTMLInputElement>) => {
        setUser(prev => ({
            ...prev,
            [field]: event.target.value,
        }));
    }, []);

    // 处理 Select 变化
    const handleSelectChange = useCallback((event: SelectChangeEvent<string>) => {
        setUser(prev => ({
            ...prev,
            role: event.target.value,
        }));
    }, []);

    // 处理表单提交
    const handleSubmit = useCallback(async (event: React.FormEvent) => {
        event.preventDefault();
        setSubmitting(true);
        try {
            const response = await updateUser(userId, user);
            showSnackbar((response as any).message, 'success');
            closeDialog(); // 关闭对话框
            onUpdate?.(); // 调用刷新函数
        } catch (error: any) {
            showSnackbar(error.response?.data?.error || t('common.messages.updateError', { item: userId }), 'error');
        } finally {
            setSubmitting(false);
        }
    }, [user, userId, showSnackbar, closeDialog, onUpdate]);

    const resetPwd = useCallback(async () => {
        setResettingPwd(true);
        try {
            const response = await forceResetPassword(user.email);
            showSnackbar((response as any).message, 'success');
        } catch (error: any) {
            showSnackbar(error.response?.data?.error || t('common.messages.updateError', { item: user.email }), 'error');
        } finally {
            setResettingPwd(false);
        }
    }, [user.email, showSnackbar]);

    if (loading) {
        return (
            <Paper elevation={0} sx={{ minWidth: 400, p: 3 }}>
                <Stack spacing={2}>
                    <Skeleton variant="text" width="60%" height={32} />
                    <Skeleton variant="rectangular" height={56} />
                    <Skeleton variant="rectangular" height={56} />
                    <Skeleton variant="rectangular" height={56} />
                    <Skeleton variant="rectangular" height={40} />
                </Stack>
            </Paper>
        );
    }

    return (
        <Paper elevation={0} sx={{ maxWidth: 500, p: 3 }}>
            
            <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                    {/* Email 字段 */}
                    <TextField
                        label={t('common.fields.email')}
                        type="email"
                        value={user.email}
                        onChange={handleTextFieldChange('email')}
                        fullWidth
                        required
                        variant="outlined"
                        InputProps={{
                            sx: { borderRadius: 1 }
                        }}
                    />

                    {/* Username 字段 */}
                    <TextField
                        label={t('common.fields.username')}
                        value={user.username}
                        onChange={handleTextFieldChange('username')}
                        fullWidth
                        required
                        variant="outlined"
                        InputProps={{
                            sx: { borderRadius: 1 }
                        }}
                    />

                    {/* Role 字段 */}
                    <FormControl fullWidth required variant="outlined">
                        <InputLabel>{t('users.fields.role')}</InputLabel>
                        <Select
                            value={user.role}
                            onChange={handleSelectChange}
                            label={t('users.fields.role')}
                            sx={{ borderRadius: 1 }}
                        >
                            <MenuItem value="admin">Admin</MenuItem>
                            <MenuItem value="enterprise">Enterprise</MenuItem>
                            <MenuItem value="operator">Operator</MenuItem>
                            <MenuItem value="user">User</MenuItem>
                        </Select>
                    </FormControl>

                    {/* 提交按钮 */}
                    <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                        <Button 
                            type="submit" 
                            variant="contained" 
                            fullWidth
                            disabled={submitting}
                            sx={{ 
                                py: 1.2,
                                bgcolor: theme.palette.primary.main,
                                '&:hover': {
                                    bgcolor: alpha(theme.palette.primary.main, 0.8)
                                },
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                minWidth: 'fit-content'
                            }}
                        >
                            {submitting ? <CircularProgress size={24} color="inherit" /> : t('users.saveChanges')}
                        </Button>
                        <Button 
                            onClick={resetPwd} 
                            variant="outlined" 
                            fullWidth
                            disabled={resettingPwd}
                            startIcon={<KeyIcon />}
                            sx={{ 
                                py: 1.2,
                                whiteSpace: 'nowrap',
                                overflow: 'hidden',
                                textOverflow: 'ellipsis',
                                minWidth: 'fit-content'
                            }}
                        >
                            {resettingPwd ? <CircularProgress size={24} color="inherit" /> : t('users.resetPassword')}
                        </Button>
                    </Stack>
                </Stack>
            </form>
        </Paper>
    );
});

export default UserForm;