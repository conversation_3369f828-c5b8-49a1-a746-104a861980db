import React, { useCallback, useEffect, useState, memo } from 'react';
import {
  Box,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
  Paper,
  Typography,
  CircularProgress,
  Button,
  ButtonGroup,
  TablePagination,
  Chip,
  Stack,
  useTheme,
  alpha,
  IconButton,
  TableContainer,
} from '@mui/material';
import { getUsers, deleteUser, UserBasic } from '../../../services/userService';
import { useSnackbar } from '../../../context/SnackbarContext';
import { useDialogContext } from "../../../context/GlobalDialog";
import Info from './userinfo';
import { Delete, Edit, Person, Refresh, PersonOutline } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

// 单行用户信息组件
const UserRow = memo(({ 
  user, 
  onEdit, 
  onDelete 
}: { 
  user: UserBasic, 
  onEdit: (userId: string) => void, 
  onDelete: (userId: string) => void 
}) => {
  const { t } = useTranslation();
  const theme = useTheme();
  
  const getRoleColor = (role: string) => {
    switch(role) {
      case 'superadmin': return 'error';
      case 'admin': return 'warning';
      case 'enterprise': return 'primary';
      case 'operator': return 'info';
      default: return 'default';
    }
  };

  return (
    <TableRow 
      hover
      sx={{ 
        '&:hover': { 
          backgroundColor: alpha(theme.palette.primary.main, 0.05),
        },
        transition: 'background-color 0.2s'
      }}
    >
      <TableCell>{user.id}</TableCell>
      <TableCell>{user.username}</TableCell>
      <TableCell>{user.name}</TableCell>
      <TableCell>{user.email}</TableCell>
      <TableCell>
        <Chip 
          size="small" 
          color={getRoleColor(user.role)} 
          label={user.role}
          sx={{ fontWeight: 500 }}
        />
      </TableCell>
      <TableCell>{user.enterprise_id}</TableCell>
      <TableCell align='center'>
        
        <ButtonGroup variant="text" size="small">
          <Button 
            color="primary" 
            onClick={() => onEdit(user.id)}
            startIcon={<Edit fontSize="small" />}
            sx={{ borderRadius: 1 }}
          >
            {t('common.edit')}
          </Button>
          <Button 
            color="error" 
            onClick={() => onDelete(user.id)}
            startIcon={<Delete fontSize="small" />}
            sx={{ borderRadius: 1 }}
          >
            {t('common.delete')}
          </Button>
        </ButtonGroup>
      </TableCell>
    </TableRow>
  );
});

export default function ManageUsers() {
  const [users, setUsers] = useState<UserBasic[]>([]);
  const [loading, setLoading] = useState(true);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [total, setTotal] = useState(0);
  const { showSnackbar } = useSnackbar();
  const { openDialog, confirm } = useDialogContext();
  const { t } = useTranslation();
  const theme = useTheme();

  const fetchUsers = useCallback(async (page: number, rowsPerPage: number) => {
    setLoading(true);
    try {
      const response = await getUsers(page + 1, rowsPerPage);
      setUsers(response.users || []);
      setTotal(response.total);
    } catch (error: any) {
      showSnackbar(
        t('users.messages.fetchError', { error: error.response?.data?.error || t('common.unknownError') }), 
        "warning"
      );
      setUsers([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  }, [showSnackbar, t]);

  const handleChangePage = useCallback((_event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  }, []);

  useEffect(() => {
    fetchUsers(page, rowsPerPage);
  }, [page, rowsPerPage, fetchUsers]);

  const handleEdit = useCallback((userId: string) => {
    openDialog(
      <Info 
        userId={userId} 
        onUpdate={() => fetchUsers(page, rowsPerPage)} 
      />, 
      t('users.editUser'),
      <Person sx={{ color: theme.palette.primary.main }} />
    );
  }, [openDialog, fetchUsers, page, rowsPerPage, t, theme]);

  const handleDelete = useCallback(async (userId: string) => {
    const confirmed = await confirm(
      t('common.deleteConfirm.title',{item:'user'}),
      <Box sx={{ p: 2 ,minWidth: 300}}>
        <Typography variant="body1" gutterBottom>
          {t('common.deleteConfirm.message',{item:'user'})}
        </Typography>
        <Typography variant="body2" color="error" gutterBottom>
          {t('common.deleteConfirm.warning')}
        </Typography>
      </Box>,
      <Delete sx={{ color: theme.palette.error.main }} />
    );
    
    if (!confirmed) return;

    try {
      await deleteUser(userId);
      showSnackbar(t('common.messages.deleteSuccess', { item: userId }), "success");
      fetchUsers(page, rowsPerPage);
    } catch (error: any) {
      showSnackbar(
        t('common.messages.deleteError', { item: userId, error: error.response?.data?.error || t('common.unknownError') }), 
        "error"
      );
    }
  }, [confirm, fetchUsers, page, rowsPerPage, showSnackbar, t, theme]);

  const handleRefresh = useCallback(() => {
    fetchUsers(page, rowsPerPage);
  }, [fetchUsers, page, rowsPerPage]);

  return (
    <Paper elevation={0}>
      {loading && (
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            gap: 1,
            zIndex: 10,
          }}
        >
          <CircularProgress size={40} sx={{ color: theme.palette.primary.main }} />
          <Typography variant="body2">{t('common.loading')}</Typography>
        </Box>
      )}
      
      <TableContainer sx={{ 
        maxHeight: '70vh',
        opacity: loading ? 0.5 : 1,
        transition: 'opacity 0.3s'
      }}>
        <Table aria-label="Users table" stickyHeader>
          <TableHead>
            <TableRow>
              <TableCell>ID</TableCell>
              <TableCell>{t('common.fields.username')}</TableCell>
              <TableCell>{t('common.fields.name')}</TableCell>
              <TableCell>{t('common.fields.email')}</TableCell>
              <TableCell>{t('users.fields.role')}</TableCell>
              <TableCell>{t('common.fields.enterpriseId')}</TableCell>
              <TableCell align='center'>
                {t('common.actions')}
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.length === 0 ? (
              <TableRow>
                <TableCell colSpan={5}>
                  <Stack direction="column" spacing={1} alignItems="center" sx={{ py: 4 }}>
                    <PersonOutline sx={{ fontSize: 40, color: 'text.disabled' }} />
                    <Typography color="text.secondary" align="center">
                      {t('users.noUsers')}
                    </Typography>
                  </Stack>
                </TableCell>
              </TableRow>
            ) : (
              users.map((user) => (
                <UserRow 
                  key={user.id} 
                  user={user} 
                  onEdit={handleEdit} 
                  onDelete={handleDelete} 
                />
              ))
            )}
          </TableBody>
        </Table>
      </TableContainer>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', pl: 1 }}>
            <IconButton onClick={handleRefresh} disabled={loading} size="small">
                <Refresh fontSize="small" />
            </IconButton>
            <TablePagination
            component="div"
            count={total}
            page={page}
            onPageChange={handleChangePage}
            rowsPerPage={rowsPerPage}
            onRowsPerPageChange={handleChangeRowsPerPage}
            rowsPerPageOptions={[10, 20, 30]}
            labelRowsPerPage={t('common.pagination.rowsPerPage')}
                labelDisplayedRows={({ from, to, count }) =>
                    t('common.pagination.displayedRows', { from, to, count })
                }
            />
        </Box>
    </Paper>
  );
}