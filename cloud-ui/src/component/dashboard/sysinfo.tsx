import React, { useEffect, useState, useCallback } from 'react';
import {
  Typography, Box, CircularProgress, Card, CardContent, Stack,
  CircularProgressProps, Chip, Paper, alpha, useTheme, Skeleton,
  Alert, Tooltip, FormControl, InputLabel, Select, MenuItem,
  SelectChangeEvent
} from '@mui/material';
import {
  Memory, Storage, ComputerOutlined,
  Speed, AccessTime, CloudDone
} from '@mui/icons-material';
import Grid from '@mui/material/Grid2';
import EChartsArea from '../charts/EChartsArea';
import { 
  SystemInfo, 
  NetworkDataPoint, 
  NetworkState, 
  getSystemInfo, 
  formatNetworkSpeed, 
  formatStorage 
} from '../../services/systemService';

// 环形进度条组件
const CircularProgressWithLabel: React.FC<CircularProgressProps & {
  value: number;
  icon: React.ReactNode;
  loading?: boolean;
}> = ({ value, icon, loading = false, ...props }) => {
  const theme = useTheme();

  const getColorByValue = (value: number) => {
    if (value > 80) return theme.palette.error;
    if (value > 50) return theme.palette.warning;
    return theme.palette.success;
  };

  const color = getColorByValue(value);

  if (loading) {
    return (
      <Box sx={{ position: 'relative', display: 'inline-flex', width: 64, height: 64 }}>
        <Skeleton variant="circular" width={64} height={64} />
      </Box>
    );
  }

  return (
    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
      <CircularProgress
        variant="determinate"
        value={100}
        size={64}
        thickness={4}
        sx={{
          color: alpha(color.main, 0.15),
        }}
        {...props}
      />
      <CircularProgress
        variant="determinate"
        value={value}
        size={64}
        thickness={4}
        sx={{
          color: color.main,
          position: 'absolute',
          left: 0,
          transition: 'all 0.5s ease',
        }}
        {...props}
      />
      <Box sx={{
        position: 'absolute',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        color: color.main
      }}>
        {icon}
      </Box>
    </Box>
  );
};

// 系统概览卡片组件
const SystemOverviewCard: React.FC<{ data: SystemInfo; loading: boolean }> = ({ data, loading }) => {
  const theme = useTheme();

  return (
    <Paper
      elevation={0}
      sx={{
        mb: 3,
        mt: 2,
        p: 3,
        borderRadius: theme.shape.borderRadius * 2,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.background.paper, 0.8)} 100%)`,
        border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
      }}
    >
      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} alignItems={{ xs: 'flex-start', sm: 'center' }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: 56,
            height: 56,
            borderRadius: '50%',
            backgroundColor: alpha(theme.palette.primary.main, 0.1),
          }}
        >
          <CloudDone color="primary" sx={{ fontSize: '2rem' }} />
        </Box>

        <Box sx={{ flex: 1 }}>
          <Stack spacing={0.5}>
            {loading ? (
              <>
                <Skeleton variant="text" width="40%" height={40} />
                <Skeleton variant="text" width="60%" height={20} />
              </>
            ) : (
              <>
                <Typography variant="h4" sx={{ fontWeight: 700 }}>{data.basic.hostname}</Typography>
                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                  System running for {data.basic.uptime}
                </Typography>
              </>
            )}
          </Stack>
        </Box>

        <Stack direction="row" flexWrap="wrap" justifyContent="space-between">
          {loading ? (
            <>
              <Skeleton variant="rounded" width={80} height={32} />
              <Skeleton variant="rounded" width={120} height={32} />
            </>
          ) : (
            <>
              <Chip
                icon={<Speed fontSize="small" />}
                size="medium"
                color="success"
                label={data.basic.kernel}
                sx={{ fontWeight: 500, m: 1 }}
              />
              <Chip
                icon={<AccessTime fontSize="small" />}
                size="medium"
                color="primary"
                label={data.basic.current_time}
                sx={{ fontWeight: 500, m: 1 }}
              />
            </>
          )}
        </Stack>
      </Stack>

      {data.cpu < 5 && !loading && (
        <Alert
          severity="info"
          sx={{ mt: 2, borderRadius: theme.shape.borderRadius }}
        >
          System is currently idle with low CPU usage ({data.cpu.toFixed(1)}%).
        </Alert>
      )}
    </Paper>
  );
};

// 资源使用卡片组件
const ResourceCard: React.FC<{
  icon: React.ReactNode;
  title: string;
  value: number;
  subtitle?: string;
  details?: Array<{
    label: string;
    value: string | number;
    tooltip?: string;
  }>;
  loading?: boolean;
}> = ({ icon, title, value, subtitle, details = [], loading = false }) => {
  const theme = useTheme();

  return (
    <Card
      variant="outlined"
      sx={{
        height: '100%',
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: theme.shadows[3],
          transform: 'translateY(-2px)'
        }
      }}
    >
      <CardContent sx={{ height: '100%', p: { xs: 2, sm: 3 } }}>
        <Stack spacing={2.5} sx={{ height: '100%' }}>
          <Stack direction="row" spacing={1} alignItems="center">
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: 36,
                height: 36,
                borderRadius: '50%',
                backgroundColor: alpha(theme.palette.primary.main, 0.1),
              }}
            >
              {React.cloneElement(icon as React.ReactElement, {
                color: "primary",
                sx: { fontSize: { xs: '1.2rem', sm: '1.5rem' } }
              })}
            </Box>
            <Typography
              variant="subtitle2"
              sx={{
                fontSize: { xs: '0.875rem', sm: '1rem' },
                fontWeight: 600
              }}
            >
              {title}
            </Typography>
          </Stack>

          <Stack
            direction="row"
            spacing={3}
            alignItems="center"
            justifyContent="space-between"
          >
            <CircularProgressWithLabel
              value={value}
              icon={icon}
              loading={loading}
            />

            <Stack spacing={0.5}>
              {loading ? (
                <>
                  <Skeleton variant="text" width={60} height={40} />
                  <Skeleton variant="text" width={100} height={20} />
                </>
              ) : (
                <>
                  <Typography
                    variant="h4"
                    sx={{
                      fontSize: { xs: '1.5rem', sm: '2rem' },
                      fontWeight: 700,
                      lineHeight: 1.2
                    }}
                  >
                    {`${Math.round(value)}%`}
                  </Typography>
                  {subtitle && (
                    <Typography
                      variant="caption"
                      sx={{
                        color: 'text.secondary',
                        fontSize: '0.75rem'
                      }}
                    >
                      {subtitle}
                    </Typography>
                  )}
                </>
              )}
            </Stack>
          </Stack>

          {details.length > 0 && (
            <Stack
              spacing={1.5}
              sx={{
                mt: 'auto',
                pt: 1,
                borderTop: '1px solid',
                borderColor: 'divider'
              }}
            >
              {details.map((detail, index) => (
                <Stack
                  key={index}
                  direction="row"
                  justifyContent="space-between"
                  alignItems="center"
                >
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'text.secondary',
                      fontSize: { xs: '0.75rem', sm: '0.875rem' }
                    }}
                  >
                    {detail.label}
                  </Typography>
                  {loading ? (
                    <Skeleton variant="text" width={60} />
                  ) : (
                    <Tooltip title={detail.tooltip || ''} arrow placement="top">
                      <Typography
                        variant="body2"
                        sx={{
                          color: 'text.primary',
                          fontWeight: 500,
                          fontSize: { xs: '0.75rem', sm: '0.875rem' }
                        }}
                      >
                        {detail.value}
                      </Typography>
                    </Tooltip>
                  )}
                </Stack>
              ))}
            </Stack>
          )}
        </Stack>
      </CardContent>
    </Card>
  );
};

const SystemInfoDashboard: React.FC = () => {
  const [data, setData] = useState<SystemInfo | null>(null);
  const [networkData, setNetworkData] = useState<NetworkDataPoint[]>([]);
  const lastDataRef = React.useRef<NetworkState | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [networkInterfaces, setNetworkInterfaces] = useState<string[]>([]);
  const [selectedInterface, setSelectedInterface] = useState<string>('');
  const [isFirstLoad, setIsFirstLoad] = useState(true);

  // 选择网络接口的处理函数
  const handleInterfaceChange = (event: SelectChangeEvent<string>) => {
    setSelectedInterface(event.target.value);
  };

  // 获取系统信息
  const fetchSystemInfo = useCallback(async () => {
    try {
      setError(null);
      
      // 准备API请求参数
      const params: { init?: string; i?: string } = {};
      
      // 第一次加载时添加init参数以获取网络接口列表
      if (isFirstLoad) {
        params.init = "1";
        setIsFirstLoad(false);
      }
      
      if (selectedInterface) {
        params.i = selectedInterface;
      }
      
      const currentTime = new Date();
      const response = await getSystemInfo(params);

      // 如返回了接口列表，则存储，并默认选中第一个接口（若用户未手动选择）
      if (response.interfaceList && response.interfaceList.length > 0) {
        setNetworkInterfaces(response.interfaceList);
        if (!selectedInterface) {
          setSelectedInterface(response.interfaceList[0]);
        }
      }

      setData(response);

      // 用于计算网络数据传输速率
      if (!lastDataRef.current) {
        lastDataRef.current = {
          bytes_recv: response.net.bytes_recv,
          bytes_sent: response.net.bytes_sent,
          timestamp: currentTime.getTime()
        };
        setLoading(false);
        return;
      }

      const timeInterval = (currentTime.getTime() - lastDataRef.current.timestamp) / 1000;
      if (timeInterval > 0) {
        const recvRate = Math.max(0, (response.net.bytes_recv - lastDataRef.current.bytes_recv) / timeInterval);
        const sentRate = Math.max(0, (response.net.bytes_sent - lastDataRef.current.bytes_sent) / timeInterval);

        setNetworkData(prevData => [
          ...prevData.slice(-19),
          {
            time: currentTime,
            recv: recvRate,
            sent: sentRate
          }
        ]);
      }

      lastDataRef.current = {
        bytes_recv: response.net.bytes_recv,
        bytes_sent: response.net.bytes_sent,
        timestamp: currentTime.getTime()
      };

      setLoading(false);
    } catch (error) {
      console.error('Error fetching system info:', error);
      setError('Failed to fetch system information. Please try again later.');
      setLoading(false);
    }
  }, [isFirstLoad, selectedInterface]);

  // 初始化图表数据和第一次获取信息
  useEffect(() => {
    const now = Date.now();
    const initialData = Array(10).fill(0).map((_, i) => ({
      time: new Date(now - (9 - i) * 3000),
      recv: 0,
      sent: 0
    }));
    setNetworkData(initialData);
    fetchSystemInfo(); // 立即获取第一次数据
  }, [fetchSystemInfo]);

  // 当选择的网络接口改变时重新初始化数据并获取信息
  useEffect(() => {
    if (!isFirstLoad && selectedInterface) {
      const now = Date.now();
      const initialData = Array(10).fill(0).map((_, i) => ({
        time: new Date(now - (9 - i) * 3000),
        recv: 0,
        sent: 0
      }));
      setNetworkData(initialData);
      lastDataRef.current = null;
      fetchSystemInfo();
    }
  }, [selectedInterface, fetchSystemInfo, isFirstLoad]);

  // 定时轮询系统信息
  useEffect(() => {
    const timerId = setInterval(fetchSystemInfo, 3000);
    return () => clearInterval(timerId);
  }, [fetchSystemInfo]);

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ flexGrow: 1 }}>
      <SystemOverviewCard data={data || {} as SystemInfo} loading={loading} />

      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <ResourceCard
            icon={<ComputerOutlined />}
            title="CPU Usage"
            value={data?.cpu || 0}
            subtitle={`${data?.cpu.toFixed(2) || 0} / 100`}
            details={[
              {
                label: "Current Load",
                value: `${data?.cpu.toFixed(2) || 0}%`,
                tooltip: "Current CPU load percentage"
              },
              {
                label: "System Uptime",
                value: data?.basic.uptime || "0d 0h 0m",
                tooltip: "Time since system boot"
              }
            ]}
            loading={loading}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <ResourceCard
            icon={<Memory />}
            title="Memory Usage"
            value={data?.memory.used_percent || 0}
            subtitle={`${formatStorage(data?.memory.used || 0)} / ${formatStorage(data?.memory.total || 0)}`}
            details={[
              {
                label: "Total Memory",
                value: formatStorage(data?.memory.total || 0),
                tooltip: "Total physical memory"
              },
              {
                label: "Available",
                value: formatStorage(data?.memory.available || 0),
                tooltip: "Memory available for allocation"
              },
              {
                label: "Used",
                value: formatStorage(data?.memory.used || 0),
                tooltip: "Memory currently in use"
              }
            ]}
            loading={loading}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <ResourceCard
            icon={<Storage />}
            title="Disk Usage"
            value={data?.disk.used_percent || 0}
            subtitle={`${formatStorage(data?.disk.used || 0)} / ${formatStorage(data?.disk.total || 0)}`}
            details={[
              {
                label: "Total Space",
                value: formatStorage(data?.disk.total || 0),
                tooltip: "Total disk space"
              },
              {
                label: "Free Space",
                value: formatStorage(data?.disk.free || 0),
                tooltip: "Available disk space"
              },
              {
                label: "Used Space",
                value: formatStorage(data?.disk.used || 0),
                tooltip: "Used disk space"
              }
            ]}
            loading={loading}
          />
        </Grid>
      </Grid>

      <Card
        variant="outlined"
        sx={{
          height: '100%',
          mb: 3
        }}
      >
        {loading ? (
          <CardContent sx={{ p: 3 }}>
            <Skeleton variant="text" width="30%" height={30} />
            <Skeleton variant="text" width="50%" height={20} sx={{ mb: 2 }} />
            <Skeleton variant="rectangular" height={320} />
          </CardContent>
        ) : (
          <>
            <CardContent sx={{ pb: 0 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6" component="div">
                  Network Traffic
                </Typography>
                {networkInterfaces.length > 0 && (
                  <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
                    <InputLabel id="network-interface-label">Interface</InputLabel>
                    <Select
                      labelId="network-interface-label"
                      value={selectedInterface}
                      onChange={handleInterfaceChange}
                      label="Interface"
                    >
                      {networkInterfaces.map((iface) => (
                        <MenuItem key={iface} value={iface}>
                          {iface}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              </Box>
              <EChartsArea
                title={`Interface: ${selectedInterface}`}
                subtitle={`Real-time monitoring of ${selectedInterface || 'default'} interface`}
                chipLabel={`↑ ${formatNetworkSpeed(networkData[networkData.length - 1]?.sent || 0)} ↓ ${formatNetworkSpeed(networkData[networkData.length - 1]?.recv || 0)}`}
                chipColor="primary"
                series={[
                  {
                    id: 'received',
                    label: 'Received',
                    data: networkData.map(d => Number(d.recv.toFixed(2)))
                  },
                  {
                    id: 'sent',
                    label: 'Sent',
                    data: networkData.map(d => Number(d.sent.toFixed(2)))
                  }
                ]}
                xAxis={{
                  data: networkData.map(d => d.time),
                  tickInterval: (index) => index % 4 === 0
                }}
                height={400}
              />
            </CardContent>
          </>
        )}
      </Card>
    </Box>
  );
};

export default SystemInfoDashboard;