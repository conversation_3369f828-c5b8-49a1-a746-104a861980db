import React, { useState, useEffect, useContext, useMemo, useCallback } from 'react';
import {
  Box,
  Button,
  CircularProgress,
  IconButton,
  TextField,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  TablePagination,
  alpha,
  styled,
  Card,
  CardHeader,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Chip,
  Divider,
  useTheme
} from '@mui/material';
import {
  Add,
  HighlightOffRounded,
  Refresh,
  Router,
  MoreHoriz,
  PlayCircleOutlineRounded,
  StopCircleOutlined,
  WifiTethering,
  PortableWifiOff,
  PeopleAlt,
  TrendingUp,
  TrendingDown,
  Speed,
  NetworkCheck,
} from '@mui/icons-material';
import { 
  listDevices, 
  createDevice, 
  deleteDevice, 
  startDevice, 
  stopDevice, 
  getDevicesStatus,
  getTrafficData,
  formatBytes,
  Device,
  DeviceWithBandwidth,
  TrafficData,
  TrafficRate
} from '../../../services/wireguardService';
import { useSnackbar } from '../../../context/SnackbarContext';
import { DialogContext } from '../../../context/GlobalDialog';
import StickyBreadcrumbs from '../../utils/StickyBreadcrumbs';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
// Import ReactECharts
import ReactECharts from 'echarts-for-react';
// Import the BandwidthLimitContent component
import BandwidthLimitContent from './BandwidthLimitDialog';
import { getUserInfoFromStorage } from '../../../context/UserContext';

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 1.5,
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.15)}`,
  '&:hover': {
    boxShadow: `0 6px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
    transform: 'translateY(-2px)'
  },
  transition: 'all 0.2s ease',
}));


// Traffic visualization component
const TrafficVisualizer: React.FC<{ deviceName: string; enterpriseId: string }> = React.memo(({ 
  deviceName,
  enterpriseId
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(true);
  const [trafficData, setTrafficData] = useState<TrafficData[]>([]);
  const [trafficRates, setTrafficRates] = useState<TrafficRate[]>([]);
  const [totalReceived, setTotalReceived] = useState(0);
  const [totalSent, setTotalSent] = useState(0);
  
  useEffect(() => {
    const fetchTrafficData = async () => {
      try {
        setLoading(true);
        const response = await getTrafficData(deviceName);
        
        if (!response.data || response.data.length <= 1) {
          setTrafficData([]);
          setTrafficRates([]);
          setTotalReceived(0);
          setTotalSent(0);
          return;
        }
        
        const sortedData = [...response.data].sort(
          (a, b) => new Date(a.ts).getTime() - new Date(b.ts).getTime()
        );
        
        // Process data more efficiently - calculate rates in a single pass
        const rates: TrafficRate[] = [];
        let previousPoint = sortedData[0];
        
        for (let i = 1; i < sortedData.length; i++) {
          const current = sortedData[i];
          const currentTime = new Date(current.ts).getTime();
          const previousTime = new Date(previousPoint.ts).getTime();
          
          // Calculate time difference in seconds
          const timeDiffSeconds = (currentTime - previousTime) / 1000;
          
          if (timeDiffSeconds > 0) {
            // Calculate bytes transferred during this interval
            const rxDiff = Math.max(0, current.rx_bytes - previousPoint.rx_bytes);
            const txDiff = Math.max(0, current.tx_bytes - previousPoint.tx_bytes);
            
            // Calculate bytes per second
            rates.push({
              timestamp: current.ts,
              rxRate: rxDiff / timeDiffSeconds,
              txRate: txDiff / timeDiffSeconds
            });
          }
          
          previousPoint = current;
        }
        
        setTrafficData(sortedData);
        setTrafficRates(rates);
        
        // Calculate totals from the last data point
        if (sortedData.length > 0) {
          const lastPoint = sortedData[sortedData.length - 1];
          setTotalReceived(lastPoint.rx_bytes);
          setTotalSent(lastPoint.tx_bytes);
        }
      } catch (error) {
        console.error('Failed to fetch traffic data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchTrafficData();
  }, [deviceName, enterpriseId]);
  
  // Memoize chart options to prevent unnecessary recalculations
  const chartOptions = useMemo(() => {
    const timestamps = trafficRates.map(item => new Date(item.timestamp).toLocaleTimeString());
    const rxRates = trafficRates.map(item => (item.rxRate / 1024).toFixed(2)); // Convert to KB/s
    const txRates = trafficRates.map(item => (item.txRate / 1024).toFixed(2)); // Convert to KB/s
    
    return {
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const time = params[0].axisValue;
          let tooltip = `<div style="margin: 0px 0 0; line-height:1;">${time}</div><div style="margin: 0px 0 0; line-height:1;"><br/></div>`;
          params.forEach((param: any) => {
            tooltip += `<div style="margin: 5px 0 0; line-height:1;">
              <span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:${param.color};"></span>
              ${param.seriesName}: ${param.value} KB/s
              </div>`;
          });
          return tooltip;
        },
      },
      title: {
        left: 'center',
        text: `${deviceName} - ${t('wireguard.common.traffic')}`,
        subtext: t('common.fields.lastUpdated')
      },
      grid: {
        left: '3%',
        right: '4%',
        top: '15%',
        bottom: '10%',
        containLabel: true
      },
      toolbox: {
        feature: {
          saveAsImage: {},
          restore: {}
        }
      },
      legend: {
        data: [t('wireguard.common.received'), t('wireguard.common.transmitted')],
        left: 0,
        top: 30
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: timestamps,
        axisLabel: {
          rotate: 45,
          interval: Math.ceil(timestamps.length / 10) // Show every Nth label to avoid crowding
        }
      },
      yAxis: {
        type: 'value',
        boundaryGap: [0, '10%']
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: 100
        },
        {
          start: 90,
          end: 100,
          bottom: '2%'
        }
      ],
      series: [
        {
          name: t('wireguard.common.received'),
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: '#2196f3' // Blue color for received data
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(33, 150, 243, 0.5)' },
                { offset: 1, color: 'rgba(33, 150, 243, 0.1)' }
              ]
            }
          },
          data: rxRates
        },
        {
          name: t('wireguard.common.transmitted'),
          type: 'line',
          symbol: 'none',
          sampling: 'lttb',
          itemStyle: {
            color: '#4caf50' // Green color for sent data
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(76, 175, 80, 0.5)' },
                { offset: 1, color: 'rgba(76, 175, 80, 0.1)' }
              ]
            }
          },
          data: txRates
        }
      ]
    };
  }, [trafficRates, deviceName, t]);
  
  if (loading) {
    return (
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'center' }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (trafficData.length <= 1) {
    return (
      <Alert severity="info" sx={{ m: 2 }}>
        {t('common.noData')}
      </Alert>
    );
  }
  
  return (
    <Box sx={{ p: 3, minWidth: 600 }}>
      <ReactECharts 
        option={chartOptions} 
        style={{ height: 400 }}
        opts={{ renderer: 'canvas' }}
        notMerge={true}
        lazyUpdate={true}
      />
      <Divider sx={{ my: 2 }} />
      <Box sx={{ display: 'flex', justifyContent: 'space-between', px: 2 }}>
        <Box>
          <Typography variant="body2" color="text.secondary">{t('wireguard.common.totalReceived')}:</Typography>
          <Typography variant="subtitle1" fontWeight="medium" color="info.main">{formatBytes(totalReceived)}</Typography>
        </Box>
        <Box>
          <Typography variant="body2" color="text.secondary">{t('wireguard.common.totalSent')}:</Typography>
          <Typography variant="subtitle1" fontWeight="medium" color="success.main">{formatBytes(totalSent)}</Typography>
        </Box>
        <Box>
          <Typography variant="body2" color="text.secondary">{t('wireguard.common.traffic')}:</Typography>
          <Typography variant="subtitle1" fontWeight="medium">{formatBytes(totalReceived + totalSent)}</Typography>
        </Box>
      </Box>
    </Box>
  );
});


// Custom hook for device polling to separate concerns
const useDevicePolling = (devices: Device[], pollingInterval = 2000) => {
  const [devicesWithBandwidth, setDevicesWithBandwidth] = useState<DeviceWithBandwidth[]>([]);
  const [isPolling, setIsPolling] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  
  // Function to fetch device status
  const fetchDeviceStatus = useCallback(async () => {
    if (devices.length === 0 || isPaused) return;
    
    try {
      setIsPolling(true);
      const devicesToQuery = devices.map(device => ({
        name: device.Name,
        enterprise_id: device.EnterpriseID
      }));
      
      const statusResponse = await getDevicesStatus(devicesToQuery);

      // Use functional update to avoid closures capturing stale state
      setDevicesWithBandwidth(prevStatus => {
        // Create a map of existing devices for efficient lookups
        const newStatusMap = new Map(
          prevStatus.map(device => [device.Name, device])
        );
        
        // Update or add each device
        statusResponse.data.forEach(device => {
          newStatusMap.set(device.device_name, {
            ...(newStatusMap.get(device.device_name) || {}),
            ...device,
            Name: device.device_name,
            status: device.status
          });
        });
        
        return Array.from(newStatusMap.values());
      });
    } catch (error) {
      console.error('Failed to fetch device status:', error);
    } finally {
      setIsPolling(false);
    }
  }, [devices, isPaused]);

  // Set up polling with useEffect
  useEffect(() => {
    // Initial fetch only if we have devices
    if (devices.length === 0) return;
    
    fetchDeviceStatus();
    
    // Set up polling interval
    const timer = setInterval(fetchDeviceStatus, pollingInterval);
    
    // Cleanup
    return () => clearInterval(timer);
  }, [fetchDeviceStatus, pollingInterval, devices.length]);

  const pausePolling = useCallback(() => {
    setIsPaused(true);
  }, []);

  const resumePolling = useCallback(() => {
    setIsPaused(false);
  }, []);

  return { 
    devicesWithBandwidth, 
    isPolling, 
    refreshDeviceStatus: fetchDeviceStatus,
    pausePolling,
    resumePolling 
  };
};

const WireguardManager: React.FC = () => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalDevices, setTotalDevices] = useState(0);
  const [refreshKey, setRefreshKey] = useState(0);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);

  const { showSnackbar } = useSnackbar();
  const dialogContext = useContext(DialogContext);
  const navigate = useNavigate();

  // Use memoized breadcrumb items to prevent unnecessary re-renders
  const breadcrumbItems = useMemo(() => [
    { label: 'Wireguard', href: '/dashboard/wireguard' },
    { label: t('routes.wireguard') + ' Manager', isCurrent: true },
  ], [t]);

  // Use custom hook for device polling
  const { 
    devicesWithBandwidth, 
    isPolling, 
    refreshDeviceStatus,
    pausePolling,
    resumePolling
  } = useDevicePolling(devices);

  // Fetch devices on component mount and when page/rowsPerPage changes
  const fetchDevices = useCallback(async () => {
    setLoading(true);
    try {
      // 首先获取设备列表
      const response = await listDevices(page + 1, rowsPerPage);
      setDevices(response.data || []);
      setTotalDevices(response.total || 0);
    } catch (error: any) {
      showSnackbar(`Failed to fetch devices: ${error.response?.data?.error || 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  }, [page, rowsPerPage, showSnackbar]);

  useEffect(() => {
    fetchDevices();
  }, [fetchDevices, refreshKey]);

  // Create device handler with useCallback to prevent unnecessary re-renders
  const handleCreateDevice = useCallback(async (deviceName: string, entId: string) => {
    if (!deviceName.trim() || !entId.trim()) {
      showSnackbar('Device name and enterprise ID cannot be empty', 'warning');
      return;
    }

    setLoading(true);
    try {
      await createDevice(deviceName, entId);
      showSnackbar('Device created successfully', 'success');
      setRefreshKey(prev => prev + 1);
      if (dialogContext) {
        dialogContext.closeDialog();
      }
    } catch (error: any) {
      showSnackbar(`Failed to create device: ${error.response?.data?.error || 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  }, [dialogContext, showSnackbar]);

  // Delete device handler with useCallback
  const handleDeleteDevice = useCallback(async (device: Device) => {
    if (!dialogContext) return;
    
    const confirmed = await dialogContext.confirm(
      t('common.confirm'),
      <Box sx={{p:2}}>
        <Typography variant="body1" gutterBottom>
          {t('common.deleteConfirm.message', { item: device.Name })}
        </Typography>
        <Typography variant="body2" color="error" gutterBottom>
          {t('common.deleteConfirm.warning')}
        </Typography>
      </Box>
    );

    if (!confirmed) return;

    setLoading(true);
    try {
      await deleteDevice(device.Name, device.EnterpriseID, device.ID);
      showSnackbar('Device deleted successfully', 'success');
      setRefreshKey(prev => prev + 1); // 触发刷新
    } catch (error: any) {
      showSnackbar(`Failed to delete device: ${error.response?.data?.error || 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  }, [dialogContext, showSnackbar, t]);

  // Interface control handlers with useCallback
  const handleStartInterface = useCallback(async (device: Device) => {
    setLoading(true);
    try {
      await startDevice(device.Name);
      showSnackbar('Interface started successfully', 'success');
      refreshDeviceStatus(); // More efficient than full refresh
    } catch (error: any) {
      showSnackbar(`Failed to start interface: ${error.response?.data?.error || 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  }, [refreshDeviceStatus, showSnackbar]);

  const handleStopInterface = useCallback(async (device: Device) => {
    setLoading(true);
    try {
      await stopDevice(device.Name);
      showSnackbar('Interface stopped successfully', 'success');
      refreshDeviceStatus(); // More efficient than full refresh
    } catch (error: any) {
      showSnackbar(`Failed to stop interface: ${error.response?.data?.error || 'Unknown error'}`, 'error');
    } finally {
      setLoading(false);
    }
  }, [refreshDeviceStatus, showSnackbar]);

  const handleChangePage = useCallback((_: unknown, newPage: number) => {
    setPage(newPage);
  }, []);

  const handleChangeRowsPerPage = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  }, []);

  // 导航到Peer管理页面
  const navigateToPeerManager = useCallback((device: Device) => {
    navigate(`/dashboard/wireguard-peers/${device.EnterpriseID}`);
  }, [navigate]);

  // View traffic details with lazy loading
  const viewTrafficDetails = useCallback((device: Device) => {
    const deviceWithBandwidth = devicesWithBandwidth.find(d => d.Name === device.Name);
    if (deviceWithBandwidth && dialogContext) {
      dialogContext.openDialog(
        <Box sx={{ p: 1, width: '100%',height:'100%', maxWidth: 800 }}>
          <TrafficVisualizer 
            deviceName={device.Name}
            enterpriseId={device.EnterpriseID}
          />
        </Box>,
        t('device.details')
      );
    }
  }, [devicesWithBandwidth, dialogContext, t]);

  // 打开创建设备对话框 - Extracted as a component to reduce re-renders
  const CreateDeviceForm = ({ onClose, onCreate }: { onClose: () => void, onCreate: (name: string, entId: string) => void }) => {
    const [dialogDeviceName, setDialogDeviceName] = useState('');
    const [dialogEnterpriseId, setDialogEnterpriseId] = useState(
      getUserInfoFromStorage().enterprise_id || ''
    );

    return (
      <Box sx={{ p: 2 }}>
        <TextField
          label={t('device.fields.name')}
          fullWidth
          value={dialogDeviceName}
          onChange={(e) => setDialogDeviceName(e.target.value)}
          margin="normal"
          required
          variant="outlined"
          InputProps={{
            sx: { borderRadius: 1.5 }
          }}
        />
        <TextField
          label={t('common.fields.enterpriseId')}
          fullWidth
          value={dialogEnterpriseId}
          onChange={(e) => setDialogEnterpriseId(e.target.value)}
          margin="normal"
          required
          helperText={t('common.fields.enterpriseId')}
          variant="outlined"
          InputProps={{
            sx: { borderRadius: 1.5 }
          }}
        />
        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
          <Button 
            onClick={onClose}
            sx={{ borderRadius: 1.5, textTransform: 'none' }}
          >
            {t('common.cancel')}
          </Button>
          <Button 
            onClick={() => onCreate(dialogDeviceName, dialogEnterpriseId)} 
            variant="contained" 
            disabled={loading || !dialogDeviceName.trim() || !dialogEnterpriseId.trim()}
            sx={{ borderRadius: 1.5, textTransform: 'none' }}
          >
            {loading ? <CircularProgress size={24} /> : t('common.create')}
          </Button>
        </Box>
      </Box>
    );
  };

  const openCreateDeviceDialog = useCallback(() => {
    if (!dialogContext) return;
    
    dialogContext.openDialog(
      <CreateDeviceForm 
        onClose={() => dialogContext.closeDialog()} 
        onCreate={handleCreateDevice}
      />,
      t('device.add.title')
    );
  }, [dialogContext, handleCreateDevice, t]);

  // 添加菜单处理函数
  const handleMenuOpen = useCallback((event: React.MouseEvent<HTMLElement>, device: Device) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedDevice(device);
    // Pause polling when menu is opened to prevent flickering
    pausePolling();
  }, [pausePolling]);

  const handleMenuClose = useCallback(() => {
    setAnchorEl(null);
    setSelectedDevice(null);
    // Resume polling when menu is closed
    resumePolling();
  }, [resumePolling]);

  // 修改带宽限制对话框处理方法
  const handleOpenBandwidthLimitDialog = useCallback((device: Device) => {
    if (dialogContext) {
      dialogContext.openDialog(
        <BandwidthLimitContent
          deviceName={device.Name}
          enterpriseId={device.EnterpriseID}
          onClose={() => dialogContext?.closeDialog()}
          onSuccess={() => {
            refreshDeviceStatus();
            showSnackbar(t('wireguard.bandwidth.limitUpdated'), 'success');
          }}
        />,
        t('wireguard.bandwidth.limitTitle'),
        <Speed color="primary" />
      );
    }
    
    // 调用已有的handleMenuClose方法关闭菜单
    handleMenuClose();
  }, [dialogContext, refreshDeviceStatus, showSnackbar, t, handleMenuClose]);

  // Memoize the table headers to prevent re-rendering
  const tableHeaders = useMemo(() => 
    [
      { key: 'name', translation: 'device.fields.name' },
      { key: 'ip', translation: 'wireguard.common.ip' },
      { key: 'listenPort', translation: 'wireguard.common.listenPort' },
      { key: 'peers', translation: 'wireguard.common.peers' },
      { key: 'status', translation: 'common.fields.status' },
      { key: 'traffic', translation: 'wireguard.common.traffic' },
      { key: 'totalReceived', translation: 'wireguard.common.totalReceived' },
      { key: 'totalSent', translation: 'wireguard.common.totalSent' },
      { key: 'actions', translation: 'common.actions' }
    ].map((header, index) => (
      <TableCell 
        key={`header-${index}`} 
        align={['actions', 'status', 'peers', 'traffic'].includes(header.key) ? 'center' : 'left'}
        sx={{ 
          py: 1.5,
          bgcolor: (theme) => header.key === 'name' ? alpha(theme.palette.primary.main, 0.15) : 'inherit'
        }}
      >
        <Typography variant="subtitle2" fontWeight="bold">
          {t(header.translation)}
        </Typography>
      </TableCell>
    )), [t]);
  
  // Memoize device menu to prevent re-renders
  const DeviceActionsMenu = useCallback(({ device }: { device: Device }) => {
    const deviceWithBandwidth = devicesWithBandwidth.find(d => d.Name === device.Name);
    const isRunning = deviceWithBandwidth?.status === 'running';
    
    return (
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl) && selectedDevice?.ID === device.ID}
        onClose={handleMenuClose}
        PaperProps={{
          elevation: 3,
          sx: { borderRadius: 2, mt: 0.5 }
        }}
      >
        <MenuItem
          onClick={() => {
            navigateToPeerManager(device);
            handleMenuClose();
          }}
        >
          <ListItemIcon>
            <Router color="primary" />
          </ListItemIcon>
          <ListItemText>{t('wireguard.common.peers')}</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleOpenBandwidthLimitDialog(device);
          }}
        >
          <ListItemIcon>
            <NetworkCheck color="primary" />
          </ListItemIcon>
          <ListItemText>{t('wireguard.bandwidth.manageLimit')}</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={() => {
            handleStartInterface(device);
            handleMenuClose();
          }}
          disabled={isRunning}
        >
          <ListItemIcon>
            <PlayCircleOutlineRounded color="success" />
          </ListItemIcon>
          <ListItemText>{t('common.status.running')}</ListItemText>
        </MenuItem>
        <MenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleStopInterface(device);
            handleMenuClose();
          }}
          disabled={!isRunning}
        >
          <ListItemIcon>
            <StopCircleOutlined color="warning" />
          </ListItemIcon>
          <ListItemText>{t('common.status.stopped')}</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem
          onClick={(e) => {
            e.stopPropagation();
            handleDeleteDevice(device);
            handleMenuClose();
          }}
        >
          <ListItemIcon>
            <HighlightOffRounded color="error" />
          </ListItemIcon>
          <ListItemText>{t('common.delete')}</ListItemText>
        </MenuItem>
      </Menu>
    );
  }, [
    anchorEl, 
    selectedDevice, 
    devicesWithBandwidth, 
    navigateToPeerManager,
    handleStartInterface,
    handleStopInterface,
    handleDeleteDevice,
    handleMenuClose,
    handleOpenBandwidthLimitDialog,
    t
  ]);

  return (
    <Box>
      <StickyBreadcrumbs items={breadcrumbItems} />
      
      <Card 
        elevation={0}
        sx={{ 
          mb: 3, 
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
        }}
      >
        <CardHeader 
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Router color="primary" />
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {t('wireguard.common.peers')}
              </Typography>
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <StyledButton 
                variant="outlined" 
                startIcon={<Refresh />} 
                size="small"
                onClick={() => setRefreshKey(prev => prev + 1)}
                disabled={loading || isPolling}
              >
                {t('common.refresh')}
              </StyledButton>
              <StyledButton 
                variant="contained" 
                startIcon={<Add />} 
                size="small"
                onClick={openCreateDeviceDialog}
              >
                {t('device.add.add')}
              </StyledButton>
            </Box>
          }
          sx={{
            backgroundColor: alpha(theme.palette.primary.main, 0.03),
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        />
      </Card>
      
      <Box>
        {loading && devices.length === 0 ? (
          <Card 
            variant="outlined" 
            sx={{ 
              display: 'flex', 
              justifyContent: 'center',
              alignItems: 'center',
              p: 5,
              backgroundColor: alpha(theme.palette.background.paper, 0.6),
              borderRadius: 3,
              boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
            }}
          >
            <CircularProgress 
              size={50} 
              thickness={4} 
              sx={{ 
                mr: 2,
                color: theme.palette.primary.main,
                '& .MuiCircularProgress-circle': {
                  strokeLinecap: 'round',
                }
              }} 
            />
            <Typography 
              variant="h6" 
              color="text.secondary"
              sx={{ 
                fontWeight: 500,
                opacity: 0.8
              }}
            >
              {t('common.loading')}
            </Typography>
          </Card>
        ) : devices.length === 0 ? (
          <Alert 
            severity="info" 
            sx={{ 
              borderRadius: 3,
              boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
              p: 3
            }}
          >
            {t('common.noItems', { item: t('device.fields.name') })}
          </Alert>
        ) : (
          <Card 
            elevation={0}
            sx={{ 
              borderRadius: 3,
              overflow: 'hidden',
              border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
            }}
          >
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow sx={{ 
                    bgcolor: (theme) => alpha(theme.palette.primary.main, 0.05),
                    '& th': {
                      borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`
                    }
                  }}>
                    {tableHeaders}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {devices.map((device) => {
                    const deviceWithBandwidth = devicesWithBandwidth.find(d => d.Name === device.Name);
                    const isRunning = deviceWithBandwidth?.status === 'running';
                    
                    return (
                      <TableRow 
                        key={device.ID}
                        hover
                        sx={{ 
                          cursor: 'default',
                          transition: 'all 0.2s',
                          borderLeft: (theme) => `4px solid ${isRunning ? theme.palette.success.main : theme.palette.error.main}`
                        }}
                      >
                        <TableCell>
                          <Typography fontWeight="medium">{device.Name}</Typography>
                        </TableCell>
                        <TableCell>
                          <Chip 
                            label={device.IP} 
                            size="small" 
                            variant="outlined" 
                            color="primary"
                            sx={{ 
                              borderRadius: 1,
                              fontSize: '0.75rem',
                              height: 24,
                              fontFamily: 'monospace'
                            }} 
                          />
                        </TableCell>
                        <TableCell>{device.ListenPort}</TableCell>
                        <TableCell align="center">
                          <Chip
                            icon={<PeopleAlt fontSize="small" />}
                            label={deviceWithBandwidth?.peersCount || 0}
                            size="small"
                            color={(deviceWithBandwidth?.peersCount ?? 0) > 0 ? "primary" : "default"}
                            variant={(deviceWithBandwidth?.peersCount ?? 0) > 0 ? "filled" : "outlined"}
                            sx={{ 
                              borderRadius: 1,
                              minWidth: 60,
                              fontWeight: 'medium',
                              transition: 'all 0.2s ease',
                              '&:hover': {
                                transform: 'scale(1.05)'
                              }
                            }}
                          />
                        </TableCell>
                        <TableCell align="center">
                          {isRunning ? (
                            <Chip
                              icon={<WifiTethering fontSize="small" />}
                              label={t('common.status.running')}
                              size="small"
                              color="success"
                              sx={{ 
                                borderRadius: 1,
                                minWidth: 80,
                                boxShadow: `0 2px 8px ${alpha(theme.palette.success.main, 0.2)}`,
                              }}
                            />
                          ) : (
                            <Chip
                              icon={<PortableWifiOff fontSize="small" />}
                              label={t('common.status.stopped')}
                              size="small"
                              color="error"
                              sx={{ 
                                borderRadius: 1,
                                minWidth: 80,
                                boxShadow: `0 2px 8px ${alpha(theme.palette.error.main, 0.2)}`,
                              }}
                            />
                          )}
                        </TableCell>
                        <TableCell align="center">
                          {deviceWithBandwidth && (
                            <Tooltip title={t('device.details')}>
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => viewTrafficDetails(device)}
                                sx={{ 
                                  backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                  '&:hover': {
                                    backgroundColor: alpha(theme.palette.primary.main, 0.2),
                                    transform: 'scale(1.1)'
                                  },
                                  transition: 'all 0.2s ease'
                                }}
                              >
                                <Speed />
                              </IconButton>
                            </Tooltip>
                          )}
                        </TableCell>
                        <TableCell>
                          <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            gap: 0.5,
                            p: 0.5,
                            borderRadius: 1,
                            width: 'fit-content'
                          }}>
                            <TrendingDown fontSize="small" color="info" />
                            <Typography variant="body2" fontWeight="medium">
                              {deviceWithBandwidth ? formatBytes(deviceWithBandwidth.totalReceived) : '-'}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell>
                          <Box sx={{ 
                            display: 'flex', 
                            alignItems: 'center', 
                            gap: 0.5,
                            p: 0.5,
                            borderRadius: 1,
                            width: 'fit-content'
                          }}>
                            <TrendingUp fontSize="small" color="success" />
                            <Typography variant="body2" fontWeight="medium">
                              {deviceWithBandwidth ? formatBytes(deviceWithBandwidth.totalSent) : '-'}
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <IconButton
                            onClick={(e) => handleMenuOpen(e, device)}
                            size="small"
                            sx={{ 
                              mx: 0.5,
                              backgroundColor: alpha(theme.palette.primary.main, 0.05),
                              '&:hover': {
                                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                                transform: 'scale(1.1)'
                              },
                              transition: 'all 0.2s ease'
                            }}
                          >
                            <MoreHoriz />
                          </IconButton>
                          <DeviceActionsMenu device={device} />
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
              <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between',
                alignItems: 'center',
                p: 2,
                backgroundColor: alpha(theme.palette.background.default, 0.4),
                borderTop: `1px solid ${alpha(theme.palette.divider, 0.1)}`
              }}>
                <StyledButton 
                  variant="contained" 
                  startIcon={<Add />} 
                  size="small"
                  onClick={openCreateDeviceDialog}
                >
                  {t('device.add.add')}
                </StyledButton>
                <TablePagination
                  component="div"
                  count={totalDevices}
                  page={page}
                  onPageChange={handleChangePage}
                  rowsPerPage={rowsPerPage}
                  onRowsPerPageChange={handleChangeRowsPerPage}
                  rowsPerPageOptions={[5, 10, 25]}
                  sx={{
                    '.MuiTablePagination-select': {
                      borderRadius: 1,
                      mr: 1
                    },
                    '.MuiTablePagination-selectIcon': {
                      color: theme.palette.primary.main
                    },
                    '.MuiTablePagination-displayedRows': {
                      fontWeight: 500,
                      color: theme.palette.text.secondary
                    }
                  }}
                />
              </Box>
            </TableContainer>
          </Card>
        )}
      </Box>
    </Box>
  );
};

export default WireguardManager; 