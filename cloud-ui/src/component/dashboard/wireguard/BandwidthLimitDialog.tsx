import React, { useState } from 'react';
import {
  <PERSON>,
  Typography,
  FormControlLabel,
  Switch,
  Slider,
  InputAdornment,
  CircularProgress,
  Alert,
  Paper,
  Chip,
  Button,
  TextField,
  alpha
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { getBandwidthLimit, setBandwidthLimit, removeBandwidthLimit } from '../../../services/wireguardService';
import { useTheme } from '@mui/material/styles';

// Supported bandwidth presets in Kbps
const BANDWIDTH_PRESETS = [
  { value: 512, label: '512 Kbps' },
  { value: 1 * 1024, label: '1 Mbps' },
  { value: 5 * 1024, label: '5 Mbps' },
  { value: 10 * 1024, label: '10 Mbps' },
  { value: 20 * 1024, label: '20 Mbps' },
  { value: 30 * 1024, label: '30 Mbps' },
];

interface BandwidthLimitContentProps {
  deviceName: string;
  enterpriseId: string;
  onClose: () => void;
  onSuccess: () => void;
}

/**
 * BandwidthLimitContent - 带宽限制设置内容组件
 * 
 * 该组件应通过DialogContext的openDialog方法来显示，例如:
 * dialogContext.openDialog(
 *   <BandwidthLimitContent 
 *     deviceName={device.Name} 
 *     enterpriseId={device.EnterpriseId}
 *     onClose={() => {}} 
 *     onSuccess={() => {}} 
 *   />,
 *   t('wireguard.bandwidth.limitTitle'),
 *   <SpeedIcon color="primary" />
 * );
 */
const BandwidthLimitContent: React.FC<BandwidthLimitContentProps> = ({
  deviceName,
  enterpriseId,
  onClose,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const theme = useTheme();

  const [loading, setLoading] = useState(false);
  const [currentLimit, setCurrentLimit] = useState<number | null>(null);
  const [newLimit, setNewLimit] = useState<number>(10 * 1024); // Default 10 Mbps
  const [enableLimit, setEnableLimit] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 在组件首次渲染时获取当前带宽限制信息
  React.useEffect(() => {
    fetchCurrentLimit();
  }, []);

  const fetchCurrentLimit = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await getBandwidthLimit(deviceName);
      
      if (response && response.limit) {
        setCurrentLimit(response.limit);
        setNewLimit(response.limit);
        setEnableLimit(true);
      } else {
        setCurrentLimit(null);
        setEnableLimit(false);
      }
    } catch (err: any) {
      setError(err.response?.data?.error || t('common.messages.fetchError', { item: t('wireguard.bandwidth.limitTitle') }));
      console.error('Failed to fetch bandwidth limit:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleApplyLimit = async () => {
    setLoading(true);
    setError(null);
    try {
      if (enableLimit) {
        // Apply bandwidth limit
        await setBandwidthLimit(deviceName, newLimit);
      } else {
        // Remove bandwidth limit
        await removeBandwidthLimit(deviceName);
      }
      setCurrentLimit(enableLimit ? newLimit : null);
      onSuccess(); // 通知父组件操作成功
      onClose(); // 关闭对话框
    } catch (err: any) {
      setError(err.response?.data?.error || t('common.messages.updateError', { item: t('wireguard.bandwidth.limitTitle') }));
      console.error('Failed to apply bandwidth limit:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSliderChange = (_: Event, value: number | number[]) => {
    setNewLimit(value as number);
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = Number(event.target.value);
    if (!isNaN(value) && value >= 0) {
      setNewLimit(value);
    }
  };

  const formatBandwidth = (value: number) => {
    if (value >= 1024) {
      return `${(value / 1024).toFixed(1)} Mbps`;
    }
    return `${value} Kbps`;
  };

  return (
    <Box sx={{ p: 2 }}>
      {loading && !currentLimit ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 0 }}>
          <CircularProgress 
            size={50} 
            thickness={4} 
            sx={{ 
              color: theme.palette.primary.main,
              '& .MuiCircularProgress-circle': {
                strokeLinecap: 'round',
              }
            }} 
          />
        </Box>
      ) : (
        <>
          {error && (
            <Alert 
              severity="error" 
              sx={{ 
                mb: 2,
                borderRadius: 2,
                boxShadow: `0 2px 8px ${alpha(theme.palette.error.main, 0.15)}`
              }}
            >
              {error}
            </Alert>
          )}
          
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
              {t('wireguard.bandwidth.deviceInfo')}
            </Typography>
            <Paper 
              variant="outlined" 
              sx={{ 
                p: 2, 
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.default, 0.5),
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              }}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                      {t('device.fields.name')}:
                  </Typography>
                  <Chip 
                    label={deviceName}
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ 
                      borderRadius: 1,
                      fontWeight: 500
                    }}
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {t('common.fields.enterpriseId')}:
                  </Typography>
                  <Chip 
                    label={enterpriseId}
                    size="small"
                    color="primary"
                    variant="outlined"
                    sx={{ 
                      borderRadius: 1,
                      fontWeight: 500
                    }}
                  />
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body2" color="text.secondary">
                    {t('wireguard.bandwidth.currentLimit')}:
                  </Typography>
                  <Chip 
                    label={currentLimit ? formatBandwidth(currentLimit) : t('wireguard.bandwidth.noLimit')}
                    size="small"
                    color={currentLimit ? "primary" : "default"}
                    variant={currentLimit ? "filled" : "outlined"}
                    sx={{ 
                      borderRadius: 1,
                      fontWeight: 500,
                      backgroundColor: currentLimit ? undefined : alpha(theme.palette.action.disabled, 0.1)
                    }}
                  />
                </Box>
              </Box>
            </Paper>
          </Box>
          
          <FormControlLabel
            control={
              <Switch
                checked={enableLimit}
                onChange={(e) => setEnableLimit(e.target.checked)}
                color="primary"
              />
            }
            label={
              <Typography sx={{ fontWeight: 500 }}>
                {t('wireguard.bandwidth.enableLimit')}
              </Typography>
            }
            sx={{ mb: 2 }}
          />
          
          {enableLimit && (
            <>
              <Box sx={{ mb: 3 }}>
                <Typography id="bandwidth-slider" gutterBottom sx={{ fontWeight: 500 }}>
                  {t('wireguard.bandwidth.limitValue')}: {formatBandwidth(newLimit)}
                </Typography>
                <Slider
                  value={newLimit}
                  onChange={handleSliderChange}
                  aria-labelledby="bandwidth-slider"
                  min={128}
                  max={1024 * 30}
                  disabled={loading}
                  sx={{
                    '& .MuiSlider-thumb': {
                      '&:hover, &.Mui-focusVisible': {
                        boxShadow: `0 0 0 8px ${alpha(theme.palette.primary.main, 0.1)}`
                      }
                    }
                  }}
                />
                <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                  <TextField
                    value={newLimit}
                    onChange={handleInputChange}
                    type="number"
                    size="small"
                    InputProps={{
                      endAdornment: <InputAdornment position="end">Kbps</InputAdornment>,
                      sx: { borderRadius: 1 }
                    }}
                    sx={{ width: '150px' }}
                    disabled={loading}
                  />
                  <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                    ({formatBandwidth(newLimit)})
                  </Typography>
                </Box>
              </Box>
              
              <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 600, color: theme.palette.primary.main }}>
                {t('wireguard.bandwidth.presets')}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', mb: 2 }}>
                {BANDWIDTH_PRESETS.map((preset) => (
                  <Button
                    key={preset.value}
                    variant={newLimit === preset.value ? "contained" : "outlined"}
                    size="small"
                    onClick={() => setNewLimit(preset.value)}
                    sx={{ 
                      borderRadius: 1,
                      minWidth: '80px',
                      textTransform: 'none',
                      fontWeight: 500,
                      ...(newLimit === preset.value && {
                        boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.25)}`,
                      })
                    }}
                    disabled={loading}
                  >
                    {preset.label}
                  </Button>
                ))}
              </Box>
              
              <Alert 
                severity="info"
                sx={{ 
                  borderRadius: 2,
                  boxShadow: `0 2px 8px ${alpha(theme.palette.info.main, 0.15)}`
                }}
              >
                {t('wireguard.bandwidth.limitDescription')}
              </Alert>
            </>
          )}
        </>
      )}

      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'flex-end', 
        gap: 1, 
        mt: 3
      }}>
        <Button 
          onClick={onClose}
          variant="outlined"
          disabled={loading}
          sx={{ 
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 500,
            '&:hover': {
              backgroundColor: alpha(theme.palette.text.primary, 0.05)
            }
          }}
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleApplyLimit}
          variant="contained"
          color="primary"
          disabled={loading}
          startIcon={loading ? <CircularProgress size={20} /> : null}
          sx={{ 
            borderRadius: 2,
            textTransform: 'none',
            fontWeight: 500,
            boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`,
            '&:hover': {
              boxShadow: `0 6px 16px ${alpha(theme.palette.primary.main, 0.4)}`,
              transform: 'translateY(-2px)'
            },
            transition: 'all 0.2s ease'
          }}
        >
          {t('common.confirm')}
        </Button>
      </Box>
    </Box>
  );
};

export default BandwidthLimitContent; 