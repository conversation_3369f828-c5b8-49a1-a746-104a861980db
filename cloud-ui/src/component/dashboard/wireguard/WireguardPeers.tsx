import React, { useState, useEffect, useContext, useCallback, useMemo } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  Grid2,
  IconButton,
  TextField,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  alpha,
  styled,
  CardHeader,
  Divider,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Chip,
  useTheme,
} from '@mui/material';
import {
  Add,
  Delete,
  Refresh,
  QrCode2,
  Download,
  Router,
  DevicesOther,
  WifiTethering,
  PortableWifiOff,
  MoreHoriz,
  CheckCircle,
  Error as ErrorIcon,
  TrendingDown,
  TrendingUp,
} from '@mui/icons-material';
import { 
  getDeviceAndPeers, 
  getPeersStatus, 
  createPeer, 
  deletePeer, 
  getPeerQRCode, 
  formatBytes,
  Device,
  Peer,
  PeerStatus,
  getPeerAllowedIPs,
  savePeerAllowedIPs
} from '../../../services/wireguardService';
import { useSnackbar } from '../../../context/SnackbarContext';
import { DialogContext } from '../../../context/GlobalDialog';
import StickyBreadcrumbs from '../../utils/StickyBreadcrumbs';
import { useTranslation } from 'react-i18next';
import { useParams } from 'react-router-dom';
import { getUserInfoFromStorage } from '../../../context/UserContext';

// 自定义样式组件
const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: theme.shape.borderRadius * 1.5,
  textTransform: 'none',
  fontWeight: 600,
  boxShadow: `0 4px 8px ${alpha(theme.palette.primary.main, 0.15)}`,
  '&:hover': {
    boxShadow: `0 6px 12px ${alpha(theme.palette.primary.main, 0.2)}`,
    transform: 'translateY(-2px)'
  },
  transition: 'all 0.2s ease',
}));

// Custom hook to handle enterprise ID resolution
const useEnterpriseId = () => {
  const { enterpriseId } = useParams<{ enterpriseId?: string }>();
  
  const getEffectiveEnterpriseId = useCallback(() => {
    if (!enterpriseId || enterpriseId === ":enterpriseId") {
      return getUserInfoFromStorage().enterprise_id || '';
    }
    return enterpriseId;
  }, [enterpriseId]);
  
  return { 
    enterpriseId, 
    effectiveEnterpriseId: getEffectiveEnterpriseId() 
  };
};

// 添加格式化时间的辅助函数
const formatHandshake = (dateString: string, t: any): string => {
  const date = new Date(dateString);
  // 检查是否为"0001-01-01T00:00:00Z"这样的默认值
  if (date.getFullYear() === 1) {
    return t('time.never');
  }
  // 计算与当前时间的差值（毫秒）
  const diffMs = Date.now() - date.getTime();
  
  // 转换为更易读的格式
  const seconds = Math.floor(diffMs / 1000);
  if (seconds < 60) {
    const key = seconds === 1 ? 'time.second' : 'time.seconds';
    return `${seconds} ${t(key)} ${t('time.ago')}`;
  }
  
  const minutes = Math.floor(seconds / 60);
  if (minutes < 60) {
    const key = minutes === 1 ? 'time.minute' : 'time.minutes';
    return `${minutes} ${t(key)} ${t('time.ago')}`;
  }
  
  const hours = Math.floor(minutes / 60);
  if (hours < 24) {
    const key = hours === 1 ? 'time.hour' : 'time.hours';
    return `${hours} ${t(key)} ${t('time.ago')}`;
  }
  
  const days = Math.floor(hours / 24);
  const key = days === 1 ? 'time.day' : 'time.days';
  return `${days} ${t(key)} ${t('time.ago')}`;
};

// Component for device details
interface DeviceDetailsCardProps {
  device: Device | null;
  deviceStatus: 'running' | 'stopped';
}

const DeviceDetailsCard = ({ device, deviceStatus }: DeviceDetailsCardProps) => {
  const { t } = useTranslation();
  const theme = useTheme();
  
  if (!device) return null;
  
  const deviceFields = [
    { field: 'deviceName', value: device.Name },
    { field: 'ip', value: device.IP },
    { field: 'listenPort', value: device.ListenPort },
    { field: 'endpoint', value: device.Endpoint },
    { field: 'publicKey', value: device.PublicKey}
  ];
  
  return (
    <Card 
      elevation={0} 
      sx={{ 
        mb: 3, 
        borderRadius: 3,
        border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
        boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
        transition: 'all 0.3s ease',
        '&:hover': {
          boxShadow: `0 6px 24px ${alpha(theme.palette.common.black, 0.08)}`,
        }
      }}
    >
      <CardHeader 
        title={
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <DevicesOther color="primary" />
            <Typography variant="h6" sx={{ fontWeight: 600 }}>
              {t('wireguard.common.deviceDetails')}
            </Typography>
          </Box>
        }
        action={
          <Chip
            size='small'
            icon={deviceStatus === 'running' ? <WifiTethering /> : <PortableWifiOff />}
            label={t(`common.status.${deviceStatus}`)}
            color={deviceStatus === 'running' ? "success" : "error"}
            variant="filled"
            sx={{ 
              borderRadius: 1.5,
              fontWeight: 500,
              boxShadow: (theme) => `0 2px 8px ${alpha(
                deviceStatus === 'running' ? theme.palette.success.main : theme.palette.error.main, 
                0.2
              )}`,
            }}
          />
        }
        sx={{
          backgroundColor: alpha(theme.palette.primary.main, 0.03),
          borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
        }}
      />
      <CardContent sx={{ p: 3 }}>
        <Grid2 container spacing={3}>
          {deviceFields.map((item, index) => (
            <Grid2 size={{xs:12, sm:6, md:item.field === 'publicKey' ? 6 : 3}} key={`field-${index}`}>
              <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column', 
                gap: 0.5,
                p: 1.5,
                borderRadius: 2,
                backgroundColor: alpha(theme.palette.background.default, 0.5),
                border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              }}>
                <Typography variant="caption" color="text.secondary" sx={{ fontWeight: 500 }}>
                  {t(`wireguard.common.${item.field}`)}
                </Typography>
                {item.field === 'ip' ? (
                  <Chip 
                    label={item.value} 
                    size="small" 
                    variant="outlined" 
                    color="primary"
                    sx={{ 
                      borderRadius: 1,
                      fontSize: '0.75rem',
                      height: 24,
                      maxWidth: 'fit-content',
                      fontFamily: 'monospace'
                    }} 
                  />
                ) : (
                  <Typography variant="body2" sx={{ 
                    wordBreak: 'break-all',
                    fontFamily: item.field === 'publicKey' ? 'monospace' : 'inherit',
                    fontSize: item.field === 'publicKey' ? '0.75rem' : 'inherit',
                    fontWeight: 500
                  }}>
                    {item.value}
                  </Typography>
                )}
              </Box>
            </Grid2>
          ))}
        </Grid2>
      </CardContent>
    </Card>
  );
};

// Component for peer creation dialog content
interface CreatePeerDialogContentProps {
  onSubmit: (peerName: string) => Promise<void>;
  onCancel: () => void;
}

const CreatePeerDialogContent = ({ onSubmit, onCancel }: CreatePeerDialogContentProps) => {
  const { t } = useTranslation();
  const [peerName, setPeerName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const handleCreate = async () => {
    if (!peerName.trim()) return;
    
    setIsSubmitting(true);
    await onSubmit(peerName);
    setIsSubmitting(false);
  };
  
  return (
    <Box sx={{ p: 2 }}>
      <TextField
        label={t('wireguard.manager.peerName')}
        fullWidth
        value={peerName}
        onChange={(e) => setPeerName(e.target.value)}
        margin="normal"
        required
        variant="outlined"
        InputProps={{
          sx: { borderRadius: 1.5 }
        }}
      />
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button 
          onClick={onCancel}
          sx={{ borderRadius: 1.5, textTransform: 'none' }}
        >
          {t('common.cancel')}
        </Button>
        <Button 
          onClick={handleCreate} 
          variant="contained" 
          disabled={isSubmitting || !peerName.trim()}
          sx={{ borderRadius: 1.5, textTransform: 'none' }}
        >
          {isSubmitting ? <CircularProgress size={24} /> : t('common.create')}
        </Button>
      </Box>
    </Box>
  );
};

// Component for peer allowed IPs dialog content
interface AllowedIPsDialogContentProps {
  peer: Peer;
  deviceName: string;
  enterpriseId: string;
  onSave: (allowedIPs: string[]) => Promise<void>;
  onCancel: () => void;
}

const AllowedIPsDialogContent = ({ peer, deviceName, enterpriseId, onSave, onCancel }: AllowedIPsDialogContentProps) => {
  const { t } = useTranslation();
  const [allowedIPs, setAllowedIPs] = useState<string[]>([]);
  const [newIP, setNewIP] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  
  // 验证IP地址格式的正则表达式
  const ipRegex = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])(\/([0-9]|[1-2][0-9]|3[0-2]))$/;
  
  // 加载允许的IP列表
  useEffect(() => {
    const fetchAllowedIPs = async () => {
      try {
        setIsLoading(true);
        const ips = await getPeerAllowedIPs(deviceName, peer.ID);
        setAllowedIPs(ips);
      } catch (error) {
        console.error('Failed to fetch allowed IPs:', error);
        setErrorMessage(t('wireguard.peers.failedToFetchIPs'));
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchAllowedIPs();
  }, [deviceName, peer.ID, t]);
  
  // 添加新IP
  const handleAddIP = () => {
    if (!newIP) return;
    
    if (!ipRegex.test(newIP)) {
      setErrorMessage(t('wireguard.peers.invalidIPFormat'));
      return;
    }
    
    if (allowedIPs.includes(newIP)) {
      setErrorMessage(t('wireguard.peers.ipAlreadyExists'));
      return;
    }
    
    setAllowedIPs([...allowedIPs, newIP]);
    setNewIP('');
    setErrorMessage('');
  };
  
  // 删除IP
  const handleDeleteIP = (ip: string) => {
    setAllowedIPs(allowedIPs.filter(item => item !== ip));
  };
  
  // 保存更改
  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onSave(allowedIPs);
    } finally {
      setIsSaving(false);
    }
  };
  
  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle1" gutterBottom fontWeight={600}>
        {t('wireguard.peers.managePeerIPs', { name: peer.Name })}
      </Typography>
      
      {isLoading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
          <CircularProgress size={30} />
        </Box>
      ) : (
        <>
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              label={t('wireguard.peers.newAllowedIP')}
              placeholder="***********/24"
              value={newIP}
              onChange={(e) => setNewIP(e.target.value)}
              error={!!errorMessage}
              helperText={errorMessage || t('wireguard.peers.ipFormatHelp')}
              InputProps={{
                endAdornment: (
                  <Button 
                    onClick={handleAddIP} 
                    variant="outlined" 
                    size="small"
                    sx={{ ml: 1, borderRadius: 1.5, textTransform: 'none' }}
                  >
                    {t('common.add')}
                  </Button>
                ),
                sx: { borderRadius: 1.5 }
              }}
            />
          </Box>
          
          <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 500 }}>
            {t('wireguard.peers.currentIPs')} ({allowedIPs.length})
          </Typography>
          
          {allowedIPs.length > 0 ? (
            <Box sx={{ 
              maxHeight: '200px', 
              overflowY: 'auto',
              border: (theme) => `1px solid ${alpha(theme.palette.divider, 0.1)}`,
              borderRadius: 1.5,
              p: 1,
              mb: 2
            }}>
              {allowedIPs.map((ip, index) => (
                <Chip
                  key={index}
                  label={ip}
                  onDelete={() => handleDeleteIP(ip)}
                  sx={{ 
                    m: 0.5, 
                    borderRadius: 1.5,
                    '& .MuiChip-deleteIcon': {
                      color: (theme) => alpha(theme.palette.error.main, 0.7),
                      '&:hover': {
                        color: (theme) => theme.palette.error.main,
                      }
                    }
                  }}
                />
              ))}
            </Box>
          ) : (
            <Alert 
              severity="info" 
              sx={{ mb: 2, borderRadius: 1.5 }}
            >
              {t('wireguard.peers.noAllowedIPs')}
            </Alert>
          )}
        </>
      )}
      
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
        <Button 
          onClick={onCancel}
          sx={{ borderRadius: 1.5, textTransform: 'none' }}
        >
          {t('common.cancel')}
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={isSaving || isLoading}
          sx={{ borderRadius: 1.5, textTransform: 'none' }}
        >
          {isSaving ? <CircularProgress size={24} /> : t('common.save')}
        </Button>
      </Box>
    </Box>
  );
};

const WireguardPeers: React.FC = () => {
  const { t } = useTranslation();
  const { effectiveEnterpriseId } = useEnterpriseId();
  const [device, setDevice] = useState<Device | null>(null);
  const [peers, setPeers] = useState<Peer[]>([]);
  const [peersStatus, setPeersStatus] = useState<PeerStatus[]>([]);
  const [loading, setLoading] = useState(false);
  const [deviceStatus, setDeviceStatus] = useState<'running' | 'stopped'>('stopped');
  
  // UI state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPeer, setSelectedPeer] = useState<Peer | null>(null);

  const { showSnackbar } = useSnackbar();
  const dialogContext = useContext(DialogContext);

  // Create breadcrumb items as a memoized value
  const breadcrumbItems = useMemo(() => [
    { label: t('routes.wireguard'), href: '/dashboard/wireguard' },
    { label: t('wireguard.common.peers'), isCurrent: true },
  ], [t]);

  // Fetch peers status
  const fetchPeersStatus = useCallback(async () => {
    if (!device?.Name) return;
    
    try {
      const statusResponse = await getPeersStatus(device.Name);
      setDeviceStatus(statusResponse ? 'running' : 'stopped');

      if (statusResponse) {
        setPeersStatus(statusResponse.data || []);
      }
    } catch (error) {
      setDeviceStatus('stopped');
      console.error('Failed to fetch peer status:', error);
    }
  }, [device?.Name]);

  // Fetch device and peers data
  const fetchDeviceAndPeers = useCallback(async () => {
    if (!effectiveEnterpriseId) {
      showSnackbar(t('common.validation.required', { field: t('common.fields.enterpriseId') }), 'error');
      return;
    }
    
    setLoading(true);
    try {
      const deviceResponse = await getDeviceAndPeers(effectiveEnterpriseId);
      
      if (deviceResponse) {
        setDevice(deviceResponse.data);
        setPeers(deviceResponse.peers || []);
        
        // Immediately fetch status if we have a device
        if (deviceResponse.data?.Name) {
          fetchPeersStatus();
        }
      }
    } catch (error: any) {
      const errorMsg = error.response?.data?.error || t('common.unknownError');
      showSnackbar(`fetchDataError: ${errorMsg}`, 'error');
    } finally {
      setLoading(false);
    }
  }, [effectiveEnterpriseId, fetchPeersStatus, showSnackbar, t]);

  // Set up initial data fetch and status polling
  useEffect(() => {
    fetchDeviceAndPeers();
    
    // Only set up polling if we have a device name
    if (!device?.Name) return;
    
    // Set up polling for peer status
    const statusTimer = setInterval(fetchPeersStatus, 2000);
    
    // Cleanup
    return () => clearInterval(statusTimer);
  }, [fetchDeviceAndPeers, fetchPeersStatus, device?.Name]);

  // Handle peer creation
  const handleCreatePeer = async (peerName: string) => {
    if (!peerName.trim()) {
      showSnackbar(t('common.validation.required', { field: t('wireguard.manager.peerName') }), 'warning');
      return;
    }

    if (!device) {
      showSnackbar(t('common.noData'), 'error');
      return;
    }

    setLoading(true);
    try {
      const response = await createPeer(peerName, effectiveEnterpriseId, device.Name);
      
      if (response.message === 'Peer created successfully') {
        showSnackbar(t('wireguard.manager.peerCreated'), 'success');
        fetchDeviceAndPeers(); // Refresh data
        dialogContext?.closeDialog();
      } else {
        showSnackbar(t('common.messages.createError', { item: t('wireguard.common.peers') }), 'error');
      }
    } catch (error: any) {
      const errorMsg = error.response?.data?.error || t('common.unknownError');
      showSnackbar(`${t('common.messages.createError', { item: t('wireguard.common.peers') })}: ${errorMsg}`, 'error');
    } finally {
      setLoading(false);
    }
  };

  // Handle peer deletion
  const handleDeletePeer = async (peer: Peer) => {
    if (!dialogContext) return;
    
    const confirmed = await dialogContext.confirm(
      t('common.confirm'),
      <Box sx={{p:2}}>
        <Typography variant="body1" gutterBottom>
          {t('wireguard.peers.deleteConfirm', { name: peer.Name })}
        </Typography>
        <Typography variant="body2" color="error" gutterBottom>
          {t('common.deleteConfirm.warning')}
        </Typography>
      </Box>
    );

    if (!confirmed) return;

    setLoading(true);
    try {
      await deletePeer(peer.ID, device?.Name || '', effectiveEnterpriseId);
      
      showSnackbar(t('wireguard.manager.peerDeleted'), 'success');
      fetchDeviceAndPeers(); // Refresh data
    } catch (error: any) {
      const errorMsg = error.response?.data?.error || t('common.unknownError');
      showSnackbar(`${t('common.messages.deleteError', { item: t('wireguard.common.peers') })}: ${errorMsg}`, 'error');
    } finally {
      setLoading(false);
      handleMenuClose();
    }
  };

  // Show QR code for peer configuration
  const handleShowQRCode = async (peer: Peer) => {
    setLoading(true);
    try {
      const response = await getPeerQRCode(peer.ID, effectiveEnterpriseId);
      
      if (dialogContext && response.svg) {
        dialogContext.openDialog(
          <Box sx={{ textAlign: 'center', p: 3 }}>
            <img src={`${response.svg}`} alt="QR Code" style={{ maxWidth: '100%' }}/>
          </Box>,
          t('wireguard.peers.qrCode')
        );
      }
    } catch (error: any) {
      const errorMsg = error.response?.data?.error || t('common.unknownError');
      showSnackbar(`${t('common.messages.fetchError', { item: t('wireguard.peers.qrCode') })}: ${errorMsg}`, 'error');
    } finally {
      setLoading(false);
      handleMenuClose();
    }
  };

  // Download configuration for peer
  const handleDownloadConfig = async (peer: Peer) => {
    if (!device) return;
    
    try {
      // Generate config file content
      const configContent = `# ${peer.ID}
[Interface]
PrivateKey = ${peer.PrivateKey}
Address = ${peer.IP}/24
DNS = ${peer.DNS}

[Peer]
PublicKey = ${device.PublicKey}
PresharedKey = ${peer.PresharedKey}
AllowedIPs = ${peer.AllowedIPs.join(', ')}
PersistentKeepalive = 25
Endpoint = ${device.Endpoint}
`;
      
      // Create download link
      const blob = new Blob([configContent], { type: 'text/plain' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${peer.Name}.conf`;
      document.body.appendChild(a);
      a.click();
      
      // Cleanup
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);
    } catch (error: any) {
      showSnackbar(error.message || t('common.unknownError'), 'error');
    } finally {
      handleMenuClose();
    }
  };

  // Menu handling
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, peer: Peer) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedPeer(peer);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPeer(null);
  };
  
  // Render peer table row
  const renderPeerRow = useCallback((peer: Peer) => {
    const peerStatus = peersStatus.find(status => status.PublicKey === peer.PublicKey);
    // 修改判断逻辑：如果LastHandshake存在且最后握手时间在2分钟内，则认为是已连接状态
    const isConnected = peerStatus?.LastHandshake && (() => {
      const lastHandshakeTime = new Date(peerStatus.LastHandshake);
      // 检查是否为默认值"0001-01-01T00:00:00Z"
      if (lastHandshakeTime.getFullYear() === 1) return false;
      // 计算与当前时间的差值（毫秒）
      const diffMs = Date.now() - lastHandshakeTime.getTime();
      // 如果在2分钟内（120000毫秒），则认为是已连接
      return diffMs <= 120000;
    })();
    
    return (
      <TableRow key={peer.ID} hover sx={{ transition: 'all 0.2s' }}>
        <TableCell 
          align="center"
          sx={{ 
            borderLeft: (theme) => `4px solid ${isConnected ? theme.palette.success.main : theme.palette.error.main}` 
          }}
        >
          <Typography fontWeight="medium">{peer.Name}</Typography>
        </TableCell>
        <TableCell align="center">
          <Chip 
            label={peer.IP} 
            size="small" 
            variant="outlined" 
            sx={{ 
              borderRadius: 1,
              fontSize: '0.75rem',
              height: 24
            }} 
          />
        </TableCell>
        <TableCell align="center">
          {peerStatus ? (
            <Chip
              icon={isConnected ? <CheckCircle /> : <ErrorIcon />}
              label={formatHandshake(peerStatus.LastHandshake, t)}
              size="small"
              color={isConnected ? "success" : "error"}
              variant={isConnected ? "filled" : "outlined"}
              sx={{ 
                borderRadius: 1,
                minWidth: 120,
                height: 24
              }}
            />
          ) : (
            <Chip
              icon={<ErrorIcon />}
              label={t('common.noData')}
              size="small"
              color="default"
              variant="outlined"
              sx={{ 
                borderRadius: 1,
                minWidth: 120,
                height: 24
              }}
            />
          )}
        </TableCell>
        <TableCell align="center">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
            <TrendingDown fontSize="small" color="info" />
            <Typography variant="body2">
              {peerStatus ? formatBytes(peerStatus.ReceiveBytes) : '0 B'}
            </Typography>
          </Box>
        </TableCell>
        <TableCell align="center">
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 0.5 }}>
            <TrendingUp fontSize="small" color="success" />
            <Typography variant="body2">
              {peerStatus ? formatBytes(peerStatus.TransmitBytes) : '0 B'}
            </Typography>
          </Box>
        </TableCell>
        <TableCell align="center">
          <IconButton
            onClick={(e) => handleMenuOpen(e, peer)}
            size="small"
            sx={{ 
              mx: 0.5,
              backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.05),
              '&:hover': {
                backgroundColor: (theme) => alpha(theme.palette.primary.main, 0.1),
              }
            }}
          >
            <MoreHoriz />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl) && selectedPeer?.ID === peer.ID}
            onClose={handleMenuClose}
            PaperProps={{
              elevation: 3,
              sx: { borderRadius: 2, mt: 0.5 }
            }}
          >
            <MenuItem onClick={() => handleShowQRCode(peer)}>
              <ListItemIcon>
                <QrCode2 color="primary" />
              </ListItemIcon>
              <ListItemText>{t('wireguard.peers.showQRCode')}</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => handleDownloadConfig(peer)}>
              <ListItemIcon>
                <Download color="primary" />
              </ListItemIcon>
              <ListItemText>{t('wireguard.peers.downloadConfig')}</ListItemText>
            </MenuItem>
            <MenuItem onClick={() => {
              if (!dialogContext || !device) return;
              
              handleMenuClose(); // 关闭菜单
              
              dialogContext.openDialog(
                <AllowedIPsDialogContent 
                  peer={peer}
                  deviceName={device.Name}
                  enterpriseId={effectiveEnterpriseId}
                  onSave={async (allowedIPs) => {
                    try {
                      await savePeerAllowedIPs(device.Name, effectiveEnterpriseId, peer.ID, allowedIPs);
                      showSnackbar(t('wireguard.peers.allowedIPsSaved'), 'success');
                      dialogContext.closeDialog();
                    } catch (error: any) {
                      const errorMsg = error.response?.data?.error || t('common.unknownError');
                      showSnackbar(`${t('wireguard.peers.failedToSaveIPs')}: ${errorMsg}`, 'error');
                    }
                  }}
                  onCancel={() => dialogContext.closeDialog()}
                />,
                t('wireguard.peers.allowedIPsTitle')
              );
            }}>
              <ListItemIcon>
                <Router color="primary" />
              </ListItemIcon>
              <ListItemText>{t('wireguard.peers.manageAllowedIPs')}</ListItemText>
            </MenuItem>
            <Divider />
            <MenuItem onClick={() => handleDeletePeer(peer)}>
              <ListItemIcon>
                <Delete color="error" />
              </ListItemIcon>
              <ListItemText>{t('common.delete')}</ListItemText>
            </MenuItem>
          </Menu>
        </TableCell>
      </TableRow>
    );
  }, [anchorEl, device, dialogContext, effectiveEnterpriseId, handleDeletePeer, handleDownloadConfig, handleMenuClose, handleMenuOpen, handleShowQRCode, peersStatus, selectedPeer, showSnackbar, t]);

  // Table header cells
  const tableHeaders = useMemo(() => [
    { key: 'name', label: t('device.fields.name') },
    { key: 'ip', label: t('wireguard.peers.peerIP') },
    { key: 'lastHandshake', label: t('wireguard.common.lastHandshake') },
    { key: 'received', label: t('wireguard.common.received') },
    { key: 'transmitted', label: t('wireguard.common.transmitted') },
    { key: 'actions', label: t('common.actions') }
  ], [t]);

  const theme = useTheme();

  // Open create peer dialog
  const openCreatePeerDialog = useCallback(() => {
    if (!dialogContext) return;
    
    dialogContext.openDialog(
      <CreatePeerDialogContent 
        onSubmit={handleCreatePeer}
        onCancel={() => dialogContext.closeDialog()}
      />,
      t('wireguard.peers.createPeer')
    );
  }, [dialogContext, handleCreatePeer, t]);

  return (
    <Box>
      <StickyBreadcrumbs items={breadcrumbItems} />
      
      <Card 
        elevation={0}
        sx={{ 
          mb: 3, 
          borderRadius: 3,
          border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
        }}
      >
        <CardHeader 
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Router color="primary" />
              <Typography variant="h6" sx={{ fontWeight: 600 }}>
                {t('wireguard.peers.title')}
              </Typography>
            </Box>
          }
          action={
            <Box sx={{ display: 'flex', gap: 1 }}>
              <StyledButton 
                variant="outlined" 
                size="small"
                startIcon={<Refresh />} 
                onClick={fetchDeviceAndPeers}
              >
                {t('common.refresh')}
              </StyledButton>
              <StyledButton 
                variant="contained" 
                size="small"
                startIcon={<Add />} 
                onClick={openCreatePeerDialog}
              >
                {t('wireguard.peers.newPeer')}
              </StyledButton>
            </Box>
          }
          sx={{
            backgroundColor: alpha(theme.palette.primary.main, 0.03),
            borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
          }}
        />
      </Card>

      {/* Device details card */}
      <DeviceDetailsCard device={device} deviceStatus={deviceStatus} />

      {/* Peers table */}
      {loading && peers.length === 0 ? (
        <Card 
          variant="outlined" 
          sx={{ 
            display: 'flex', 
            justifyContent: 'center',
            alignItems: 'center',
            p: 5,
            backgroundColor: alpha(theme.palette.background.paper, 0.6),
            borderRadius: 3,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          }}
        >
          <CircularProgress 
            size={50} 
            thickness={4} 
            sx={{ 
              mr: 2,
              color: theme.palette.primary.main,
              '& .MuiCircularProgress-circle': {
                strokeLinecap: 'round',
              }
            }} 
          />
          <Typography 
            variant="h6" 
            color="text.secondary"
            sx={{ 
              fontWeight: 500,
              opacity: 0.8
            }}
          >
            {t('common.loading')}
          </Typography>
        </Card>
      ) : peers.length === 0 ? (
        <Alert 
          severity="info" 
          sx={{ 
            borderRadius: 3,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          }}
        >
          {t('wireguard.peers.noPeers')}
        </Alert>
      ) : (
        <Card 
          elevation={0}
          sx={{ 
            borderRadius: 3,
            overflow: 'hidden',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          }}
        >
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ 
                  bgcolor: (theme) => alpha(theme.palette.primary.main, 0.05),
                  '& th': {
                    borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`
                  }
                }}>
                  {tableHeaders.map((header, index) => (
                    <TableCell 
                      key={`header-${index}`}
                      align={index === 0 ? 'left' : 'center'}
                      sx={{ 
                        fontWeight: 600,
                        fontSize: '0.875rem',
                        color: theme.palette.text.primary,
                        borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`
                      }}
                    >
                      {header.label}
                    </TableCell>
                  ))}
                </TableRow>
              </TableHead>
              <TableBody>
                {peers.map((peer) => renderPeerRow(peer))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      )}
    </Box>
  );
};

export default WireguardPeers;