import React, { useEffect, useState, useContext } from 'react';
import {
    Box,
    Typography,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    IconButton,
    Button,
    Stack,
    Chip,
    TablePagination,
    TextField,
    MenuItem,
    FormControlLabel,
    Switch,
    Dialog,
    DialogContent,
    DialogActions,
    Menu,
    Tooltip,
    Card,
    CardContent,
    Fade,
    Skeleton,
    alpha,
    useTheme,
    Divider,
    LinearProgress,
    CircularProgress,
} from '@mui/material';
import {
    Edit as EditIcon,
    Delete as DeleteIcon,
    Add as AddIcon,
    MoreVert as MoreVertIcon,
    CancelOutlined,
    RuleOutlined,
} from '@mui/icons-material';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { DialogContext } from '../../../../context/GlobalDialog';
import StickyBreadcrumbs from '../../../utils/StickyBreadcrumbs';
import { useTranslation } from 'react-i18next';
import {
    ThresholdRule,
    Topic,
    ThresholdRuleData,
    getThresholdRules,
    getAvailableTopics,
    createThresholdRule,
    updateThresholdRule,
    deleteThresholdRule,
    getWechatQRCode,
    createWechatBindWebSocket,
    WechatBindCallbacks
} from '../../../../services/mqttAlertService';

// 面包屑类型及数据
interface BreadcrumbItem {
    label: string;
    isCurrent: boolean;
}
const BREADCRUMB_ITEMS: BreadcrumbItem[] = [
    { label: 'MQTT', isCurrent: false },
    { label: 'Manage Alert', isCurrent: true }
];

// Main ManageAlert Component
const ManageAlert: React.FC = () => {
    const [rules, setRules] = useState<ThresholdRule[]>([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const { showSnackbar } = useSnackbar();
    const dialog = useContext(DialogContext);
    const [topics, setTopics] = useState<Topic[]>([]);
    const { t } = useTranslation();
    const theme = useTheme();
    
    // 状态管理
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [selectedRule, setSelectedRule] = useState<ThresholdRule | null>(null);

    const fetchRules = async () => {
        try {
            // 使用服务获取告警规则
            const rulesData = await getThresholdRules();
            setRules(rulesData || []);
        } catch (error: any) {
            showSnackbar(t('mqtt.threshold.manageAlert.fetchError', { error: error.response?.data?.error || t('common.unknownError') }), 'error');
        } finally {
            setLoading(false);
        }
    };

    const fetchAvailableTopics = async () => {
        try {
            // 使用服务获取可用主题
            const topicsData = await getAvailableTopics();
            setTopics(topicsData || []);
        } catch (error: any) {
            showSnackbar(t('mqtt.threshold.manageAlert.fetchTopicsError', { error: error.response?.data?.error || t('common.unknownError') }), 'error');
        }
    };

    useEffect(() => {
        fetchRules();
        fetchAvailableTopics();
    }, []);

    const handleChangePage = (_: unknown, newPage: number) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleEdit = (rule: ThresholdRule) => {
        if (!dialog) return;
        dialog.openDialog(
            <ThresholdRuleDialog
                rule={rule}
                onClose={dialog.closeDialog}
                onSuccess={fetchRules}
                topics={topics}
            />,
            t('mqtt.threshold.manageAlert.dialog.title.edit')
        );
    };

    const handleDelete = async (rule: ThresholdRule) => {
        if (!dialog) return;
        
        const confirmed = await dialog.confirm(
            t('common.deleteConfirm.title', { item: t('mqtt.threshold.title') }),
            <Box sx={{p:2}}>
                <Typography color="error" sx={{ mb: 2 }}>
                    {t('common.deleteConfirm.message', { item: t('mqtt.threshold.title') })}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                    {t('common.deleteConfirm.warning')}
                </Typography>
            </Box>,
            <DeleteIcon color="error" />
        );
        
        if (confirmed) {
            try {
                // 使用服务删除告警规则
                await deleteThresholdRule(rule.id);
                showSnackbar(t('mqtt.threshold.manageAlert.deleteSuccess'), 'success');
                fetchRules();
            } catch (error: any) {
                showSnackbar(t('mqtt.threshold.manageAlert.deleteError', { error: error.response?.data?.error || t('common.unknownError') }), 'error');
            }
        }
    };

    const handleCreate = () => {
        if (!dialog) return;
        dialog.openDialog(
            <ThresholdRuleDialog

                onClose={dialog.closeDialog}
                onSuccess={fetchRules}
                topics={topics}
            />,
            t('mqtt.threshold.manageAlert.dialog.title.create')
        );
    };

    // 菜单处理函数
    const handleMenuOpen = (event: React.MouseEvent<HTMLButtonElement>, rule: ThresholdRule) => {
        setAnchorEl(event.currentTarget);
        setSelectedRule(rule);
    };

    const handleMenuClose = () => {
        setAnchorEl(null);
        setSelectedRule(null);
    };

    const handleMenuEdit = () => {
        if (selectedRule) handleEdit(selectedRule);
        handleMenuClose();
    };

    const handleMenuDelete = async () => {
        if (selectedRule) await handleDelete(selectedRule);
        handleMenuClose();
    };

    // 渲染加载骨架屏
    const renderSkeletons = () => (
        <TableBody>
            {[...Array(5)].map((_, index) => (
                <TableRow key={index}>
                    {[...Array(7)].map((_, cellIndex) => (
                        <TableCell key={cellIndex}>
                            <Skeleton animation="wave" height={24} />
                        </TableCell>
                    ))}
                </TableRow>
            ))}
        </TableBody>
    );

    // 渲染空状态
    const renderEmptyState = () => (
        <TableRow>
            <TableCell colSpan={7} sx={{ py: 4 }}>
                <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    alignItems: 'center', 
                    justifyContent: 'center', 
                    py: 4,
                    gap: 2
                }}>
                    <RuleOutlined sx={{ fontSize: 48, color: 'text.secondary', opacity: 0.7 }} />
                    <Typography variant="h6" color="text.secondary">
                        {t('mqtt.threshold.manageAlert.noRules')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" align="center" sx={{ maxWidth: 400 }}>
                        {t('mqtt.threshold.manageAlert.emptyDescription')}
                    </Typography>
                    <Button 
                        variant="contained" 
                        startIcon={<AddIcon />} 
                        onClick={handleCreate}
                        sx={{ mt: 1 }}
                    >
                        {t('mqtt.threshold.manageAlert.createNew')}
                    </Button>
                </Box>
            </TableCell>
        </TableRow>
    );

    if (loading) {
        return (
            <Box sx={{ width: '100%' }}>
                <StickyBreadcrumbs items={BREADCRUMB_ITEMS} />
                <Card sx={{ mt: 2, p: 2 }}>
                    <LinearProgress sx={{ mb: 2 }} />
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', py: 8, gap: 1 }}>
                        <Typography variant="h6">{t('mqtt.threshold.manageAlert.loading')}</Typography>
                        <Typography variant="body2" color="text.secondary">{t('mqtt.threshold.manageAlert.pleaseWait')}</Typography>
                    </Box>
                </Card>
            </Box>
        );
    }

    return (
        <Fade in={true} timeout={800}>
            <Box sx={{ width: '100%' }}>
                <StickyBreadcrumbs items={BREADCRUMB_ITEMS} />
                
                <Card elevation={0} variant="outlined" sx={{ mb: 2, mt: 2 }}>
                    <CardContent sx={{ 
                        p: { xs: 1.5, sm: 2 }, 
                        '&:last-child': { pb: { xs: 1.5, sm: 2 } },
                        display: 'flex',
                        flexDirection: { xs: 'column', sm: 'row' },
                        justifyContent: 'space-between',
                        alignItems: { xs: 'flex-start', sm: 'center' },
                        gap: 1
                    }}>
                        <Box>
                            <Typography variant="h6" component="h2" sx={{ fontWeight: 500 }}>
                                {t('mqtt.threshold.manageAlert.title')}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                {t('mqtt.threshold.manageAlert.description')}
                            </Typography>
                        </Box>
                        
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={handleCreate}
                            size="small"
                            sx={{
                                borderRadius: 1.5,
                                textTransform: 'none',
                                boxShadow: 1,
                                '&:hover': {
                                    boxShadow: 3
                                }
                            }}
                        >
                            {t('mqtt.threshold.manageAlert.createNew')}
                        </Button>
                    </CardContent>
                </Card>
                
                <Paper 
                    elevation={1}
                    sx={{ 
                        width: '100%', 
                        overflow: 'hidden',
                        borderRadius: 1
                    }}
                >
                    <TableContainer sx={{ maxHeight: { xs: 'calc(100vh - 250px)', sm: 'calc(100vh - 200px)' } }}>
                        <Table stickyHeader size="small">
                            <TableHead>
                                <TableRow>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.manageAlert.fields.topic')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.manageAlert.fields.expression')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.manageAlert.fields.description')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', display: { xs: 'none', sm: 'table-cell' } }}>
                                        {t('mqtt.threshold.manageAlert.fields.notification')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('common.status.text')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap', display: { xs: 'none', md: 'table-cell' } }}>
                                        {t('mqtt.threshold.manageAlert.fields.createdBy')}
                                    </TableCell>
                                    <TableCell align="right" sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.manageAlert.fields.actions')}
                                    </TableCell>
                                </TableRow>
                            </TableHead>
                            
                            {loading ? renderSkeletons() : (
                                <TableBody>
                                    {rules.length === 0 ? (
                                        renderEmptyState()
                                    ) : (
                                        (rowsPerPage > 0
                                            ? rules.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                            : rules
                                        ).map((rule) => (
                                            <TableRow 
                                                key={rule.id} 
                                                hover
                                                sx={{
                                                    '&:nth-of-type(odd)': {
                                                        backgroundColor: alpha(theme.palette.primary.light, 0.02),
                                                    },
                                                    '&:hover': {
                                                        backgroundColor: alpha(theme.palette.primary.light, 0.05),
                                                    }
                                                }}
                                            >
                                                <TableCell sx={{ maxWidth: { xs: 80, sm: 150 }, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                    <Tooltip title={rule.topic} placement="top">
                                                        <Chip 
                                                            label={rule.topic}
                                                            size="small"
                                                            variant="outlined"
                                                            color="primary"
                                                            sx={{ 
                                                                maxWidth: '100%',
                                                                '.MuiChip-label': { 
                                                                    overflow: 'hidden',
                                                                    textOverflow: 'ellipsis'
                                                                } 
                                                            }}
                                                        />
                                                    </Tooltip>
                                                </TableCell>
                                                <TableCell sx={{ maxWidth: { xs: 100, sm: 200 }, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                    <Tooltip title={rule.expression} placement="top">
                                                        <Typography variant="body2" noWrap>
                                                            {rule.expression}
                                                        </Typography>
                                                    </Tooltip>
                                                </TableCell>
                                                <TableCell sx={{ maxWidth: { xs: 100, sm: 200 }, overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                                                    <Tooltip title={rule.description || t('mqtt.threshold.manageAlert.noDescription')} placement="top">
                                                        <Typography variant="body2" noWrap color={rule.description ? 'textPrimary' : 'text.secondary'} sx={{ fontStyle: rule.description ? 'normal' : 'italic' }}>
                                                            {rule.description || t('mqtt.threshold.manageAlert.noDescription')}
                                                        </Typography>
                                                    </Tooltip>
                                                </TableCell>
                                                <TableCell sx={{ display: { xs: 'none', sm: 'table-cell' } }}>
                                                    <Tooltip title={`${t(`mqtt.threshold.manageAlert.dialog.modes.${rule.mode}`)}: ${rule.contact}`} placement="top">
                                                        <Chip
                                                            label={t(`mqtt.threshold.manageAlert.dialog.modes.${rule.mode}`)}
                                                            color={rule.mode === 'email' ? 'primary' : rule.mode === 'sms' ? 'secondary' : 'success'}
                                                            size="small"
                                                            sx={{ maxWidth: '100%', overflow: 'hidden' }}
                                                        />
                                                    </Tooltip>
                                                </TableCell>
                                                <TableCell>
                                                    <Chip
                                                        label={rule.is_enabled ? t('common.status.enabled') : t('common.status.disabled')}
                                                        color={rule.is_enabled ? 'success' : 'default'}
                                                        size="small"
                                                        variant={rule.is_enabled ? 'filled' : 'outlined'}
                                                    />
                                                </TableCell>
                                                <TableCell sx={{ display: { xs: 'none', md: 'table-cell' } }}>
                                                    <Tooltip title={t('mqtt.threshold.manageAlert.createdBy', { name: rule.created_by })} placement="top">
                                                        <Typography variant="body2" noWrap>
                                                            {rule.created_by}
                                                        </Typography>
                                                    </Tooltip>
                                                </TableCell>
                                                <TableCell align="right">
                                                    <IconButton
                                                        size="small"
                                                        onClick={(e) => handleMenuOpen(e, rule)}
                                                        sx={{
                                                            color: 'text.secondary',
                                                            transition: 'all 0.2s',
                                                            '&:hover': {
                                                                color: 'primary.main',
                                                                backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                                            }
                                                        }}
                                                    >
                                                        <MoreVertIcon fontSize="small" />
                                                    </IconButton>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            )}
                        </Table>
                    </TableContainer>
                    
                    {rules.length > 0 && (
                        <Box sx={{ 
                            display: 'flex', 
                            justifyContent: 'flex-end', 
                            borderTop: 1,
                            borderColor: 'divider'
                        }}>
                            <TablePagination
                                component="div"
                                count={rules.length}
                                page={page}
                                onPageChange={handleChangePage}
                                rowsPerPage={rowsPerPage}
                                onRowsPerPageChange={handleChangeRowsPerPage}
                                rowsPerPageOptions={[5, 10, 25, 50]}
                                labelRowsPerPage={t('common.pagination.rowsPerPage')}
                                labelDisplayedRows={({ from, to, count }) => 
                                    t('common.pagination.displayedRows', { from, to, count })
                                }
                            />
                        </Box>
                    )}
                </Paper>

                {/* 操作菜单 */}
                <Menu
                    anchorEl={anchorEl}
                    open={Boolean(anchorEl)}
                    onClose={handleMenuClose}
                    transformOrigin={{ horizontal: 'right', vertical: 'top' }}
                    anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
                    PaperProps={{
                        elevation: 3,
                        sx: {
                            minWidth: 150,
                            borderRadius: 1,
                            mt: 0.5
                        }
                    }}
                >
                    <MenuItem onClick={handleMenuEdit} sx={{ py: 1 }}>
                        <EditIcon fontSize="small" sx={{ mr: 1.5 }} />
                        {t('common.edit')}
                    </MenuItem>
                    <Divider />
                    <MenuItem onClick={handleMenuDelete} sx={{ color: 'error.main', py: 1 }}>
                        <DeleteIcon fontSize="small" sx={{ mr: 1.5 }} />
                        {t('common.delete')}
                    </MenuItem>
                </Menu>
            </Box>
        </Fade>
    );
};

// ThresholdRuleDialog Component for creating/editing rules
const ThresholdRuleDialog: React.FC<{
    rule?: ThresholdRule;
    onClose: () => void;
    onSuccess: () => void;
    topics: Topic[];
}> = ({ rule, onClose, onSuccess, topics }) => {
    const [mode, setMode] = useState<'email' | 'sms' | 'wechat'>(rule?.mode || 'email');
    const [expression, setExpression] = useState<string>(rule?.expression || '');
    const [description, setDescription] = useState(rule?.description || '');
    const [contact, setContact] = useState(rule?.contact || '');
    const [isEnabled, setIsEnabled] = useState(rule?.is_enabled ?? true);
    const { showSnackbar } = useSnackbar();
    const [selectedTopic, setSelectedTopic] = useState(rule?.topic || '');
    const { t } = useTranslation();
    const theme = useTheme();
    
    const [showQRCode, setShowQRCode] = useState(false);
    const [qrCodeImage, setQRCodeImage] = useState<string>('');
    const [cleanupFn, setCleanupFn] = useState<(() => void) | null>(null);
    const [bindingStatus, setBindingStatus] = useState<'idle' | 'connecting' | 'scanning' | 'binding' | 'success' | 'error' | 'timeout'>('idle');

    // 表达式构建的状态
    const [expressionType, setExpressionType] = useState<'simple' | 'range'>('simple');
    const [simpleOperator, setSimpleOperator] = useState<string>('>');
    const [simpleValue, setSimpleValue] = useState<string>('0');
    const [rangeMin, setRangeMin] = useState<string>('0');
    const [rangeMax, setRangeMax] = useState<string>('0');
    const [rangeMinOp, setRangeMinOp] = useState<string>('<');
    const [rangeMaxOp, setRangeMaxOp] = useState<string>('<');
    const [inverseRange, setInverseRange] = useState<boolean>(false);

    useEffect(() => {
        if (rule?.expression) {
            parseExistingExpression(rule.expression);
        }
    }, [rule]);

    const parseExistingExpression = (expr: string) => {
        
        const inversePattern = /value\s*<\s*(\d+(\.\d+)?)\s*\|\|\s*value\s*>\s*(\d+(\.\d+)?)/;
        const inverseMatch = expr.match(inversePattern);
        
        if (inverseMatch) {
            setExpressionType('range');
            setInverseRange(true);
            setRangeMin(inverseMatch[1]);
            setRangeMax(inverseMatch[3]);
            return;
        }
        
        const rangePatterns = [
            { pattern: /(\d+(\.\d+)?)\s*<\s*value\s*<\s*(\d+(\.\d+)?)/, minOp: '<', maxOp: '<', reverse: false },
            { pattern: /(\d+(\.\d+)?)\s*>\s*value\s*>\s*(\d+(\.\d+)?)/, minOp: '>', maxOp: '>', reverse: true },
            { pattern: /(\d+(\.\d+)?)\s*<=\s*value\s*<=\s*(\d+(\.\d+)?)/, minOp: '<=', maxOp: '<=', reverse: false },
            { pattern: /(\d+(\.\d+)?)\s*>=\s*value\s*>=\s*(\d+(\.\d+)?)/, minOp: '>=', maxOp: '>=', reverse: true },
            { pattern: /(\d+(\.\d+)?)\s*<\s*value\s*<=\s*(\d+(\.\d+)?)/, minOp: '<', maxOp: '<=', reverse: false },
            { pattern: /(\d+(\.\d+)?)\s*<=\s*value\s*<\s*(\d+(\.\d+)?)/, minOp: '<=', maxOp: '<', reverse: false },
            { pattern: /(\d+(\.\d+)?)\s*>\s*value\s*>=\s*(\d+(\.\d+)?)/, minOp: '>', maxOp: '>=', reverse: true },
            { pattern: /(\d+(\.\d+)?)\s*>=\s*value\s*>\s*(\d+(\.\d+)?)/, minOp: '>=', maxOp: '>', reverse: true }
        ];

        for (const { pattern, minOp, maxOp, reverse } of rangePatterns) {
            const match = expr.match(pattern);
            if (match) {
                // console.log("范围表达式匹配:", match, "minOp:", minOp, "maxOp:", maxOp, "reverse:", reverse);
                setExpressionType('range');
                setInverseRange(false);
                setRangeMinOp(minOp);
                setRangeMaxOp(maxOp);
                
                if (reverse) {
                    setRangeMin(match[3]);
                    setRangeMax(match[1]);
                } else {
                    setRangeMin(match[1]);
                    setRangeMax(match[3]);
                }
                return;
            }
        }

        // 检查简单表达式
        const simplePatterns = [
            { pattern: /value\s*>\s*(\d+(\.\d+)?)/, op: '>' },
            { pattern: /value\s*>=\s*(\d+(\.\d+)?)/, op: '>=' },
            { pattern: /value\s*<\s*(\d+(\.\d+)?)/, op: '<' },
            { pattern: /value\s*<=\s*(\d+(\.\d+)?)/, op: '<=' },
            { pattern: /value\s*==\s*(\d+(\.\d+)?)/, op: '==' },
            { pattern: /value\s*!=\s*(\d+(\.\d+)?)/, op: '!=' }
        ];

        for (const { pattern, op } of simplePatterns) {
            const match = expr.match(pattern);
            if (match) {
                // console.log("简单表达式匹配:", match, "op:", op);
                setExpressionType('simple');
                setSimpleOperator(op);
                setSimpleValue(match[1]);
                return;
            }
        }

        // console.log('无法解析表达式:', expr);
    };

    // 基于选择的选项生成表达式字符串
    const generateExpression = () => {
        if (expressionType === 'simple') {
            return `value${simpleOperator}${simpleValue}`;
        } else {
            if (inverseRange) {
                // 范围外
                return `value<${rangeMin}||value>${rangeMax}`;
            } else {
                // 范围内
                return `${rangeMin}${rangeMinOp}value${rangeMaxOp}${rangeMax}`;
            }
        }
    };

    // 当组件值改变时更新表达式
    useEffect(() => {
        setExpression(generateExpression());
    }, [expressionType, simpleOperator, simpleValue, rangeMin, rangeMax, rangeMinOp, rangeMaxOp, inverseRange]);

    useEffect(() => {
        return () => {
            if (cleanupFn) {
                cleanupFn();
            }
        };
    }, [cleanupFn]);

    const handleSave = async () => {
        const ruleData: ThresholdRuleData = {
            topic_id: selectedTopic,
            expression,
            description,
            is_enabled: isEnabled,
            mode,
            contact,
        };

        try {
            if (rule) {
                // 使用服务更新告警规则
                await updateThresholdRule(rule.id, ruleData);
            } else {
                // 使用服务创建告警规则
                await createThresholdRule(ruleData);
            }
            showSnackbar(rule ? t('mqtt.threshold.manageAlert.updateSuccess') : t('mqtt.threshold.manageAlert.createSuccess'), 'success');
            onSuccess();
            onClose();
        } catch (error: any) {
            showSnackbar(t('mqtt.threshold.manageAlert.saveError', { error: error.response?.data?.error || t('common.unknownError') }), 'error');
        }
    };

    const handleWechatBind = async () => {
        try {
            // 从选定主题获取主题ID
            const topicObj = topics.find(t => t.topic === selectedTopic);
            if (!topicObj) {
                showSnackbar(t('mqtt.threshold.manageAlert.selectTopicFirst'), 'error');
                return;
            }

            setBindingStatus('connecting');
            
            // 使用服务获取二维码
            const response = await getWechatQRCode(topicObj.device_sn);
            setQRCodeImage(`data:image/png;base64,${response.qrcode}`);
            setShowQRCode(true);
            setBindingStatus('scanning');

            // 关闭任何现有的WebSocket连接
            if (cleanupFn) {
                cleanupFn();
                setCleanupFn(null);
            }

            // 定义WebSocket回调
            const callbacks: WechatBindCallbacks = {
                onConnect: () => {
                    // 连接建立，不需要特殊处理
                },
                onSuccess: (data) => {
                    setBindingStatus('success');
                    setContact(`${data.nickname}(${data.openid})`);
                    setShowQRCode(false);
                    showSnackbar(t('mqtt.threshold.manageAlert.dialog.qrCode.success'), 'success');
                },
                onTimeout: () => {
                    setBindingStatus('timeout');
                    setShowQRCode(false);
                    showSnackbar(t('mqtt.threshold.manageAlert.dialog.qrCode.timeout'), 'warning');
                },
                onError: (message) => {
                    setBindingStatus('error');
                    setShowQRCode(false);
                    showSnackbar(t('mqtt.threshold.manageAlert.dialog.qrCode.error', { error: message || t('common.unknownError') }), 'error');
                },
                onClose: () => {
                    if (bindingStatus === 'scanning' || bindingStatus === 'connecting') {
                        setBindingStatus('error');
                        setShowQRCode(false);
                        showSnackbar(t('mqtt.threshold.manageAlert.dialog.qrCode.connectionClosed'), 'warning');
                    }
                }
            };

            // 使用服务创建WebSocket连接
            const {cleanup } = createWechatBindWebSocket(topicObj.device_sn, callbacks);
            
            setCleanupFn(() => cleanup);
            
        } catch (error: any) {
            setBindingStatus('error');
            showSnackbar(t('mqtt.threshold.manageAlert.dialog.qrCode.fetchError', { error: error.response?.data?.error || t('common.unknownError') }), 'error');
        }
    };

    return (
        <Box p={{ xs: 1, sm: 2 }} sx={{ maxWidth: '100%', width: { xs: '100%', sm: 500 } }}>
            <Stack spacing={3}>
                <TextField
                    select
                    fullWidth
                    label={t('mqtt.threshold.manageAlert.dialog.fields.topic')}
                    value={selectedTopic}
                    onChange={(e) => setSelectedTopic(e.target.value)}
                    required
                    size="small"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 1 } }}
                >
                    {topics.map((topic) => (
                        <MenuItem key={topic.id} value={topic.topic}>
                            {topic.topic}
                        </MenuItem>
                    ))}
                </TextField>

                <TextField
                    select
                    fullWidth
                    label={t('mqtt.threshold.manageAlert.dialog.fields.notificationMode')}
                    value={mode}
                    onChange={(e) => setMode(e.target.value as 'email' | 'sms' | 'wechat')}
                    size="small"
                    sx={{ '& .MuiOutlinedInput-root': { borderRadius: 1 } }}
                >
                    <MenuItem value="email">{t('mqtt.threshold.manageAlert.dialog.modes.email')}</MenuItem>
                    <MenuItem value="sms">{t('mqtt.threshold.manageAlert.dialog.modes.sms')}</MenuItem>
                    <MenuItem value="wechat">{t('mqtt.threshold.manageAlert.dialog.modes.wechat')}</MenuItem>
                </TextField>

                {mode === 'email' && (
                    <TextField
                        fullWidth
                        label={t('mqtt.threshold.manageAlert.dialog.fields.email')}
                        value={contact}
                        onChange={(e) => setContact(e.target.value)}
                        type="email"
                        size="small"
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 1 } }}
                    />
                )}

                {mode === 'sms' && (
                    <TextField
                        fullWidth
                        label={t('mqtt.threshold.manageAlert.dialog.fields.sms')}
                        value={contact}
                        onChange={(e) => setContact(e.target.value)}
                        size="small"
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 1 } }}
                    />
                )}

                {mode === 'wechat' && (
                    <Box>
                        <TextField
                            fullWidth
                            label={t('mqtt.threshold.manageAlert.dialog.fields.wechat')}
                            value={contact}
                            disabled
                            size="small"
                            sx={{ 
                                '& .MuiOutlinedInput-root': { borderRadius: 1 },
                                mb: 1
                            }}
                        />
                        <Button
                            variant="contained"
                            onClick={handleWechatBind}
                            disabled={bindingStatus === 'connecting' || bindingStatus === 'scanning'}
                            size="small"
                            sx={{
                                borderRadius: 1,
                                textTransform: 'none'
                            }}
                            startIcon={bindingStatus === 'connecting' || bindingStatus === 'scanning' ? <CircularProgress size={16} color="inherit" /> : undefined}
                        >
                            {bindingStatus === 'connecting' || bindingStatus === 'scanning'
                                ? t('mqtt.threshold.manageAlert.dialog.buttons.binding')
                                : t('mqtt.threshold.manageAlert.dialog.buttons.bindWechat')
                            }
                        </Button>
                    </Box>
                )}

                {/* 表达式类型选择器 */}
                <Paper 
                    variant="outlined" 
                    sx={{ 
                        p: 2, 
                        borderRadius: 1,
                        background: alpha(theme.palette.primary.light, 0.03)
                    }}
                >
                    <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 'bold' }}>
                        {t('mqtt.threshold.manageAlert.dialog.fields.expressionType')}
                    </Typography>
                    
                    <Stack spacing={2}>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                            <Button 
                                variant={expressionType === 'simple' ? 'contained' : 'outlined'}
                                onClick={() => setExpressionType('simple')}
                                size="small"
                                sx={{ 
                                    flex: 1,
                                    borderRadius: 1,
                                    textTransform: 'none'
                                }}
                            >
                                {t('mqtt.threshold.manageAlert.dialog.expressionBuilder.simple')}
                            </Button>
                            <Button 
                                variant={expressionType === 'range' ? 'contained' : 'outlined'} 
                                onClick={() => setExpressionType('range')}
                                size="small"
                                sx={{ 
                                    flex: 1,
                                    borderRadius: 1,
                                    textTransform: 'none'
                                }}
                            >
                                {t('mqtt.threshold.manageAlert.dialog.expressionBuilder.range')}
                            </Button>
                        </Box>

                        {/* 简单表达式构建器 */}
                        {expressionType === 'simple' && (
                            <Box sx={{ 
                                display: 'flex', 
                                gap: 1, 
                                alignItems: 'center',
                                bgcolor: 'background.paper', 
                                p: 1.5,
                                borderRadius: 1,
                                border: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                            }}>
                                <Typography variant="body2" sx={{ minWidth: 50 }}>
                                    {t('mqtt.threshold.manageAlert.dialog.expressionBuilder.value')}
                                </Typography>
                                <TextField
                                    select
                                    sx={{ minWidth: 80 }}
                                    value={simpleOperator}
                                    onChange={(e) => setSimpleOperator(e.target.value)}
                                    size="small"
                                    variant="outlined"
                                >
                                    <MenuItem value=">">{'>'}</MenuItem>
                                    <MenuItem value=">=">{'≥'}</MenuItem>
                                    <MenuItem value="<">{'<'}</MenuItem>
                                    <MenuItem value="<=">{'≤'}</MenuItem>
                                    <MenuItem value="==">{'='}</MenuItem>
                                    <MenuItem value="!=">{'≠'}</MenuItem>
                                </TextField>
                                <TextField
                                    type="number"
                                    inputProps={{ step: 'any' }}
                                    value={simpleValue}
                                    onChange={(e) => setSimpleValue(e.target.value)}
                                    fullWidth
                                    size="small"
                                    variant="outlined"
                                />
                            </Box>
                        )}

                        {/* 范围表达式构建器 */}
                        {expressionType === 'range' && (
                            <Box sx={{ 
                                bgcolor: 'background.paper', 
                                p: 1.5,
                                borderRadius: 1,
                                border: `1px solid ${alpha(theme.palette.divider, 0.5)}`
                            }}>
                                <FormControlLabel
                                    control={
                                        <Switch
                                            checked={inverseRange}
                                            onChange={(e) => setInverseRange(e.target.checked)}
                                            size="small"
                                            color="primary"
                                        />
                                    }
                                    label={
                                        <Typography variant="body2">
                                            {t('mqtt.threshold.manageAlert.dialog.expressionBuilder.outsideRange')}
                                        </Typography>
                                    }
                                />
                                
                                {inverseRange ? (
                                    <Box sx={{ 
                                        display: 'flex', 
                                        justifyContent: 'center',
                                        flexDirection: { xs: 'column', sm: 'row' }, 
                                        alignItems: 'center', 
                                        mt: 1, 
                                        gap: 1
                                    }}>
                                        <Typography variant="body2">
                                            {t('mqtt.threshold.manageAlert.dialog.expressionBuilder.alertWhenLessThan')}
                                        </Typography>
                                        <TextField
                                            type="number"
                                            inputProps={{ step: 'any' }}
                                            value={rangeMin}
                                            onChange={(e) => setRangeMin(e.target.value)}
                                            sx={{ width: { xs: '100%', sm: 80 } }}
                                            size="small"
                                        />
                                        <Typography variant="body2">
                                            {t('mqtt.threshold.manageAlert.dialog.expressionBuilder.alertWhenGreaterThan')}
                                        </Typography>
                                        <TextField
                                            type="number"
                                            inputProps={{ step: 'any' }}
                                            value={rangeMax}
                                            onChange={(e) => setRangeMax(e.target.value)}
                                            sx={{ width: { xs: '100%', sm: 80 } }}
                                            size="small"
                                        />
                                    </Box>
                                ) : (
                                    <Box sx={{ 
                                        display: 'flex', 
                                        alignItems: 'center', 
                                        mt: 1, 
                                        gap: 0.5, 
                                        flexWrap: { xs: 'wrap', sm: 'nowrap' } 
                                    }}>
                                        <TextField
                                            type="number"
                                            value={rangeMin}
                                            onChange={(e) => setRangeMin(e.target.value)}
                                            sx={{ width: { xs: '30%', sm: 100 } }}
                                            size="small"
                                        />
                                        <TextField
                                            select
                                            sx={{ width: { xs: '18%', sm: 60 } }}
                                            value={rangeMinOp}
                                            onChange={(e) => setRangeMinOp(e.target.value)}
                                            size="small"
                                        >
                                            <MenuItem value="<">{'<'}</MenuItem>
                                            <MenuItem value="<=">{'≤'}</MenuItem>
                                        </TextField>
                                        <Typography variant="body2" sx={{ px: 0.5 }}>
                                            {t('mqtt.threshold.manageAlert.dialog.expressionBuilder.value')}
                                        </Typography>
                                        <TextField
                                            select
                                            sx={{ width: { xs: '18%', sm: 60 } }}
                                            value={rangeMaxOp}
                                            onChange={(e) => setRangeMaxOp(e.target.value)}
                                            size="small"
                                        >
                                            <MenuItem value="<">{'<'}</MenuItem>
                                            <MenuItem value="<=">{'≤'}</MenuItem>
                                        </TextField>
                                        <TextField
                                            type="number"
                                            value={rangeMax}
                                            onChange={(e) => setRangeMax(e.target.value)}
                                            sx={{ width: { xs: '30%', sm: 120 } }}
                                            size="small"
                                        />
                                    </Box>
                                )}
                            </Box>
                        )}
                    
                        <TextField
                            fullWidth
                            label={t('mqtt.threshold.manageAlert.dialog.fields.generatedExpression')}
                            value={expression}
                            InputProps={{ readOnly: true }}
                            helperText={t('mqtt.threshold.manageAlert.dialog.expressionBuilder.helperText')}
                            size="small"
                            sx={{ 
                                '& .MuiOutlinedInput-root': { 
                                    borderRadius: 1,
                                    fontFamily: 'monospace',
                                    fontSize: '0.9rem'
                                }
                            }}
                        />
                    </Stack>
                </Paper>
                <Tooltip 
                    arrow 
                    title={t('mqtt.threshold.manageAlert.dialog.fields.descriptionTooltip')}
                    placement="top"
                >
                    <TextField
                        fullWidth
                        label={t('mqtt.threshold.manageAlert.dialog.fields.description')}
                        value={description}
                        onChange={(e) => setDescription(e.target.value)}
                        multiline
                        rows={2}
                        size="small"
                        sx={{ '& .MuiOutlinedInput-root': { borderRadius: 1 } }}
                    />
                </Tooltip>

                <FormControlLabel
                    control={
                        <Switch
                            checked={isEnabled}
                            onChange={(e) => setIsEnabled(e.target.checked)}
                            color="success"
                        />
                    }
                    label={t('mqtt.threshold.manageAlert.dialog.fields.enableRule')}
                />

                <Stack direction="row" spacing={2} justifyContent="flex-end">
                    <Button 
                        onClick={onClose} 
                        size="small"
                        startIcon={<CancelOutlined />}
                        sx={{ borderRadius: 1 }}
                    >
                        {t('common.cancel')}
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleSave}
                        size="small"
                        sx={{ borderRadius: 1 }}
                        disabled={
                            !selectedTopic || !expression ||
                            (mode === 'email' && !contact) ||
                            (mode === 'sms' && !contact) ||
                            (mode === 'wechat' && !contact)
                        }
                    >
                        {rule
                            ? t('common.update')
                            : t('common.create')
                        }
                    </Button>
                </Stack>
            </Stack>

            <Dialog 
                open={showQRCode} 
                onClose={() => {
                    if (bindingStatus !== 'connecting' && bindingStatus !== 'scanning') {
                        setShowQRCode(false);
                        if (cleanupFn) cleanupFn();
                    }
                }}
                maxWidth="xs"
                PaperProps={{
                    sx: {
                        borderRadius: 2,
                        overflow: 'hidden'
                    }
                }}
            >
                <DialogContent sx={{ p: 3 }}>
                    <Typography variant="h6" align="center" gutterBottom sx={{ mb: 2 }}>
                        {t('mqtt.threshold.manageAlert.dialog.qrCode.title')}
                    </Typography>
                    
                    <Box sx={{ 
                        display: 'flex', 
                        justifyContent: 'center', 
                        p: 2,
                        border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                        borderRadius: 1,
                        backgroundColor: alpha(theme.palette.primary.light, 0.05)
                    }}>
                        <img src={qrCodeImage} alt="WeChat QR Code" style={{ maxWidth: '100%' }} />
                    </Box>
                    
                    <Typography variant="body1" align="center" sx={{ mt: 3, fontWeight: 500 }}>
                        {bindingStatus === 'scanning' 
                            ? t('mqtt.threshold.manageAlert.dialog.qrCode.instruction')
                            : bindingStatus === 'connecting' 
                                ? t('mqtt.threshold.manageAlert.dialog.qrCode.connecting')
                                : t('mqtt.threshold.manageAlert.dialog.qrCode.processing')
                        }
                    </Typography>
                    
                    {bindingStatus === 'scanning' && (
                        <Typography variant="caption" align="center" sx={{ mt: 1, display: 'block', color: 'text.secondary' }}>
                            {t('mqtt.threshold.manageAlert.dialog.qrCode.timeoutWarning')}
                        </Typography>
                    )}
                </DialogContent>
                <DialogActions sx={{ px: 3, pb: 2 }}>
                    <Button 
                        onClick={() => {
                            setShowQRCode(false);
                            if (cleanupFn) cleanupFn();
                            setBindingStatus('idle');
                        }}
                        disabled={bindingStatus === 'binding'}
                        sx={{ borderRadius: 1 }}
                    >
                        {t('common.cancel')}
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
};

export default ManageAlert;
