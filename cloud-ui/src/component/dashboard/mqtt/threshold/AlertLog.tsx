import Box from '@mui/material/Box';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Typography from '@mui/material/Typography';
import CircularProgress from '@mui/material/CircularProgress';
import { useEffect, useState, useContext } from 'react';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { 
    Chip, 
    Paper, 
    TablePagination, 
    Tooltip, 
    Button, 
    Skeleton,
    Card, 
    CardContent, 
    Fade,
    useTheme,
    alpha,
    TableContainer
} from '@mui/material';
import StickyBreadcrumbs from '../../../utils/StickyBreadcrumbs';
import { FileDownload, InfoOutlined } from '@mui/icons-material';
import { DialogContext } from '../../../../context/GlobalDialog';
import ExportDialogContent from '../../../utils/Timerange';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { Alarm, getAlarms, exportAlarmHistory } from '../../../../services/mqttAlertService';

// 面包屑类型及数据
interface BreadcrumbItem {
    label: string;
    isCurrent: boolean;
}

// 定义面包屑导航项
const BREADCRUMB_ITEMS: BreadcrumbItem[] = [
    { label: 'MQTT', isCurrent: false },
    { label: 'Alarm Log', isCurrent: true }
];

// 格式化日期的辅助函数
const formatDate = (dateStr: string) => {
    const date = new Date(dateStr);
    return date.toLocaleString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    });
};

export default function AlertLog() {
    const [alarms, setAlarms] = useState<Alarm[]>([]);
    const [loading, setLoading] = useState(true);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [total, setTotal] = useState(0);
    const { showSnackbar } = useSnackbar();
    const { confirm } = useContext(DialogContext)!;
    const { t } = useTranslation();
    const theme = useTheme();

    const fetchAlarms = async (page: number, rowsPerPage: number) => {
        setLoading(true);
        try {
            // 使用服务获取告警记录
            const response = await getAlarms(page + 1, rowsPerPage);
            setAlarms(response.alarms || []);
            setTotal(response.total || 0);
        } catch (error: any) {
            showSnackbar(t('mqtt.threshold.alarmLog.error.fetchFailed', { error: error.response?.data?.error || t('common.unknownError') }), "warning");
            setAlarms([]);
        } finally {
            setLoading(false);
        }
    };

    const handleChangePage = (
        _event: React.MouseEvent<HTMLButtonElement> | null,
        newPage: number,
    ) => {
        setPage(newPage);
        fetchAlarms(newPage, rowsPerPage);
    };

    const handleChangeRowsPerPage = (
        event: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    ) => {
        const newRowsPerPage = parseInt(event.target.value, 10);
        setRowsPerPage(newRowsPerPage);
        setPage(0);
        fetchAlarms(0, newRowsPerPage);
    };

    const handleExport = async (startTime: Date, endTime: Date) => {
        try {
            // 使用服务导出告警历史
            const { data, filename } = await exportAlarmHistory(startTime, endTime);

            // 为blob创建临时URL
            const url = window.URL.createObjectURL(data);
            
            // 创建临时链接元素并触发下载
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || `alarms_${startTime.toISOString()}_${endTime.toISOString()}.csv`;
            document.body.appendChild(link);
            link.click();
            
            // 清理
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            showSnackbar(t('mqtt.threshold.alarmLog.success.exportCompleted'), 'success');
        } catch (error: any) {
            console.error('Export error:', error);
            showSnackbar(error.response?.data?.error || t('mqtt.threshold.alarmLog.error.exportFailed'), 'error');
        }
    };

    const handleExportClick = async () => {
        const confirmed = await confirm(
            t('mqtt.threshold.alarmLog.exportDialog.title'),
            <ExportDialogContent />
        );

        if (confirmed) {
            const startTime = dayjs().subtract(24, 'hour').toDate();
            const endTime = dayjs().toDate();
            handleExport(startTime, endTime);
        }
    };

    useEffect(() => {
        fetchAlarms(page, rowsPerPage);
    }, []);

    // 渲染加载骨架屏
    const renderSkeletons = () => (
        <TableBody>
            {[...Array(rowsPerPage)].map((_, index) => (
                <TableRow key={index}>
                    {[...Array(6)].map((_, cellIndex) => (
                        <TableCell key={cellIndex}>
                            <Skeleton animation="wave" height={24} />
                        </TableCell>
                    ))}
                </TableRow>
            ))}
        </TableBody>
    );

    // 渲染空状态
    const renderEmptyState = () => (
        <TableRow>
            <TableCell colSpan={7} sx={{ py: 4 }}>
                <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    alignItems: 'center', 
                    justifyContent: 'center', 
                    py: 3,
                    gap: 1
                }}>
                    <InfoOutlined sx={{ fontSize: 40, color: 'text.secondary', mb: 1 }} />
                    <Typography variant="h6" color="text.secondary">
                        {t('mqtt.threshold.alarmLog.noRecords')}
                    </Typography>
                </Box>
            </TableCell>
        </TableRow>
    );

    return (
        <Fade in={true} timeout={800}>
            <Box sx={{ position: 'relative', minHeight: '200px' }}>
                <Box sx={{ mb: 2 }}><StickyBreadcrumbs items={BREADCRUMB_ITEMS}/></Box>
                
                <Card elevation={0} variant="outlined" sx={{ mb: 2 }}>
                    <CardContent sx={{ 
                        p: { xs: 1.5, sm: 2 }, 
                        '&:last-child': { pb: { xs: 1.5, sm: 2 } },
                        display: 'flex',
                        justifyContent: 'space-between',
                        alignItems: 'center',
                        flexWrap: 'wrap',
                        gap: 1
                    }}>
                        
                        <Button
                            variant="outlined"
                            startIcon={<FileDownload />}
                            onClick={handleExportClick}
                            size="small"
                            sx={{
                                borderRadius: 1.5,
                                textTransform: 'none',
                                boxShadow: 'none',
                                '&:hover': {
                                    boxShadow: 1
                                }
                            }}
                        >
                            {t('mqtt.threshold.alarmLog.exportButton')}
                        </Button>
                    </CardContent>
                </Card>

                {loading && (
                    <Box
                        sx={{
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            gap: 1,
                            zIndex: 10
                        }}
                    >
                        <CircularProgress size={40} sx={{ color: theme.palette.primary.main }} />
                        <Typography variant="body2">{t('mqtt.threshold.alarmLog.loading')}</Typography>
                    </Box>
                )}

                <Paper 
                    sx={{ 
                        width: '100%', 
                        overflow: 'hidden',
                        borderRadius: 1,
                        boxShadow: theme.shadows[1],
                        opacity: loading ? 0.6 : 1,
                        transition: 'opacity 0.3s ease'
                    }}
                >
                    <TableContainer sx={{ maxHeight: { xs: 'calc(100vh - 250px)', sm: 'calc(100vh - 200px)' } }}>
                        <Table stickyHeader aria-label="Alarms table" size="small">
                            <TableHead>
                                <TableRow>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.alarmLog.table.time')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.alarmLog.table.topic')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.alarmLog.table.value')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.alarmLog.table.description')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.alarmLog.table.alarmType')}
                                    </TableCell>
                                    <TableCell sx={{ fontWeight: 'bold', whiteSpace: 'nowrap' }}>
                                        {t('mqtt.threshold.alarmLog.table.contactInfo')}
                                    </TableCell>
                                </TableRow>
                            </TableHead>
                            
                            {loading ? (
                                renderSkeletons()
                            ) : (
                                <TableBody>
                                    {alarms.length === 0 ? (
                                        renderEmptyState()
                                    ) : (
                                        alarms.map((alarm, index) => (
                                            <TableRow 
                                                key={index}
                                                hover
                                                sx={{
                                                    '&:nth-of-type(odd)': {
                                                        backgroundColor: alpha(theme.palette.primary.light, 0.02),
                                                    },
                                                    '&:hover': {
                                                        backgroundColor: alpha(theme.palette.primary.light, 0.05),
                                                    }
                                                }}
                                            >
                                                <TableCell>
                                                    <Typography variant="caption" color="text.secondary">
                                                        {formatDate(alarm.time)}
                                                    </Typography>
                                                </TableCell>
                                                <TableCell>
                                                    <Chip 
                                                        size="small" 
                                                        label={alarm.topic} 
                                                        variant='outlined' 
                                                        color='primary'
                                                        sx={{ 
                                                            maxWidth: { xs: 100, sm: 150, md: 'unset' },
                                                            '.MuiChip-label': {
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                                whiteSpace: 'nowrap'
                                                            }
                                                        }}
                                                    />
                                                </TableCell>
                                                <TableCell>{alarm.value}</TableCell>
                                                <TableCell>
                                                    <Tooltip title={alarm.desc} arrow placement="top">
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                maxWidth: { xs: 120, sm: 200, md: 300 },
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                                whiteSpace: 'nowrap'
                                                            }}
                                                        >
                                                            {alarm.desc}
                                                        </Typography>
                                                    </Tooltip>
                                                </TableCell>
                                                <TableCell>
                                                    <Chip 
                                                        size="small" 
                                                        label={alarm.alarm_type} 
                                                        color={
                                                            alarm.alarm_type === 'email' ? 'primary' : 
                                                            alarm.alarm_type === 'sms' ? 'secondary' : 'success'
                                                        }
                                                        sx={{ height: 24 }}
                                                    />
                                                </TableCell>
                                                <TableCell sx={{ maxWidth: { xs: 120, sm: 150, md: 200 } }}>
                                                    <Tooltip title={alarm.alarm_contact} arrow placement="top">
                                                        <Typography
                                                            variant="body2"
                                                            sx={{
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis',
                                                                whiteSpace: 'nowrap'
                                                            }}
                                                        >
                                                            {alarm.alarm_contact}
                                                        </Typography>
                                                    </Tooltip>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            )}
                        </Table>
                    </TableContainer>
                    
                    <Box 
                        sx={{ 
                            display: 'flex', 
                            justifyContent: 'center', 
                            alignItems: 'center', 
                            borderTop: 1, 
                            borderColor: 'divider'
                        }}
                    >
                        <TablePagination
                            component="div"
                            count={total}
                            page={page}
                            onPageChange={handleChangePage}
                            rowsPerPage={rowsPerPage}
                            onRowsPerPageChange={handleChangeRowsPerPage}
                            rowsPerPageOptions={[10, 20, 30, 50]}
                            labelRowsPerPage={t('common.pagination.rowsPerPage')}
                            labelDisplayedRows={({ from, to ,count}) =>
                                t('common.pagination.displayedRows', {
                                    from,
                                    to,
                                    count
                                })
                            }
                        />
                    </Box>
                </Paper>
            </Box>
        </Fade>
    );
}