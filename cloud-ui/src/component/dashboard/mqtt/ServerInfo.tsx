import React, { useEffect, useState, useCallback, useRef } from 'react';
import {
    Card, CardContent, Typography, Box, Stack, Chip,
    Paper, alpha, useTheme, Skeleton, Alert, Tooltip
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
    Memory, Timer, People, Message,
    CloudDone, Speed, TrendingUp, Warning
} from '@mui/icons-material';
import EChartsBar from '../../charts/EChartsBar';
import EChartsArea from '../../charts/EChartsArea';
import { 
    ServerInfo, 
    TrafficDataPoint, 
    getMqttServerInfo, 
    formatBytes, 
    formatUptime, 
    formatNumber 
} from '../../../services/mqttService';

interface StatCardProps {
    icon: React.ReactNode;
    title: string;
    mainValue: string | number;
    mainChip?: {
        label: string;
        color?: 'success' | 'error' | 'warning' | 'info' | 'primary';
    };
    stats: Array<{
        label: string;
        value: string | number;
        tooltip?: string;
    }>;
    loading?: boolean;
}

const StatCard: React.FC<StatCardProps> = ({ icon, title, mainValue, mainChip, stats, loading = false }) => {
    const theme = useTheme();

    return (
        <Card
            variant="outlined"
            sx={{
                height: '100%',
                transition: 'all 0.3s ease',
                '&:hover': {
                    boxShadow: theme.shadows[3],
                    transform: 'translateY(-2px)'
                }
            }}
        >
            <CardContent sx={{ height: '100%', p: { xs: 2, sm: 3 } }}>
                <Stack spacing={2.5} sx={{ height: '100%' }}>
                    <Stack direction="row" spacing={1} alignItems="center">
                        <Box
                            sx={{
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 36,
                                height: 36,
                                borderRadius: '50%',
                                backgroundColor: alpha(theme.palette.primary.main, 0.1),
                            }}
                        >
                            {React.cloneElement(icon as React.ReactElement, {
                                color: "primary",
                                sx: { fontSize: { xs: '1.2rem', sm: '1.5rem' } }
                            })}
                        </Box>
                        <Typography
                            variant="subtitle2"
                            sx={{
                                fontSize: { xs: '0.875rem', sm: '1rem' },
                                fontWeight: 600
                            }}
                        >
                            {title}
                        </Typography>
                    </Stack>

                    <Stack spacing={1}>
                        <Stack
                            direction="row"
                            justifyContent="space-between"
                            alignItems="center"
                            spacing={1}
                        >
                            {loading ? (
                                <Skeleton variant="text" width="60%" height={60} />
                            ) : (
                                <Typography
                                    variant="h4"
                                    sx={{
                                        fontSize: { xs: '1.5rem', sm: '2rem' },
                                        fontWeight: 700,
                                        lineHeight: 1.2
                                    }}
                                >
                                    {mainValue}
                                </Typography>
                            )}
                            {mainChip && (
                                loading ? (
                                    <Skeleton variant="rounded" width={80} height={24} />
                                ) : (
                                    <Chip
                                        size="small"
                                        color={mainChip.color || 'success'}
                                        label={mainChip.label}
                                        sx={{
                                            height: 24,
                                            '& .MuiChip-label': {
                                                px: 1,
                                                fontSize: { xs: '0.75rem', sm: '0.875rem' }
                                            }
                                        }}
                                    />
                                )
                            )}
                        </Stack>
                    </Stack>

                    <Stack
                        spacing={1.5}
                        sx={{
                            mt: 'auto',
                            pt: 1,
                            borderTop: '1px solid',
                            borderColor: 'divider'
                        }}
                    >
                        {stats.map((stat, index) => (
                            <Stack
                                key={index}
                                direction="row"
                                justifyContent="space-between"
                                alignItems="center"
                            >
                                <Typography
                                    variant="body2"
                                    sx={{
                                        color: 'text.secondary',
                                        fontSize: { xs: '0.75rem', sm: '0.875rem' }
                                    }}
                                >
                                    {stat.label}
                                </Typography>
                                {loading ? (
                                    <Skeleton variant="text" width={60} />
                                ) : (
                                    <Tooltip title={stat.tooltip || ''} arrow placement="top">
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                color: 'text.primary',
                                                fontWeight: 500,
                                                fontSize: { xs: '0.75rem', sm: '0.875rem' }
                                            }}
                                        >
                                            {stat.value}
                                        </Typography>
                                    </Tooltip>
                                )}
                            </Stack>
                        ))}
                    </Stack>
                </Stack>
            </CardContent>
        </Card>
    );
};

const ServerOverviewCard: React.FC<{ data: ServerInfo; loading: boolean }> = ({ data, loading }) => {
    const theme = useTheme();

    return (
        <Paper
            elevation={0}
            sx={{
                mb: 3,
                mt: 2,
                p: 3,
                borderRadius: theme.shape.borderRadius * 2,
                background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.background.paper, 0.8)} 100%)`,
                border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
            }}
        >
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} alignItems={{ xs: 'flex-start', sm: 'center' }}>
                <Box
                    sx={{
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        width: 56,
                        height: 56,
                        borderRadius: '50%',
                        backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    }}
                >
                    <CloudDone color="primary" sx={{ fontSize: '2rem' }} />
                </Box>

                <Box sx={{ flex: 1 }}>
                    <Stack spacing={0.5}>
                        {loading ? (
                            <>
                                <Skeleton variant="text" width="40%" height={40} />
                                <Skeleton variant="text" width="60%" height={20} />
                            </>
                        ) : (
                            <>
                                <Typography variant="h4" sx={{ fontWeight: 700 }}>MQTT Server</Typography>
                                <Typography variant="body2" sx={{ color: 'text.secondary' }}>
                                    Running for {formatUptime(data.uptime)}
                                </Typography>
                            </>
                        )}
                    </Stack>
                </Box>

                <Stack direction="row" spacing={1} flexWrap="wrap" justifyContent="flex-end">
                    {loading ? (
                        <>
                            <Skeleton variant="rounded" width={80} height={32} />
                            <Skeleton variant="rounded" width={120} height={32} />
                        </>
                    ) : (
                        <>
                            <Chip
                                icon={<Speed fontSize="small" />}
                                size="medium"
                                color="success"
                                label={`v${data.version}`}
                                sx={{ fontWeight: 500 }}
                            />
                            <Chip
                                icon={<TrendingUp fontSize="small" />}
                                size="medium"
                                color="primary"
                                label={`Threads: ${data.threads}`}
                                sx={{ fontWeight: 500 }}
                            />
                        </>
                    )}
                </Stack>
            </Stack>

            {data.clients_connected === 0 && !loading && (
                <Alert
                    severity="info"
                    icon={<Warning />}
                    sx={{ mt: 2, borderRadius: theme.shape.borderRadius }}
                >
                    No clients are currently connected to the MQTT broker.
                </Alert>
            )}
        </Paper>
    );
};

const MqttServerDashboard: React.FC = () => {
    const [data, setData] = useState<ServerInfo | null>(null);
    const [trafficData, setTrafficData] = useState<TrafficDataPoint[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const prevBytes = useRef({ recv: 0, sent: 0 });
    const theme = useTheme();

    // 初始化流量数据
    useEffect(() => {
        const now = Date.now();
        const initialData = Array(10).fill(0).map((_, i) => ({
            time: new Date(now - (9 - i) * 3000),
            recv: 0,
            sent: 0
        }));
        setTrafficData(initialData);
    }, []);

    const fetchServerInfo = useCallback(async () => {
        try {
            setError(null);
            const response = await getMqttServerInfo();
            const currentTime = new Date();

            // 计算增量
            const deltaRecv = response.info.bytes_received - prevBytes.current.recv;
            const deltaSent = response.info.bytes_sent - prevBytes.current.sent;

            // 处理计数器重置的情况
            const adjustedDeltaRecv = deltaRecv >= 0 ? deltaRecv : response.info.bytes_received;
            const adjustedDeltaSent = deltaSent >= 0 ? deltaSent : response.info.bytes_sent;

            setTrafficData(prev => [
                ...prev.slice(-9),
                {
                    time: currentTime,
                    recv: adjustedDeltaRecv,
                    sent: adjustedDeltaSent
                }
            ]);

            // 更新前一个值
            prevBytes.current = {
                recv: response.info.bytes_received,
                sent: response.info.bytes_sent
            };
            response.info.topics = response.topics;
            setData(response.info);
            setLoading(false);
        } catch (error) {
            console.error('Error fetching MQTT server info:', error);
            setError('Failed to fetch server information. Please try again later.');
            setLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchServerInfo();
        const timerId = setInterval(fetchServerInfo, 3000);
        return () => clearInterval(timerId);
    }, [fetchServerInfo]);

    if (error) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error" sx={{ mt: 2 }}>
                    {error}
                </Alert>
            </Box>
        );
    }

    const statsCards = [
        {
            icon: <People />,
            title: "Clients",
            mainValue: data ? formatNumber(data.clients_connected) : 0,
            mainChip: {
                label: "Active",
                color: ((data?.clients_connected || 0) > 0 ? "success" : "warning") as 'success' | 'warning'
            },
            stats: [
                {
                    label: "Total Connected",
                    value: data ? formatNumber(data.clients_total) : 0,
                    tooltip: "Total number of clients that have connected"
                },
                {
                    label: "Disconnected",
                    value: data ? formatNumber(data.clients_disconnected) : 0,
                    tooltip: "Number of clients that have disconnected"
                },
                {
                    label: "Maximum",
                    value: data ? formatNumber(data.clients_maximum) : 0,
                    tooltip: "Maximum number of simultaneous client connections"
                },
            ],
        },
        {
            icon: <Message />,
            title: "Messages",
            mainValue: data ? formatNumber(data.messages_received) : 0,
            mainChip: {
                label: `Sent: ${data ? formatNumber(data.messages_sent) : 0}`,
                color: "primary" as 'primary'
            },
            stats: [
                {
                    label: "Dropped",
                    value: data ? formatNumber(data.messages_dropped) : 0,
                    tooltip: "Number of messages that were dropped"
                },
                {
                    label: "In Flight",
                    value: data ? formatNumber(data.inflight) : 0,
                    tooltip: "Number of messages currently in flight"
                },
                {
                    label: "Flight Dropped",
                    value: data ? formatNumber(data.inflight_dropped) : 0,
                    tooltip: "Number of in-flight messages that were dropped"
                },
            ],
        },
        {
            icon: <Timer />,
            title: "Subscriptions",
            mainValue: data ? formatNumber(data.subscriptions) : 0,
            mainChip: {
                label: `Topics: ${data ? formatNumber(data.topics) : 0}`,
                color: "info" as 'info'
            },
            stats: [
                {
                    label: "Retained Messages",
                    value: data ? formatNumber(data.retained) : 0,
                    tooltip: "Number of retained messages stored on the broker"
                },
                {
                    label: "Total Topics",
                    value: data ? formatNumber(data.topics) : 0,
                    tooltip: "Total number of topics on the broker"
                },
                {
                    label: "Packets Received",
                    value: data ? formatNumber(data.packets_received) : 0,
                    tooltip: "Total number of MQTT packets received"
                },
            ],
        },
        {
            icon: <Memory />,
            title: "System Resources",
            mainValue: data ? `${(data.memory_alloc / 1024 / 1024).toFixed(2)}` : "0",
            mainChip: {
                label: "MB",
                color: "warning" as 'warning'
            },
            stats: [
                {
                    label: "Memory Allocation",
                    value: data ? formatBytes(data.memory_alloc) : "0 B",
                    tooltip: "Current memory allocation for the MQTT broker"
                },
                {
                    label: "Active Threads",
                    value: data ? data.threads : 0,
                    tooltip: "Number of active threads in the MQTT broker"
                },
                {
                    label: "Uptime",
                    value: data ? formatUptime(data.uptime) : "0m",
                    tooltip: "Time since the MQTT broker was started"
                },
            ],
        },
    ];

    return (
        <Box sx={{ flexGrow: 1 }}>
            <ServerOverviewCard data={data || {} as ServerInfo} loading={loading} />

            <Grid container spacing={3} sx={{ mb: 3 }}>
                {statsCards.map((card, index) => (
                    <Grid key={index} size={{ xs: 12, sm: 6, lg: 3 }}>
                        <StatCard {...card} loading={loading} />
                    </Grid>
                ))}
            </Grid>

            <Grid container spacing={3}>
                <Grid size={{ xs: 12, lg: 6 }}>
                    {loading ? (
                        <Card variant="outlined" sx={{ height: 400, p: 2 }}>
                            <Skeleton variant="text" width="30%" height={30} />
                            <Skeleton variant="text" width="50%" height={20} sx={{ mb: 2 }} />
                            <Skeleton variant="rectangular" height={320} />
                        </Card>
                    ) : (
                        <Card
                            variant="outlined"
                            sx={{
                                height: '100%',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                    boxShadow: theme.shadows[3],
                                }
                            }}
                        >
                            <EChartsBar
                                title="Message Statistics"
                                subtitle="Total messages processed by the MQTT broker"
                                mainMetric={data ? formatNumber(data.messages_received + data.messages_sent) : 0}
                                chipLabel={data && data.messages_dropped > 0 ? `${data.messages_dropped} dropped` : "No drops"}
                                chipColor={data && data.messages_dropped > 0 ? "error" : "success"}
                                series={[
                                    {
                                        id: 'messages',
                                        label: 'Messages',
                                        data: data ? [
                                            data.messages_received,
                                            data.messages_sent,
                                            data.packets_received,
                                            data.packets_sent,
                                        ] : [0, 0, 0, 0]
                                    }
                                ]}
                                xAxis={{
                                    data: ['Received', 'Sent', 'Packets Recv', 'Packets Sent'],
                                    scaleType: 'band'
                                }}
                                height={400}
                            />
                        </Card>
                    )}
                </Grid>
                <Grid size={{ xs: 12, lg: 6 }}>
                    {loading ? (
                        <Card variant="outlined" sx={{ height: 400, p: 2 }}>
                            <Skeleton variant="text" width="30%" height={30} />
                            <Skeleton variant="text" width="50%" height={20} sx={{ mb: 2 }} />
                            <Skeleton variant="rectangular" height={320} />
                        </Card>
                    ) : (
                        <Card
                            variant="outlined"
                            sx={{
                                height: '100%',
                                transition: 'all 0.3s ease',
                                '&:hover': {
                                    boxShadow: theme.shadows[3]
                                }
                            }}
                        >
                            <EChartsArea
                                title="Network Traffic"
                                subtitle="Real-time network traffic monitoring"
                                mainMetric={formatBytes(trafficData[trafficData.length - 1]?.recv || 0)}
                                chipLabel={`↑ ${formatBytes(trafficData[trafficData.length - 1]?.sent || 0)}`}
                                chipColor="primary"
                                series={[
                                    {
                                        id: 'received',
                                        label: 'Received',
                                        data: trafficData.map(d => Number(d.recv.toFixed(2)))
                                    },
                                    {
                                        id: 'sent',
                                        label: 'Sent',
                                        data: trafficData.map(d => Number(d.sent.toFixed(2)))
                                    }
                                ]}
                                xAxis={{
                                    data: trafficData.map(d => d.time),
                                    tickInterval: (index) => index % 2 === 0
                                }}
                                height={400}
                            />
                        </Card>
                    )}
                </Grid>
            </Grid>
        </Box>
    );
};

export default MqttServerDashboard;