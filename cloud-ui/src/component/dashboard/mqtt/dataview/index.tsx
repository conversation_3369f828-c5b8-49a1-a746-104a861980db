import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import {
    Box,
    Card,
    CardContent,
    Typography,
    TablePagination,
    Stack,
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    Chip,
    TableContainer,
    Avatar,
    Paper,
    CircularProgress,
    IconButton,
    InputAdornment,
    TextField,
    Tabs,
    Tab,
    Divider,
    Alert,
    useTheme,
    Grid
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import FilterListIcon from '@mui/icons-material/FilterList';
import RefreshIcon from '@mui/icons-material/Refresh';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import TableChartIcon from '@mui/icons-material/TableChart';
import ReportGmailerrorredIcon from '@mui/icons-material/ReportGmailerrorred';
import StickyBreadcrumbs from '../../../utils/StickyBreadcrumbs';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
    TopicMessage,
    Topic,
    getDevicesWithTopics,
    createMqttWebSocket,
    formatValue,
    validateDataType
} from '../../../../services/mqttDataService';

interface BreadcrumbItem {
    label: string;
    isCurrent: boolean;
}
const BREADCRUMB_ITEMS: BreadcrumbItem[] = [
    { label: 'MQTT', isCurrent: false },
    { label: 'DataView', isCurrent: true }
];

// New dashboard card component for improved visual display
const DeviceDataCard: React.FC<{ 
    topic: Topic; 
    message: TopicMessage | undefined;
    onCardClick: () => void;
}> = React.memo(({ topic, message, onCardClick }) => {
    const theme = useTheme();
    const { t } = useTranslation();
    const dataType = validateDataType(topic.data_type || 'string');
    const displayValue = message ? formatValue(message.payload, dataType) : 'N/A';
    
    // Get topic name without device prefix for display
    const topicName = topic.topic.split('/').slice(1).join('/');

    return (
        <Card 
            variant="outlined" 
            sx={{
                height: '100%',
                cursor: 'pointer',
                transition: 'all 0.2s',
                '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: 3,
                    '& .view-details': {
                        opacity: 1
                    }
                },
                borderLeft: message?.is_alarm 
                    ? `4px solid ${theme.palette.error.main}` 
                    : message
                        ? `4px solid ${theme.palette.primary.main}`
                        : 'none'
            }}
            onClick={onCardClick}
        >
            <CardContent>
                <Stack spacing={1}>
                    <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="subtitle2" color="textSecondary" gutterBottom>
                            {topic.device_name || topic.device_sn}
                        </Typography>
                        <Chip 
                            size="small"
                            label={dataType}
                            color={dataType === 'number' ? 'primary' : dataType === 'boolean' ? 'success' : 'default'}
                            variant="outlined"
                        />
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', mb: 1 }}>
                        <Stack>
                            <Typography 
                                variant="h4" 
                                sx={{
                                    color: message?.is_alarm ? 'error.main' : 'textPrimary',
                                    fontWeight: 'bold'
                                }}
                            >
                                {displayValue}
                            </Typography>
                            <Typography color="textSecondary" variant="body2">
                                {message ? new Date(message.time).toLocaleString() : 'No data'}
                            </Typography>
                        </Stack>
                        {message?.is_alarm && (
                            <Chip icon={<ReportGmailerrorredIcon />} 
                                label={t('mqtt.dataview.alarm')} 
                                color="error" 
                                size="small" 
                            />
                        )}
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mt: 'auto' }}>
                        <Chip 
                            size="small" 
                            avatar={<Avatar>{topicName.charAt(0)}</Avatar>} 
                            label={topicName} 
                        />
                    </Box>
                </Stack>
                
                <Box 
                    className="view-details"
                    sx={{ 
                        position: 'absolute',
                        bottom: 8,
                        right: 8,
                        opacity: 0,
                        transition: 'opacity 0.2s',
                        bgcolor: 'background.paper',
                        borderRadius: 1,
                        px: 1,
                        py: 0.5,
                        fontSize: '0.75rem',
                        color: 'text.secondary',
                        boxShadow: 1
                    }}
                >
                    {t('mqtt.dataview.viewDetails')}
                </Box>
            </CardContent>
        </Card>
    );
});

// Main component
const DataVisualization: React.FC = () => {
    const [topics, setTopics] = useState<Topic[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(12);
    const [total, setTotal] = useState(0);
    const [latestMessages, setLatestMessages] = useState<{ [key: string]: TopicMessage }>({});
    const [searchQuery, setSearchQuery] = useState('');
    const [viewMode, setViewMode] = useState<'grid' | 'table'>('grid');
    const [refreshTrigger, setRefreshTrigger] = useState(0);
    const { t } = useTranslation();
    const navigate = useNavigate();
    
    // WebSocket connection
    const wsRef = useRef<WebSocket | null>(null);
    const cleanupRef = useRef<(() => void) | null>(null);
    
    // Handle new WebSocket messages
    const handleMessages = useCallback((messages: TopicMessage[]) => {
        setLatestMessages(prev => {
            const next = { ...prev };
            messages.forEach(message => {
                next[message.topic] = message;
            });
            return next;
        });
    }, []);
    
    // Establish WebSocket connection
    useEffect(() => {
        // Cleanup previous connection if exists
        if (cleanupRef.current) {
            cleanupRef.current();
            cleanupRef.current = null;
        }
        
        if (topics.length > 0) {
            // Use the service to create the WebSocket connection
            const handleWebSocketMessage = (data: any) => {
                try {
                    if (Array.isArray(data)) {
                        const messages: TopicMessage[] = data.map(d => d.latestMessage);
                        handleMessages(messages);
                    }
                } catch (error) {
                    console.error('Error processing WebSocket message:', error);
                }
            };
            
            const handleWebSocketOpen = (ws: WebSocket) => {
                wsRef.current = ws;
                if (ws.readyState === WebSocket.OPEN && topics.length > 0) {
                    ws.send(JSON.stringify({
                        type: 'subscribe',
                        topics: topics.map(topic => topic.topic),
                        mode: 'stream',
                        interval: 1000
                    }));
                }
            };
            
            const { cleanup } = createMqttWebSocket(handleWebSocketMessage, handleWebSocketOpen);
            cleanupRef.current = cleanup;
        }
        
        // Cleanup on component unmount
        return () => {
            if (cleanupRef.current) {
                cleanupRef.current();
                cleanupRef.current = null;
            }
        };
    }, [topics, handleMessages]);

    // Fetch topics from API
    const fetchTopics = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);
            
            // 使用服务获取设备和主题列表
            const response = await getDevicesWithTopics(page, pageSize);
            
            if (response && response.devices) {
                // Extract topics from devices
                const allTopics: Topic[] = [];
                response.devices.forEach(device => {
                    if (device.topics) {
                        device.topics.forEach(topic => {
                            allTopics.push({
                                ...topic,
                                device_name: device.name
                            });
                        });
                    }
                });
                
                setTopics(allTopics);
                setTotal(response.total);
                
                // Auto-subscribe to new topics via WebSocket
                if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN && allTopics.length > 0) {
                    wsRef.current.send(JSON.stringify({
                        type: 'subscribe',
                        topics: allTopics.map(topic => topic.topic),
                        mode: 'stream',
                        interval: 1000
                    }));
                }
            } else {
                setTopics([]);
                setTotal(0);
            }
        } catch (error) {
            console.error('Error fetching topics:', error);
            setError('Failed to load topics. Please try again later.');
            setTopics([]);
            setTotal(0);
        } finally {
            setLoading(false);
        }
    }, [page, pageSize]);

    // Load topics when page or page size changes or refresh is triggered
    useEffect(() => {
        fetchTopics();
    }, [fetchTopics, refreshTrigger]);

    // Handle page change
    const handlePageChange = (_event: unknown, newPage: number) => {
        setPage(newPage + 1);
    };

    // Handle page size change
    const handlePageSizeChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        setPageSize(parseInt(event.target.value, 10));
        setPage(1);
    };

    // Handle manual refresh
    const handleRefresh = () => {
        setRefreshTrigger(prev => prev + 1);
    };

    // Filter topics by search query
    const filteredTopics = useMemo(() => {
        return topics.filter(topic => {
            const searchLower = searchQuery.toLowerCase();
            return (
                topic.topic.toLowerCase().includes(searchLower) ||
                (topic.device_name || topic.device_sn).toLowerCase().includes(searchLower) ||
                (topic.data_type || '').toLowerCase().includes(searchLower)
            );
        });
    }, [topics, searchQuery]);

    // Navigate to detail page for a topic
    const handleTopicClick = useCallback((topic: Topic) => {
        navigate(`/dashboard/dataview/detail/${encodeURIComponent(topic.topic)}?type=${topic.data_type}`);
    }, [navigate]);

    // Render card grid view
    const renderGridView = useMemo(() => (
        <Grid container spacing={2}>
            {filteredTopics.map(topic => (
                <Grid item xs={12} sm={6} md={4} lg={4} xl={3} key={topic.topic}>
                    <DeviceDataCard
                        topic={topic}
                        message={latestMessages[topic.topic]}
                        onCardClick={() => handleTopicClick(topic)}
                    />
                </Grid>
            ))}
        </Grid>
    ), [filteredTopics, latestMessages, handleTopicClick]);

    // Render table view
    const renderTableView = useMemo(() => (
        <TableContainer component={Paper} variant="outlined">
            <Table size="medium">
                <TableHead>
                    <TableRow>
                        <TableCell>Topic</TableCell>
                        <TableCell>Device</TableCell>
                        <TableCell>{t('common.fields.dataType')}</TableCell>
                        <TableCell>{t('mqtt.dataview.table.latestValue')}</TableCell>
                        <TableCell>{t('common.fields.lastUpdated')}</TableCell>
                        <TableCell>{t('common.fields.createdBy')}</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody>
                    {filteredTopics.map((topic) => {
                        const message = latestMessages[topic.topic];
                        const dataType = validateDataType(topic.data_type || 'string');
                        const displayValue = message ? formatValue(message.payload, dataType) : 'N/A';
                        
                        return (
                            <TableRow 
                                key={topic.topic}
                                hover
                                onClick={() => handleTopicClick(topic)}
                                sx={{ 
                                    cursor: 'pointer',
                                    '&:hover': { bgcolor: 'action.hover' }
                                }}
                            >
                                <TableCell>
                                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                        {message?.is_alarm && (
                                            <ReportGmailerrorredIcon 
                                                sx={{ color: 'error.main', mr: 1, fontSize: 18 }} 
                                            />
                                        )}
                                        <Typography variant="body2">{topic.topic}</Typography>
                                    </Box>
                                </TableCell>
                                <TableCell>
                                    <Typography variant="body2" color="textSecondary">
                                        {topic.device_name || topic.device_sn}
                                    </Typography>
                                </TableCell>
                                <TableCell>
                                    <Chip 
                                        label={topic.data_type} 
                                        size="small"
                                        color={topic.data_type === 'number' ? 'primary' : 
                                              topic.data_type === 'boolean' ? 'success' : 'default'}
                                        variant="outlined"
                                    />
                                </TableCell>
                                <TableCell>
                                    <Typography 
                                        variant="body2" 
                                        fontWeight="medium"
                                        color={message?.is_alarm ? 'error.main' : 'textPrimary'}
                                    >
                                        {displayValue}
                                    </Typography>
                                </TableCell>
                                <TableCell>
                                    <Typography variant="body2" color="textSecondary">
                                        {message ? new Date(message.time).toLocaleString() : '-'}
                                    </Typography>
                                </TableCell>
                                <TableCell>
                                    <Typography variant="body2">{topic.created_by}</Typography>
                                </TableCell>
                            </TableRow>
                        );
                    })}
                </TableBody>
            </Table>
        </TableContainer>
    ), [filteredTopics, latestMessages, t, handleTopicClick]);

    return (
        <Box sx={{ zIndex: 100 }}>
            <StickyBreadcrumbs items={BREADCRUMB_ITEMS} />
            
            <Paper 
                variant="outlined" 
                sx={{ p: 2, mt: 2, mb: 2, borderRadius: 1 }}
            >
                <Stack spacing={2}>
                    {/* Header with search and controls */}
                    <Stack 
                        direction={{ xs: 'column', sm: 'row' }} 
                        spacing={2} 
                        justifyContent="space-between"
                        alignItems={{ xs: 'stretch', sm: 'center' }}
                    >
                        <TextField
                            placeholder={t('mqtt.dataview.searchPlaceholder')}
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                            size="small"
                            sx={{ maxWidth: { sm: 300 } }}
                            InputProps={{
                                startAdornment: (
                                    <InputAdornment position="start">
                                        <SearchIcon fontSize="small" />
                                    </InputAdornment>
                                )
                            }}
                        />
                        
                        <Stack direction="row" spacing={1} alignItems="center">
                            <Tabs 
                                value={viewMode} 
                                onChange={(_, newValue) => setViewMode(newValue)}
                                variant="scrollable"
                                scrollButtons="auto"
                            >
                                <Tab 
                                    value="grid" 
                                    label={t('mqtt.dataview.viewModes.grid')} 
                                    icon={<FilterListIcon />}
                                    iconPosition="start"
                                />
                                <Tab 
                                    value="table" 
                                    label={t('mqtt.dataview.viewModes.table')} 
                                    icon={<TableChartIcon />}
                                    iconPosition="start"
                                />
                            </Tabs>
                            
                            <IconButton onClick={handleRefresh} color="primary">
                                <RefreshIcon />
                            </IconButton>
                        </Stack>
                    </Stack>
                    
                    <Divider />
                    
                    {/* Loading state */}
                    {loading && topics.length === 0 && (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                            <CircularProgress />
                        </Box>
                    )}
                    
                    {/* Error state */}
                    {error && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {error}
                        </Alert>
                    )}
                    
                    {/* Empty state */}
                    {!loading && topics.length === 0 && !error && (
                        <Box sx={{ p: 4, textAlign: 'center' }}>
                            <InfoOutlinedIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                            <Typography variant="h6" color="textSecondary" gutterBottom>
                                {t('mqtt.dataview.noTopics')}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                                {t('mqtt.dataview.createTopicHint')}
                            </Typography>
                        </Box>
                    )}
                    
                    {/* Data display based on view mode */}
                    {!loading && topics.length > 0 && !error && (
                        <>
                            {viewMode === 'grid' ? renderGridView : renderTableView}
                        </>
                    )}
                    
                    {/* Pagination */}
                    {topics.length > 0 && (
                        <Box sx={{ display: 'flex', justifyContent: 'flex-end', pt: 2 }}>
                            <TablePagination
                                component="div"
                                count={total}
                                page={page - 1}
                                onPageChange={handlePageChange}
                                rowsPerPage={pageSize}
                                onRowsPerPageChange={handlePageSizeChange}
                                rowsPerPageOptions={[12, 24, 36]}
                                labelRowsPerPage={t('common.pagination.rowsPerPage')}
                                labelDisplayedRows={({ from, to, count }) =>
                                    t('common.pagination.displayedRows', { 
                                        from, 
                                        to, 
                                        count: typeof count === 'number' ? count : to 
                                    })
                                }
                            />
                        </Box>
                    )}
                </Stack>
            </Paper>
        </Box>
    );
};

export default DataVisualization;
