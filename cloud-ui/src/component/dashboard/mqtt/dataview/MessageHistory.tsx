import React, { useState, useEffect, useCallback, useMemo, useContext } from 'react';
import {
    Box,
    Typography,
    Table,
    TableHead,
    TableBody,
    TableRow,
    TableCell,
    TablePagination,
    Chip,
    Tooltip,
    TableContainer,
    Paper,
    CircularProgress,
    IconButton,
    Card,
    CardContent,
    Alert,
    Button,
    TextField,
    FormControl,
    FormLabel,
    RadioGroup,
    FormControlLabel,
    Radio,
    Stack,
} from '@mui/material';
import { RefreshRounded, Send as SendIcon, FileDownload } from '@mui/icons-material';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import dayjs, { Dayjs } from 'dayjs';
import { DialogContext } from '../../../../context/GlobalDialog';
import { useTranslation } from 'react-i18next';
import {
    TopicMessage,
    getTopicMessages,
    exportTopicHistory,
    publishMessage,
    formatValue,
} from '../../../../services/mqttDataService';

export interface MessageHistoryProps {
    topic: string;
    dataType: 'number' | 'boolean' | 'string';
    onClose?: () => void;
}

// PayloadCell component
const PayloadCell: React.FC<{ payload: string; dataType: 'number' | 'boolean' | 'string' }> = ({ payload, dataType }) => {
    const maxLength = 25;
    const needsTruncation = payload.length > maxLength;

    const displayText = needsTruncation ? `${formatValue(payload, dataType).slice(0, maxLength)}...` : formatValue(payload, dataType);

    return (
        <Box>
            <Tooltip
                title={needsTruncation ? formatValue(payload, dataType) : ""}
                arrow
                placement="top-start"
                slotProps={{
                    tooltip: {
                        sx: {
                            bgcolor: 'black',
                            '& .MuiTooltip-arrow': {
                                color: 'black',
                            },
                            fontSize: 'body2.fontSize',
                        }
                    }
                }}
            >
                <Typography
                    variant="body2"
                    sx={{
                        fontFamily: 'monospace',
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-all'
                    }}
                >
                    {displayText}
                </Typography>
            </Tooltip>
        </Box>
    );
};

// PublishMessage component - Memoized to prevent unnecessary re-renders
const PublishMessage = React.memo<{ topic: string; dataType: string; onPublished: () => void }>(({ topic, dataType, onPublished }) => {
    const [payload, setPayload] = useState('');
    const [numberValue, setNumberValue] = useState<number | null>(0);
    const [booleanValue, setBooleanValue] = useState<boolean>(false);
    const { showSnackbar } = useSnackbar();
    const [publishing, setPublishing] = useState(false);
    const { t } = useTranslation();

    const handlePublish = useCallback(async () => {
        let finalPayload = dataType === 'number' ? (numberValue?.toString() ?? '') :
                          dataType === 'boolean' ? booleanValue.toString() :
                          payload.trim();

        if (!finalPayload) {
            showSnackbar('Please enter a message', 'warning');
            return;
        }

        setPublishing(true);
        try {
            await publishMessage(topic, finalPayload);
            showSnackbar('Message published successfully', 'success');
            // Reset input after successful publish
            if (dataType === 'number') setNumberValue(0);
            else if (dataType === 'boolean') setBooleanValue(false);
            else setPayload('');
            // Delay refresh by 500ms
            setTimeout(onPublished, 500);
        } catch (error: any) {
            showSnackbar('Failed to publish message: ' + error.response?.data?.error, 'error');
        } finally {
            setPublishing(false);
        }
    }, [topic, dataType, numberValue, booleanValue, payload, showSnackbar, onPublished]);

    const renderInput = useMemo(() => {
        switch (dataType) {
            case 'number':
                return (
                    <TextField
                        type="number"
                        label="Number Value"
                        value={numberValue ?? ''}
                        onChange={(e) => {
                            const value = e.target.value === '' ? null : Number(e.target.value);
                            setNumberValue(value);
                        }}
                        sx={{ flex: 1, mr: 2 }}
                    />
                );
            case 'boolean':
                return (
                    <FormControl sx={{ flex: 1, mr: 2 }}>
                        <FormLabel>Boolean Value</FormLabel>
                        <RadioGroup
                            row
                            value={booleanValue.toString()}
                            onChange={(e) => setBooleanValue(e.target.value === 'true')}
                        >
                            <FormControlLabel value="true" control={<Radio />} label="True" />
                            <FormControlLabel value="false" control={<Radio />} label="False" />
                        </RadioGroup>
                    </FormControl>
                );
            default:
                return (
                    <TextField
                        multiline
                        rows={1}
                        label="Message Payload"
                        value={payload}
                        onChange={(e) => setPayload(e.target.value)}
                        sx={{ flex: 1, mr: 2 }}
                    />
                );
        }
    }, [dataType, numberValue, booleanValue, payload]);

    return (
        <Card variant="outlined" sx={{ mb: 2 }}>
            <CardContent>
                <Alert severity="info" sx={{ mb: 2 }}>
                    {t('mqtt.dataview.messageHistory.expectDataType')} <strong>{dataType}</strong>
                </Alert>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {renderInput}
                    <Button
                        variant="contained"
                        loading={publishing}
                        onClick={handlePublish}
                        disabled={publishing}
                        startIcon={<SendIcon />}
                        sx={{ minWidth: 120, height: 56 }}
                    >
                        {t('mqtt.dataview.publish.publishButton')}
                    </Button>
                </Box>
            </CardContent>
        </Card>
    );
});

export const MessageHistory: React.FC<MessageHistoryProps> = ({ topic, dataType }) => {
    const [messages, setMessages] = useState<TopicMessage[]>([]);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(10);
    const [total, setTotal] = useState(0);
    const { confirm } = useContext(DialogContext)!;
    const { showSnackbar } = useSnackbar();
    const { t } = useTranslation();

    const fetchMessages = useCallback(async () => {
        if (!topic) {
            setError('No topic specified');
            return;
        }

        setLoading(true);
        setError(null);
        try {
            const response = await getTopicMessages(topic, page + 1, rowsPerPage);
            if (response) {
                setMessages(response.data || []);
                setTotal(response.total || 0);
            } else {
                setMessages([]);
                setTotal(0);
            }
        } catch (error: any) {
            setError(error.response?.data?.error || 'Failed to fetch messages');
            setMessages([]);
            setTotal(0);
            console.error('Error fetching messages:', error);
        } finally {
            setLoading(false);
        }
    }, [topic, page, rowsPerPage]);

    useEffect(() => {
        fetchMessages();
    }, [topic, page, rowsPerPage, fetchMessages]);

    const handleChangePage = (_event: unknown, newPage: number) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleExport = async (startTime: Date, endTime: Date) => {
        try {
            const { data, filename } = await exportTopicHistory(topic, startTime, endTime);

            const url = window.URL.createObjectURL(data);
            
            const link = document.createElement('a');
            link.href = url;
            link.download = filename || `${topic}_${startTime.toISOString()}_${endTime.toISOString()}.csv`;
            document.body.appendChild(link);
            link.click();
            
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            showSnackbar('Export completed successfully', 'success');
        } catch (error: any) {
            console.error('Export error:', error);
            showSnackbar(error.response?.data?.error || 'Failed to export data', 'error');
        }
    };

    const handleExportClick = async () => {
        const ExportContent = () => {
            const [startTime, setStartTime] = React.useState<Dayjs | null>(dayjs().subtract(24, 'hour'));
            const [endTime, setEndTime] = React.useState<Dayjs | null>(dayjs());
            const [error, setError] = useState<string>('');

            if (!startTime || !endTime) {
                setError(t('mqtt.dataview.messageHistory.exportDialog.errors.selectTime'));
                return null;
            }
            if (endTime.isBefore(startTime)) {
                setError(t('mqtt.dataview.messageHistory.exportDialog.errors.invalidRange'));
                return null;
            }

            return (
                <Box sx={{ p:2 }}>
                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <Stack spacing={3} direction="row">
                            <DateTimePicker
                                label={t('mqtt.dataview.messageHistory.exportDialog.startTime')}
                                value={startTime}
                                onChange={(newValue: Dayjs | null) => {
                                    setStartTime(newValue);
                                    setError('');
                                }}
                            />
                            <DateTimePicker
                                label={t('mqtt.dataview.messageHistory.exportDialog.endTime')}
                                value={endTime}
                                onChange={(newValue: Dayjs | null) => {
                                    setEndTime(newValue);
                                    setError('');
                                }}
                            />
                        </Stack>
                    </LocalizationProvider>
                    {error && (
                        <Alert severity="error" sx={{ mt: 2 }}>
                            {error}
                        </Alert>
                    )}
                </Box>
            );
        };

        const confirmed = await confirm(
            t('mqtt.dataview.messageHistory.exportDialog.title'),
            <ExportContent />
        );

        if (confirmed) {
            const startTime = dayjs().subtract(24, 'hour').toDate();
            const endTime = dayjs().toDate();
            handleExport(startTime, endTime);
        }
    };

    if (!topic) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="warning">{t('mqtt.dataview.messageHistory.noTopic')}</Alert>
            </Box>
        );
    }

    if (loading && messages.length === 0) {
        return (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                <CircularProgress />
            </Box>
        );
    }

    if (error) {
        return (
            <Box sx={{ p: 3 }}>
                <Alert severity="error">{error}</Alert>
            </Box>
        );
    }

    if (!messages || messages.length === 0) {
        return (
            <Box>
                <PublishMessage topic={topic} dataType={dataType} onPublished={fetchMessages} />
                <Card variant="outlined">
                    <CardContent>
                        <Box sx={{ textAlign: 'center', py: 3 }}>
                            <Typography color="textSecondary">
                                {t('mqtt.dataview.messageHistory.noMessages')}
                            </Typography>
                        </Box>
                    </CardContent>
                </Card>
            </Box>
        );
    }

    return (
        <Stack spacing={2}>
            <PublishMessage topic={topic} dataType={dataType} onPublished={fetchMessages} />
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 2 }}>
                <Button
                    variant="outlined"
                    startIcon={<FileDownload />}
                    onClick={handleExportClick}
                >
                    {t('mqtt.dataview.messageHistory.exportButton')}
                </Button>
            </Box>
            <Box sx={{ overflow: 'auto', maxHeight: '80vh', display: 'flex', flexDirection: 'column' }}>
                <Box sx={{flex: 1, overflow: 'auto' }}>
                    <TableContainer component={Paper}>
                        <Table stickyHeader size="small">
                            <TableHead>
                                <TableRow>
                                    <TableCell>{t('mqtt.dataview.messageHistory.table.time')}</TableCell>
                                    <TableCell>{t('mqtt.dataview.messageHistory.table.payload')}</TableCell>
                                    <TableCell align="center">{t('mqtt.dataview.messageHistory.table.qos')}</TableCell>
                                    <TableCell>{t('mqtt.dataview.messageHistory.table.clientId')}</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {messages.map((message, index) => (
                                    <TableRow key={index} hover>
                                        <TableCell sx={{ whiteSpace: 'nowrap' }}>
                                            {new Date(message.time).toLocaleString()}
                                        </TableCell>
                                        <TableCell>
                                            <PayloadCell payload={message.payload} dataType={dataType} />
                                        </TableCell>
                                        <TableCell align="center">
                                            <Chip
                                                label={message.qos}
                                                size="small"
                                                color="primary"
                                                variant="outlined"
                                            />
                                        </TableCell>
                                        <TableCell>
                                            <Typography
                                                variant="body2"
                                                sx={{
                                                    fontFamily: 'monospace',
                                                    fontSize: '0.75rem'
                                                }}
                                            >
                                                {message.client_id}
                                            </Typography>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                </Box>
                <Box sx={{ mt:1,display:'flex',justifyContent:'space-between' }}>
                    <IconButton aria-label="Refresh" sx={{ height: 1 }} onClick={fetchMessages}>
                        <RefreshRounded />
                    </IconButton>
                    <TablePagination
                        component="div"
                        count={total}
                        page={page}
                        onPageChange={handleChangePage}
                        rowsPerPage={rowsPerPage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        rowsPerPageOptions={[5, 10, 20, 30]}
                    />
                </Box>
            </Box>
        </Stack>
    );
}; 