import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
    Box,
    Stack,
    Chip,
    Avatar,
    FormControl,
    InputLabel,
    MenuItem,
    Select,
    SelectChangeEvent,
    ToggleButton,
    ToggleButtonGroup,
    Paper,
    Typography,
    Grid2,
    Card,
    CardContent,
    IconButton,
    Tooltip,
    Skeleton,
    Divider,
    useTheme,
    alpha,
} from '@mui/material';
import ReactECharts from 'echarts-for-react';
import { useParams, useLocation } from 'react-router-dom';
import StickyBreadcrumbs from '../../../utils/StickyBreadcrumbs';
import { MessageHistory } from './MessageHistory';
import { 
    Timeline as TimelineIcon, 
    Table<PERSON>hart as TableChartIcon, 
    <PERSON><PERSON>hart as BarChartIcon, 
    <PERSON><PERSON>hart as PieChartIcon, 
    StackedLine<PERSON>hart as StackedLineChartIcon,
    ShowChart as ShowChartIcon,
    Speed as SpeedIcon,
    Refresh as RefreshIcon,
    InfoOutlined as InfoOutlinedIcon,
    FullscreenOutlined as FullscreenOutlinedIcon
} from '@mui/icons-material';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { TopicMessage, getTopicHistory } from '../../../../services/mqttDataService';

// Define supported chart types
type ChartType = 'line' | 'bar' | 'area' | 'scatter' | 'pie' | 'gauge';

// Component for chart type selection
const ChartTypeSelector: React.FC<{
    chartType: ChartType;
    onChartTypeChange: (type: ChartType) => void;
    disabled?: boolean;
}> = ({ chartType, onChartTypeChange, disabled }) => {
    const { t } = useTranslation();
    
    const chartTypes: { value: ChartType; label: string; icon: React.ReactNode }[] = [
        { value: 'line', label: t('mqtt.dataview.detail.chartType.line'), icon: <ShowChartIcon /> },
        { value: 'area', label: t('mqtt.dataview.detail.chartType.area'), icon: <StackedLineChartIcon /> },
        { value: 'bar', label: t('mqtt.dataview.detail.chartType.bar'), icon: <BarChartIcon /> },
        { value: 'scatter', label: t('mqtt.dataview.detail.chartType.scatter'), icon: <TimelineIcon /> },
        { value: 'pie', label: t('mqtt.dataview.detail.chartType.pie'), icon: <PieChartIcon /> },
        { value: 'gauge', label: t('mqtt.dataview.detail.chartType.gauge'), icon: <SpeedIcon /> },
    ];

    return (
        <Stack direction="row" spacing={0.5} sx={{ 
            flexWrap: 'wrap',
            gap: { xs: '4px', sm: '8px' }
        }}>
            {chartTypes.map((type) => (
                <Tooltip key={type.value} title={type.label}>
                    <IconButton
                        color={chartType === type.value ? 'primary' : 'default'}
                        onClick={() => onChartTypeChange(type.value)}
                        disabled={disabled}
                        size="small"
                        sx={{ 
                            border: chartType === type.value ? 1 : 0,
                            borderColor: 'primary.main',
                            bgcolor: chartType === type.value ? (theme) => alpha(theme.palette.primary.main, 0.1) : 'transparent',
                            padding: { xs: '4px', sm: '8px' }
                        }}
                    >
                        {type.icon}
                    </IconButton>
                </Tooltip>
            ))}
        </Stack>
    );
};

// Stats Card component
const StatsCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ReactNode;
}> = ({ title, value, icon }) => {
    return (
        <Card variant="outlined" sx={{ height: '100%' }}>
            <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="subtitle2" color="textSecondary" noWrap>{title}</Typography>
                    {icon}
                </Box>
                <Typography variant="h5" sx={{ fontSize: { xs: '1.2rem', sm: '1.5rem' } }}>{value}</Typography>
            </CardContent>
        </Card>
    );
};

const TopicDetail: React.FC = () => {
    const { topic } = useParams<{ topic: string }>();
    const location = useLocation();
    const searchParams = new URLSearchParams(location.search);
    const dataType = searchParams.get('type') as 'number' | 'boolean' | 'string' || 'string';
    const [messages, setMessages] = useState<TopicMessage[]>([]);
    const [timeRange, setTimeRange] = useState('1h');
    const [viewMode, setViewMode] = useState<'chart' | 'table'>('chart');
    const [chartType, setChartType] = useState<ChartType>('line');
    const [isLoading, setIsLoading] = useState(false);
    const [fullScreen, setFullScreen] = useState(false);
    const decodedTopic = topic ? decodeURIComponent(topic) : '';
    const { t } = useTranslation();
    const theme = useTheme();

    const chartData = useMemo(() => {
        if (messages.length === 0) return [];
        return messages
            .sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime())
            .map(msg => ({
                x: new Date(msg.time).getTime(),
                y: parseFloat(msg.payload) || 0,
                value: parseFloat(msg.payload) || 0,
                name: new Date(msg.time).toLocaleTimeString()
            }));
    }, [messages]);

    // Calculate stats for numerical data
    const stats = useMemo(() => {
        if (messages.length === 0 || dataType !== 'number') return null;
        
        const values = messages.map(msg => parseFloat(msg.payload) || 0);
        const sum = values.reduce((acc, val) => acc + val, 0);
        const avg = sum / values.length;
        const min = Math.min(...values);
        const max = Math.max(...values);
        const last = values[values.length - 1];
        
        return { avg, min, max, last };
    }, [messages, dataType]);

    const chartOption = useMemo(() => {
        if (messages.length === 0 || viewMode !== 'chart' || dataType !== 'number') return null;

        // Base chart configuration
        const baseOption = {
            Grid2: {
                left: '5%',
                right: '5%',
                top: '15%',
                bottom: '15%'
            },
            tooltip: {
                trigger: 'axis'
            },
            toolbox: {
                feature: {
                    restore: {},
                    saveAsImage: {
                        name: decodedTopic
                    }
                }
            },
        };

        // Series configuration based on chart type
        let options: any;
        
        switch (chartType) {
            case 'pie':
                // Group data by hour for pie chart
                const groupedData = chartData.reduce((acc: Record<string, number>, item) => {
                    const hour = new Date(item.x).getHours();
                    const key = `${hour}:00`;
                    if (!acc[key]) acc[key] = 0;
                    acc[key] += item.y;
                    return acc;
                }, {});
                
                options = {
                    tooltip: {
                        trigger: 'item',
                        formatter: '{a} <br/>{b}: {c} ({d}%)'
                    },
                    legend: {
                        type: 'scroll',
                        orient: 'vertical',
                        right: 10,
                        top: 20,
                        bottom: 20,
                    },
                    series: [{
                        name: decodedTopic,
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: true,
                        itemStyle: {
                            borderRadius: 8,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: Object.entries(groupedData).map(([name, value]) => ({ 
                            name, 
                            value 
                        }))
                    }]
                };
                break;

            case 'gauge':
                const value = messages.length > 0 
                    ? parseFloat(messages[messages.length - 1].payload) || 0 
                    : 0;
                    
                const maxValue = stats ? Math.max(100, Math.ceil(stats.max * 1.2)) : 100;
                
                options = {
                    tooltip: {
                        formatter: '{a} <br/>{b}: {c}'
                    },
                    series: [{
                        name: decodedTopic,
                        type: 'gauge',
                        progress: {
                            show: true,
                            width: 18
                        },
                        axisLine: {
                            lineStyle: {
                                width: 18,
                                color: [
                                    [0.3, '#67e0e3'],
                                    [0.7, '#37a2da'],
                                    [1, '#fd666d']
                                ]
                            }
                        },
                        axisTick: { show: false },
                        splitLine: {
                            length: 15,
                            lineStyle: {
                                width: 2,
                                color: '#999'
                            }
                        },
                        axisLabel: {
                            distance: 25,
                            color: '#999',
                            fontSize: 14
                        },
                        anchor: {
                            show: true,
                            showAbove: true,
                            size: 25,
                            itemStyle: {
                                borderWidth: 10
                            }
                        },
                        title: {
                            show: true,
                            fontSize: 14
                        },
                        detail: {
                            valueAnimation: true,
                            fontSize: 30,
                            offsetCenter: [0, '70%'],
                            formatter: '{value}',
                            color: 'inherit'
                        },
                        data: [{
                            value: value,
                            name: decodedTopic.split('/').pop() || '',
                            title: { color: '#636363' }
                        }],
                        max: maxValue
                    }]
                };
                break;

            case 'bar':
                options = {
                    ...baseOption,
                    xAxis: {
                        type: 'time',
                        axisLabel: {
                            formatter: (value: number) => new Date(value).toLocaleTimeString()
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                        {
                            start: 0,
                            end: 100
                        }
                    ],
                    series: [{
                        name: decodedTopic,
                        type: 'bar',
                        itemStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: theme.palette.primary.main
                                }, {
                                    offset: 1,
                                    color: alpha(theme.palette.primary.main, 0.6)
                                }]
                            },
                            borderRadius: [4, 4, 0, 0]
                        },
                        data: chartData.map(item => [item.x, item.y])
                    }]
                };
                break;

            case 'area':
                options = {
                    ...baseOption,
                    xAxis: {
                        type: 'time',
                        boundaryGap: false,
                        axisLabel: {
                            formatter: (value: number) => new Date(value).toLocaleTimeString()
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                        {
                            start: 0,
                            end: 100
                        }
                    ],
                    series: [{
                        name: decodedTopic,
                        type: 'line',
                        smooth: true,
                        symbol: 'none',
                        sampling: 'lttb',
                        lineStyle: {
                            width: 2,
                            color: theme.palette.primary.main
                        },
                        areaStyle: {
                            color: {
                                type: 'linear',
                                x: 0,
                                y: 0,
                                x2: 0,
                                y2: 1,
                                colorStops: [{
                                    offset: 0,
                                    color: alpha(theme.palette.primary.main, 0.5)
                                }, {
                                    offset: 1,
                                    color: alpha(theme.palette.primary.main, 0.05)
                                }]
                            }
                        },
                        data: chartData.map(item => [item.x, item.y])
                    }]
                };
                break;

            case 'scatter':
                options = {
                    ...baseOption,
                    xAxis: {
                        type: 'time',
                        axisLabel: {
                            formatter: (value: number) => new Date(value).toLocaleTimeString()
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                        {
                            start: 0,
                            end: 100
                        }
                    ],
                    series: [{
                        name: decodedTopic,
                        type: 'scatter',
                        symbolSize: 12,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.3)'
                            }
                        },
                        itemStyle: {
                            color: theme.palette.primary.main,
                            borderColor: theme.palette.background.paper,
                            borderWidth: 2
                        },
                        data: chartData.map(item => [item.x, item.y])
                    }]
                };
                break;

            case 'line':
            default:
                options = {
                    ...baseOption,
                    xAxis: {
                        type: 'time',
                        boundaryGap: false,
                        axisLabel: {
                            formatter: (value: number) => new Date(value).toLocaleTimeString()
                        }
                    },
                    yAxis: {
                        type: 'value',
                        scale: true
                    },
                    dataZoom: [
                        {
                            type: 'inside',
                            start: 0,
                            end: 100
                        },
                        {
                            start: 0,
                            end: 100
                        }
                    ],
                    series: [{
                        name: decodedTopic,
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 6,
                        sampling: 'lttb',
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowColor: 'rgba(0, 0, 0, 0.3)'
                            }
                        },
                        lineStyle: {
                            width: 3,
                            color: theme.palette.primary.main
                        },
                        data: chartData.map(item => [item.x, item.y])
                    }]
                };
                break;
        }

        return options;
    }, [messages, viewMode, dataType, decodedTopic, chartData, chartType, stats, theme]);

    const fetchHistoricalData = useCallback(async () => {
        try {
            if (viewMode !== 'chart') return;
            
            setIsLoading(true);

            // Calculate start and end times based on timeRange
            const endTime = dayjs();
            let startTime;
            switch (timeRange) {
                case '1h':
                    startTime = endTime.subtract(1, 'hour');
                    break;
                case '6h':
                    startTime = endTime.subtract(6, 'hours');
                    break;
                case '24h':
                    startTime = endTime.subtract(24, 'hours');
                    break;
                case '7d':
                    startTime = endTime.subtract(7, 'days');
                    break;
                default:
                    startTime = endTime.subtract(1, 'hour');
            }

            try {
                // 使用mqttDataService获取历史数据
                const messages = await getTopicHistory(decodedTopic, startTime.toDate(), endTime.toDate());
                
                if (messages && messages.length > 0) {
                    const sortedMessages = messages
                        .sort((a: TopicMessage, b: TopicMessage) => new Date(b.time).getTime() - new Date(a.time).getTime())
                        .filter((msg: TopicMessage, index: number, self: TopicMessage[]) =>
                            index === self.findIndex((m: TopicMessage) =>
                                new Date(m.time).getTime() === new Date(msg.time).getTime()
                            )
                        );
                    setMessages(sortedMessages);
                } else {
                    setMessages([]);
                }
            } catch (error) {
                console.error("Error getting topic history:", error);
                setMessages([]);
            }
        } catch (error) {
            console.error('Error fetching data:', error);
        } finally {
            setIsLoading(false);
        }
    }, [decodedTopic, timeRange, viewMode]);

    useEffect(() => {
        if (viewMode === 'chart') {
            fetchHistoricalData();
        }
    }, [timeRange, viewMode, fetchHistoricalData]);

    const breadcrumbItems = useMemo(() => [
        { label: 'MQTT', isCurrent: false },
        { label: 'DataView', isCurrent: false },
        { label: decodedTopic, isCurrent: true }
    ], [decodedTopic]);

    const handleRefresh = () => {
        fetchHistoricalData();
    };

    const renderChartStats = () => {
        if (!stats || dataType !== 'number') return null;
        
        return (
            <Grid2 container spacing={1} sx={{ mb: 2 }}>
                <Grid2 size={{xs:6,sm:6,md:3}}>
                    <StatsCard 
                        title={t('mqtt.dataview.detail.stats.latest')} 
                        value={stats.last.toFixed(2)} 
                        icon={<ShowChartIcon color="primary" fontSize="small" />} 
                    />
                </Grid2>
                <Grid2 size={{xs:6,sm:6,md:3}}>
                    <StatsCard 
                        title={t('mqtt.dataview.detail.stats.average')} 
                        value={stats.avg.toFixed(2)} 
                        icon={<BarChartIcon color="primary" fontSize="small" />} 
                    />
                </Grid2>
                <Grid2 size={{xs:6,sm:6,md:3}}>
                    <StatsCard 
                        title={t('mqtt.dataview.detail.stats.minimum')} 
                        value={stats.min.toFixed(2)} 
                        icon={<StackedLineChartIcon color="primary" fontSize="small" />} 
                    />
                </Grid2>
                <Grid2 size={{xs:6,sm:6,md:3}}>
                    <StatsCard 
                        title={t('mqtt.dataview.detail.stats.maximum')} 
                        value={stats.max.toFixed(2)} 
                        icon={<PieChartIcon color="primary" fontSize="small" />} 
                    />
                </Grid2>
            </Grid2>
        );
    };

    const renderDataView = useMemo(() => {
        if (viewMode === 'chart') {
            if (dataType === 'number') {
                if (isLoading) {
                    return (
                        <Box sx={{ p: 2 }}>
                            <Skeleton variant="rectangular" height={400} sx={{ mb: 2 }} />
                        </Box>
                    );
                }
                
                if (messages.length === 0) {
                    return (
                        <Box sx={{ 
                            display: 'flex', 
                            flexDirection: 'column',
                            justifyContent: 'center', 
                            alignItems: 'center', 
                            height: { xs: 250, sm: 400 },
                            p: 2,
                            color: 'text.secondary'
                        }}>
                            <InfoOutlinedIcon sx={{ fontSize: { xs: 36, sm: 48 }, mb: 2, opacity: 0.6 }} />
                            <Typography variant="h6" gutterBottom align="center">
                                {t('mqtt.dataview.detail.noData')}
                            </Typography>
                            <Typography variant="body2" align="center">
                                {t('mqtt.dataview.detail.tryDifferentTime')}
                            </Typography>
                        </Box>
                    );
                }

                return (
                    <Box sx={{ 
                        height: fullScreen ? 'calc(80vh - 240px)' : { xs: 300, sm: 400 },
                        transition: 'height 0.3s ease-in-out',
                    }}>
                        <ReactECharts
                            option={chartOption!}
                            style={{ height: '100%', width: '100%' }}
                            notMerge={true}
                            lazyUpdate={true}
                        />
                    </Box>
                );
            }
            return (
                <Box sx={{ 
                    p: { xs: 2, sm: 4 }, 
                    display: 'flex', 
                    flexDirection: 'column',
                    justifyContent: 'center', 
                    alignItems: 'center',
                    height: { xs: 200, sm: 300 },
                    color: 'text.secondary'
                }}>
                    <InfoOutlinedIcon sx={{ fontSize: { xs: 36, sm: 48 }, mb: 2, opacity: 0.6 }} />
                    <Typography variant="h6" gutterBottom align="center">
                        {t('mqtt.dataview.detail.chartsOnlyForNumeric')}
                    </Typography>
                    <Typography variant="body2" align="center">
                        {t('mqtt.dataview.detail.switchToTable')}
                    </Typography>
                </Box>
            );
        }
    
        return <MessageHistory
            topic={decodedTopic}
            dataType={dataType}
        />;
    }, [messages.length, viewMode, dataType, decodedTopic, chartOption, t, isLoading, fullScreen]);

    return (
        <Box sx={{ zIndex: 100 }}>
            <StickyBreadcrumbs items={breadcrumbItems} />
            <Stack spacing={2} sx={{ mt: 2 }}>
                <Paper elevation={0} variant="outlined" sx={{ p: { xs: 1, sm: 2 } }}>
                    <Stack spacing={2}>
                        <Box sx={{ 
                            display: 'flex', 
                            flexDirection: { xs: 'column', sm: 'row' },
                            justifyContent: 'space-between', 
                            alignItems: { xs: 'flex-start', sm: 'center' },
                            gap: 2
                        }}>
                            <Stack direction="row" spacing={1} alignItems="center" sx={{ flexWrap: 'wrap', gap: 1 }}>
                                <Chip
                                    size="small"
                                    avatar={<Avatar>{decodedTopic.split('/').pop()?.charAt(0)}</Avatar>}
                                    label={decodedTopic}
                                    sx={{ maxWidth: { xs: '100%', sm: 'auto' }, 
                                         "& .MuiChip-label": { 
                                             overflow: 'hidden',
                                             textOverflow: 'ellipsis',
                                         }
                                    }}
                                />
                                <Chip
                                    size="small"
                                    label={dataType}
                                    color={dataType === 'number' ? 'primary' : dataType === 'boolean' ? 'success' : 'default'}
                                    variant="outlined"
                                />
                            </Stack>
                            <ToggleButtonGroup
                                value={viewMode}
                                exclusive
                                onChange={(_, newMode) => newMode && setViewMode(newMode)}
                                size="small"
                                sx={{ 
                                    flexWrap: 'wrap',
                                    '.MuiToggleButton-root': {
                                        px: { xs: 1, sm: 2 }
                                    }
                                }}
                            >
                                <ToggleButton value="chart">
                                    <TimelineIcon sx={{ mr: { xs: 0.5, sm: 1 } }} />
                                    {t('mqtt.dataview.detail.viewMode.chart')}
                                </ToggleButton>
                                <ToggleButton value="table">
                                    <TableChartIcon sx={{ mr: { xs: 0.5, sm: 1 } }} />
                                    {t('mqtt.dataview.detail.viewMode.table')}
                                </ToggleButton>
                            </ToggleButtonGroup>
                        </Box>
                        
                        {viewMode === 'chart' && dataType === 'number' && (
                            <>
                                <Divider />
                                
                                <Box sx={{ 
                                    display: 'flex', 
                                    flexDirection: { xs: 'column', sm: 'row' }, 
                                    justifyContent: 'space-between', 
                                    alignItems: { xs: 'flex-start', sm: 'center' }, 
                                    gap: 2 
                                }}>
                                    <FormControl size="small" sx={{ minWidth: { xs: '100%', sm: 120 } }}>
                                        <InputLabel>{t('mqtt.dataview.detail.timeRange.label')}</InputLabel>
                                        <Select
                                            value={timeRange}
                                            label={t('mqtt.dataview.detail.timeRange.label')}
                                            onChange={(e: SelectChangeEvent) => setTimeRange(e.target.value)}
                                            fullWidth
                                        >
                                            <MenuItem value="1h">{t('mqtt.dataview.detail.timeRange.lastHour')}</MenuItem>
                                            <MenuItem value="6h">{t('mqtt.dataview.detail.timeRange.last6Hours')}</MenuItem>
                                            <MenuItem value="24h">{t('mqtt.dataview.detail.timeRange.last24Hours')}</MenuItem>
                                            <MenuItem value="7d">{t('mqtt.dataview.detail.timeRange.last7Days')}</MenuItem>
                                        </Select>
                                    </FormControl>
                                    
                                    <Stack 
                                        direction="row" 
                                        spacing={1} 
                                        alignItems="center" 
                                        sx={{ 
                                            width: { xs: '100%', sm: 'auto' },
                                            justifyContent: { xs: 'space-between', sm: 'flex-end' }
                                        }}
                                    >
                                        <ChartTypeSelector 
                                            chartType={chartType}
                                            onChartTypeChange={setChartType}
                                            disabled={isLoading}
                                        />
                                        
                                        <Box>
                                            <Tooltip title={t('mqtt.dataview.detail.refresh')}>
                                                <IconButton 
                                                    onClick={handleRefresh} 
                                                    size="small"
                                                    disabled={isLoading}
                                                >
                                                    <RefreshIcon />
                                                </IconButton>
                                            </Tooltip>
                                            
                                            <Tooltip title={fullScreen ? t('mqtt.dataview.detail.exitFullscreen') : t('mqtt.dataview.detail.fullscreen')}>
                                                <IconButton
                                                    sx={{
                                                        bgcolor: fullScreen ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
                                                    }}
                                                    onClick={() => setFullScreen(!fullScreen)}
                                                    size="small"
                                                >
                                                    <FullscreenOutlinedIcon />
                                                </IconButton>
                                            </Tooltip>
                                        </Box>
                                    </Stack>
                                </Box>
                                
                                {/* Stats Cards */}
                                {renderChartStats()}
                            </>
                        )}
                        
                        {/* Chart or Table View */}
                        {renderDataView}
                    </Stack>
                </Paper>
            </Stack>
        </Box>
    );
};

export default TopicDetail; 