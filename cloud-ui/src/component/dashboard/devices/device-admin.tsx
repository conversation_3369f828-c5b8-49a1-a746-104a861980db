import { useState, useEffect, useContext } from 'react';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  IconButton,
  Collapse,
  Chip,
  Stack,
  Alert,
  alpha,
  useTheme,
  Divider,
  Grid2,
  Card,
} from '@mui/material';
import { 
  KeyboardArrowDown as KeyboardArrowDownIcon,
  KeyboardArrowUp as KeyboardArrowUpIcon,
  Delete as DeleteIcon,
  InfoOutlined as InfoIcon,
  Add as AddIcon,
  DevicesOutlined as DevicesIcon,
} from '@mui/icons-material';
import { useSnackbar } from '../../../context/SnackbarContext';
import { DialogContext } from '../../../context/GlobalDialog';
import { useTranslation } from 'react-i18next';
import StickyBreadcrumbs from '../../utils/StickyBreadcrumbs';
import AddDeviceDialog from './components/CreateDevice';
import { 
  Device, 
  DeviceImportParams, 
  getDevicesPaginated, 
  deleteDevice, 
  importDevice,
  AddDeviceFormData 
} from '../../../services/deviceService';

function DeviceRow({ device, onDelete }: { device: Device, registered: boolean, onDelete: (deviceSN: string) => void }) {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const theme = useTheme();

  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation();
    onDelete(device.device_sn);
  };

  const isRegistered = device.registered_at !== "0001-01-01T00:00:00Z";

  return (
    <>
      <TableRow 
        sx={{ 
          '& > *': { borderBottom: 'unset' },
          backgroundColor: isRegistered ? 'inherit' : alpha(theme.palette.warning.light, 0.1),
          '&:hover': {
            backgroundColor: isRegistered ? alpha(theme.palette.primary.light, 0.05) : alpha(theme.palette.warning.light, 0.15),
          },
          cursor: 'pointer',
          transition: 'all 0.2s ease',
        }}
        onClick={() => setOpen(!open)}
      >
        <TableCell padding="checkbox">
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              setOpen(!open);
            }}
            sx={{
              transition: 'transform 0.3s ease',
              transform: open ? 'rotate(180deg)' : 'rotate(0)',
              backgroundColor: open ? alpha(theme.palette.primary.main, 0.1) : 'transparent',
              '&:hover': {
                backgroundColor: alpha(theme.palette.primary.main, 0.15),
              }
            }}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </TableCell>
        <TableCell>
          <Typography variant="body2" component="div" noWrap sx={{ maxWidth: 150, fontWeight: 500 }}>
            {device.name || t('device.unnamedDevice')}
          </Typography>
        </TableCell>
        <TableCell>
          <Typography variant="body2" component="div" noWrap sx={{ maxWidth: 150 }}>
            {device.type}
          </Typography>
        </TableCell>
        <TableCell>
          <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem', backgroundColor: alpha(theme.palette.background.default, 0.5), p: 0.5, borderRadius: 1, display: 'inline-block' }}>
            {device.device_sn}
          </Typography>
        </TableCell>
        <TableCell>
          <Typography variant="body2" sx={{ fontFamily: 'monospace', fontSize: '0.75rem', backgroundColor: alpha(theme.palette.background.default, 0.5), p: 0.5, borderRadius: 1, display: 'inline-block' }}>
            {device.imei_code}
          </Typography>
        </TableCell>
        <TableCell>
          {isRegistered ? (
            <Typography variant="body2" sx={{ fontSize: '0.75rem' }}>
              {new Date(device.registered_at).toLocaleString()}
            </Typography>
          ) : (
            <Chip 
              size="small" 
              label={t('device.fields.unregistered')} 
              color="warning" 
              variant="outlined"
              sx={{
                borderRadius: 1,
                fontWeight: 500,
                fontSize: '0.7rem',
                '&:hover': {
                  boxShadow: `0 0 8px ${alpha(theme.palette.warning.main, 0.4)}`
                },
                transition: 'box-shadow 0.3s ease'
              }}
            />
          )}
        </TableCell>
        <TableCell align="right">
          <IconButton
            size="small"
            color="error"
            onClick={handleDelete}
            sx={{
              border: `1px solid ${alpha(theme.palette.error.main, 0.2)}`,
              '&:hover': {
                backgroundColor: alpha(theme.palette.error.main, 0.1),
                transform: 'scale(1.1)',
              },
              transition: 'all 0.2s ease'
            }}
          >
            <DeleteIcon fontSize="small" />
          </IconButton>
        </TableCell>
      </TableRow>
      <TableRow>
        <TableCell style={{ paddingBottom: 0, paddingTop: 0 }} colSpan={7}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ 
              m:1,p:2,border: `2px dashed ${alpha(theme.palette.divider, 0.2)}`,
              backgroundColor: alpha(theme.palette.background.default, 0.5),
              borderBottom: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
              transition: 'all 0.3s ease'
            }}>
              <Grid2 container spacing={3}>
                <Grid2 size={12}>
                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} flexWrap="wrap">
                    <Chip 
                      icon={<DevicesIcon />}
                      label={`${t('device.fields.enterpriseId')}: ${device.enterprise_id || 'N/A'}`}
                      variant="outlined"
                      size="small"
                      sx={{ 
                        borderRadius: 1.5,
                        backgroundColor: alpha(theme.palette.background.paper, 0.8)
                      }}
                    />
                    <Chip 
                      label={`${t('device.fields.registeredBy')}: ${device.registered_by || 'N/A'}`}
                      variant="outlined"
                      size="small"
                      sx={{ 
                        borderRadius: 1.5,
                        backgroundColor: alpha(theme.palette.background.paper, 0.8)
                      }}
                    />
                    <Chip 
                      label={`${t('device.fields.longitude')}: ${device.longitude || 'N/A'}`}
                      variant="outlined"
                      size="small"
                      sx={{ 
                        borderRadius: 1.5,
                        backgroundColor: alpha(theme.palette.background.paper, 0.8)
                      }}
                    />
                    <Chip 
                      label={`${t('device.fields.latitude')}: ${device.latitude || 'N/A'}`}
                      variant="outlined"
                      size="small"
                      sx={{ 
                        borderRadius: 1.5,
                        backgroundColor: alpha(theme.palette.background.paper, 0.8)
                      }}
                    />
                  </Stack>
                </Grid2>
                <Grid2 size={{xs:12, sm:6}}>
                  <Typography variant="subtitle2" gutterBottom sx={{ 
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    mb: 1.5,
                    fontWeight: 600,
                    color: theme.palette.primary.main
                  }}>
                    {t('device.fields.operators')}
                  </Typography>
                  {device.operators && device.operators.length > 0 ? (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.8 }}>
                      {device.operators.map((operator: string, index: number) => (
                        <Chip 
                          key={index} 
                          size="small" 
                          label={operator} 
                          variant="outlined"
                          color="success"
                          sx={{
                            borderRadius: 1,
                            fontSize: '0.7rem',
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              boxShadow: `0 2px 4px ${alpha(theme.palette.success.main, 0.2)}`,
                              borderColor: theme.palette.success.main
                            }
                          }}
                        />
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontStyle: 'italic',
                      fontSize: '0.8rem'
                    }}>
                      {t('device.operators.none')}
                    </Typography>
                  )}
                </Grid2>
                <Grid2 size={{xs:12, sm:6}}>
                  <Typography variant="subtitle2" gutterBottom sx={{ 
                    display: 'flex',
                    alignItems: 'center',
                    gap: 0.5,
                    mb: 1.5,
                    fontWeight: 600,
                    color: theme.palette.primary.main
                  }}>
                    {t('device.fields.topics')}
                  </Typography>
                  {device.topics && device.topics.length > 0 ? (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.8 }}>
                      {device.topics.map((topic: string, index: number) => (
                        <Chip 
                          key={index}
                          label={topic} 
                          size="small"
                          variant="outlined"
                          color="primary"
                          sx={{
                            maxWidth: 200,
                            borderRadius: 1,
                            fontSize: '0.7rem',
                            '& .MuiChip-label': {
                              whiteSpace: 'nowrap',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis'
                            },
                            transition: 'all 0.2s ease',
                            '&:hover': {
                              boxShadow: `0 2px 4px ${alpha(theme.palette.primary.main, 0.2)}`,
                              borderColor: theme.palette.primary.main
                            }
                          }}
                        />
                      ))}
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary" sx={{ 
                      fontStyle: 'italic',
                      fontSize: '0.8rem'
                    }}>
                      {t('device.topics.none')}
                    </Typography>
                  )}
                </Grid2>
              </Grid2>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </>
  );
}

const BREADCRUMB_ITEMS = [
  { label: 'Device', isCurrent: false },
  { label: 'Device Admin', isCurrent: true }
];

export default function ManageDevice() {
  const { t } = useTranslation();
  const { showSnackbar } = useSnackbar();
  const dialogContext = useContext(DialogContext);
  const [devices, setDevices] = useState<Device[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const theme = useTheme();

  const fetchDevices = async (currentPage: number, pageSize: number) => {
    setLoading(true);
    try {
      const response = await getDevicesPaginated({
        page: currentPage + 1,
        page_size: pageSize
      });
      setDevices(response.devices || []);
      setTotal(response.total || 0);
    } catch (error: any) {
      showSnackbar(t('device.messages.fetchError', { error: error.response?.data?.error }), 'error');
      setDevices([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDevices(page, rowsPerPage);
  }, [page, rowsPerPage]);

  const handleAddDevice = async (formData: AddDeviceFormData) => {
    try {
      const response = await importDevice(formData as DeviceImportParams);
      showSnackbar(t('device.messages.addSuccess'), 'success');
      fetchDevices(page, rowsPerPage);
      return response;
    } catch (error: any) {
      const errorMsg = error.response?.data?.error || t('device.messages.addError');
      showSnackbar(errorMsg, 'error');
      throw error;
    }
  };

  const showAddDeviceDialog = () => {
    if (!dialogContext) return;

    dialogContext.openDialog(
      <AddDeviceDialog
        onAdd={handleAddDevice}
        onClose={() => dialogContext.closeDialog()}
      />,
      t('device.add.title')
    );
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleDeleteDevice = async (deviceSN: string) => {
    if (!dialogContext) return;

    dialogContext.openDialog(
      <Box sx={{ p: 2}}>
        <Alert severity="warning" sx={{ 
          mb: 3, 
          width: { sm: '90vw', md: '400px' },
          borderRadius: 2,
          boxShadow: `0 2px 8px ${alpha(theme.palette.warning.main, 0.15)}`
        }}>
          <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
            {t('common.deleteConfirm.message',{item:'device'})}
          </Typography>
          <Typography variant="caption" display="block" sx={{ 
            mt: 1,
            p: 0.8,
            pl: 1,
            pr: 1,
            borderRadius: 1,
            backgroundColor: alpha(theme.palette.warning.light, 0.2),
            display: 'inline-block'
          }}>
            {t('device.fields.sn')}: <strong>{deviceSN}</strong>
          </Typography>
        </Alert>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          {t('common.deleteConfirm.warning')}
        </Typography>
        
        <Divider sx={{ my: 2 }} />
        
        <Stack direction="row" spacing={2} justifyContent="flex-end">
          <Button 
            variant="outlined" 
            color="inherit"
            onClick={() => dialogContext.closeDialog()}
            sx={{ 
              borderRadius: 2,
              fontWeight: 500,
              '&:hover': {
                backgroundColor: alpha(theme.palette.text.primary, 0.05)
              }
            }}
          >
            {t('common.cancel')}
          </Button>
          <Button
            variant="contained"
            color="error"
            startIcon={<DeleteIcon />}
            onClick={async () => {
              try {
                await deleteDevice(deviceSN);
                showSnackbar(t('device.messages.topicDeleteSuccess'), 'success');
                fetchDevices(page, rowsPerPage);
              } catch (error: any) {
                showSnackbar(t('device.messages.topicDeleteError', { error: error.response?.data?.error }), 'error');
              } finally {
                dialogContext.closeDialog();
              }
            }}
            sx={{
              borderRadius: 2,
              fontWeight: 500,
              boxShadow: `0 4px 12px ${alpha(theme.palette.error.main, 0.3)}`,
              '&:hover': {
                boxShadow: `0 6px 16px ${alpha(theme.palette.error.main, 0.4)}`,
                transform: 'translateY(-2px)'
              },
              transition: 'all 0.2s ease'
            }}
          >
            {t('common.delete')}
          </Button>
        </Stack>
      </Box>,
      t('common.delete'),<DeleteIcon color="error" />
    );
  };

  // Group devices by registration status
  const groupedDevices = devices.reduce((acc: { registered: Device[], unregistered: Device[] }, device) => {
    if (device.registered_at !== "0001-01-01T00:00:00Z") {
      acc.registered.push(device);
    } else {
      acc.unregistered.push(device);
    }
    return acc;
  }, { registered: [], unregistered: [] });

  return (
    <Box>
      <Box sx={{ mb: 2 }}>
        <StickyBreadcrumbs items={BREADCRUMB_ITEMS} />
      </Box>
      
      {loading ? (
        <Card 
          variant="outlined" 
          sx={{ 
            display: 'flex', 
            justifyContent: 'center',
            alignItems: 'center',
            p: 5,
            backgroundColor: alpha(theme.palette.background.paper, 0.6),
            borderRadius: 3,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          }}
        >
          <CircularProgress 
            size={50} 
            thickness={4} 
            sx={{ 
              mr: 2,
              color: theme.palette.primary.main,
              '& .MuiCircularProgress-circle': {
                strokeLinecap: 'round',
              }
            }} 
          />
          <Typography 
            variant="h6" 
            color="text.secondary"
            sx={{ 
              fontWeight: 500,
              opacity: 0.8
            }}
          >
            {t('common.loading')}
          </Typography>
        </Card>
      ) : devices.length === 0 ? (
        <Card 
          variant="outlined"
          sx={{ 
            p: 5, 
            textAlign: 'center', 
            backgroundColor: alpha(theme.palette.background.paper, 0.6),
            borderRadius: 3,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
          }}
        >
          <Box 
            sx={{
              width: 80,
              height: 80,
              borderRadius: '50%',
              backgroundColor: alpha(theme.palette.info.light, 0.15),
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 24px',
            }}
          >
            <InfoIcon color="info" sx={{ fontSize: 48, opacity: 0.8 }} />
          </Box>
          <Typography color="text.secondary" variant="h6" gutterBottom sx={{ fontWeight: 500 }}>
            {t('device.noDevices')}
          </Typography>
          <Typography color="text.secondary" variant="body2" sx={{ mb: 3, maxWidth: 500, mx: 'auto' }}>
            {t('device.admin.emptyState')}
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={showAddDeviceDialog}
            sx={{
              fontWeight: 600,
              boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
              background: `linear-gradient(145deg, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.dark, 0.9)})`,
              borderRadius: 2,
              px: 3,
              py: 1,
              '&:hover': { 
                boxShadow: '0 8px 16px rgba(0, 0, 0, 0.15)',
                transform: 'translateY(-2px)'
              },
              transition: 'all 0.3s ease',
            }}
          >
            {t('device.add.title')}
          </Button>
        </Card>
      ) : (
        <Card 
          variant="outlined"
          sx={{ 
            width: '100%', 
            overflow: 'hidden',
            borderRadius: 3,
            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.05)}`,
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
          }}
        >
          <TableContainer>
            <Table aria-label="device table" size="small">
              <TableHead>
                <TableRow sx={{ 
                  backgroundColor: alpha(theme.palette.primary.main, 0.05),
                  '& th': {
                    borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`
                  }
                }}>
                  <TableCell padding="checkbox" />
                  <TableCell sx={{ 
                    fontWeight: 'bold',
                    color: theme.palette.primary.main
                  }}>{t('device.fields.name')}</TableCell>
                  <TableCell sx={{ 
                    fontWeight: 'bold',
                    color: theme.palette.primary.main
                  }}>{t('device.fields.type')}</TableCell>
                  <TableCell sx={{ 
                    fontWeight: 'bold',
                    color: theme.palette.primary.main
                  }}>{t('device.fields.sn')}</TableCell>
                  <TableCell sx={{ 
                    fontWeight: 'bold',
                    color: theme.palette.primary.main
                  }}>{t('device.fields.imei')}</TableCell>
                  <TableCell sx={{ 
                    fontWeight: 'bold',
                    color: theme.palette.primary.main
                  }}>{t('device.fields.registeredAt')}</TableCell>
                  <TableCell align="right" sx={{ 
                    fontWeight: 'bold',
                    color: theme.palette.primary.main
                  }}>{t('common.edit')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {/* Registered Devices */}
                {groupedDevices.registered.map((device) => (
                  <DeviceRow 
                    key={device.id} 
                    device={device} 
                    registered={true}
                    onDelete={handleDeleteDevice}
                  />
                ))}
                {/* Unregistered Devices */}
                {groupedDevices.unregistered.map((device) => (
                  <DeviceRow 
                    key={device.id} 
                    device={device} 
                    registered={false}
                    onDelete={handleDeleteDevice}
                  />
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          <Divider />
          <Box sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between',
            px: 2,
            py: 1,
            backgroundColor: alpha(theme.palette.background.default, 0.4)
          }}>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={showAddDeviceDialog}
              disabled={loading}
              size="small"
              sx={{ 
                borderRadius: 2,
                fontWeight: 600,
                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                background: `linear-gradient(145deg, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.dark, 0.9)})`,
                px: 2,
                '&:hover': {
                  boxShadow: '0 6px 12px rgba(0, 0, 0, 0.15)',
                  transform: 'translateY(-2px)'
                },
                transition: 'all 0.2s ease',
              }}
            >
              {t('device.add.title')}
            </Button>
            <TablePagination
              rowsPerPageOptions={[10, 25, 50]}
              component="div"
              count={total}
              rowsPerPage={rowsPerPage}
              page={page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              sx={{
                '.MuiTablePagination-select': {
                  borderRadius: 1,
                  mr: 1
                },
                '.MuiTablePagination-selectIcon': {
                  color: theme.palette.primary.main
                },
                '.MuiTablePagination-displayedRows': {
                  fontWeight: 500,
                  color: theme.palette.text.secondary
                }
              }}
            />
          </Box>
        </Card>
      )}
    </Box>
  );
}
