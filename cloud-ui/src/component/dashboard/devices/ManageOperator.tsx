import React, { useState, useEffect, useContext, useMemo, memo } from 'react';
import {
  Box,
  Button,
  TextField,
  Paper,
  IconButton,
  Typography,
  CircularProgress,
  Alert,
  Stack,
  Divider,
  Tooltip,
  alpha,
  useTheme,
  Card,
  Avatar,
  CardContent,
  Grid2,
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Add as AddIcon,
  Person as PersonIcon,
  ManageAccounts as ManageAccountsIcon,
  Email as EmailIcon,
  Badge as BadgeIcon,
} from '@mui/icons-material';
import { useSnackbar } from '../../../context/SnackbarContext';
import { DialogContext } from '../../../context/GlobalDialog';
import { useTranslation } from 'react-i18next';
import StickyBreadcrumbs from '../../utils/StickyBreadcrumbs';
import { 
  Operator, 
  CreateOperatorParams, 
  getOperators, 
  createOperator, 
  deleteOperator 
} from '../../../services/operatorService';
import { getUserInfoFromStorage } from '../../../context/UserContext';

interface CreateOperatorData {
  name: string;
  username: string;
  password: string;
  email: string;
}

// Update the validation constants to match backend rules
const USERNAME_MAX_LENGTH = 12;
const USERNAME_PATTERN = /^[a-zA-Z][a-zA-Z0-9]*$/;
const PASSWORD_MIN_LENGTH = 8;
const PASSWORD_MAX_LENGTH = 20;
const PASSWORD_SPECIAL_CHARS = '@$!%*?&';

// Update validation functions
const isValidUsername = (username: string): boolean => {
    if (username.length > USERNAME_MAX_LENGTH) {
        return false;
    }
    return USERNAME_PATTERN.test(username);
};

const isValidPassword = (password: string): boolean => {
    if (password.length < PASSWORD_MIN_LENGTH || password.length > PASSWORD_MAX_LENGTH) {
        return false;
    }

    const patterns = [
        /[a-zA-Z]/,  // letters
        /[0-9]/,     // numbers
        new RegExp(`[${PASSWORD_SPECIAL_CHARS}]`)  // special characters
    ];

    let count = 0;
    for (const pattern of patterns) {
        if (pattern.test(password)) {
            count++;
        }
    }
    return count >= 2;
};

// Add breadcrumb items definition before the ManageOperator component
const BREADCRUMB_ITEMS = [
  { label: 'Device', isCurrent: false },
  { label: 'Operator Management', isCurrent: true }
];

// 添加邮箱验证
const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
};

// 更新 ValidationErrors 接口
interface ValidationErrors {
    username?: string;
    password?: string;
    email?: string;
    name?: string;
}

// Create a memoized OperatorCard component for better performance
const OperatorCard = memo(({ operator, handleDeleteOperator, t, theme }: {
    operator: Operator;
    handleDeleteOperator: (id: string, name: string, username: string) => void;
    t: any;
    theme: any;
}) => {
    return (
        <Card
            variant="outlined"
            sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                transition: theme.transitions.create(['box-shadow', 'transform', 'background-color'], {
                    duration: theme.transitions.duration.standard,
                }),
                '&:hover': {
                    boxShadow: 6,
                    transform: 'translateY(-4px)',
                    backgroundColor: alpha(theme.palette.primary.light, 0.03),
                },
                position: 'relative',
                overflow: 'visible',
                borderRadius: 2,
            }}
        >
            <Box sx={{ 
                p: 2, 
                display: 'flex', 
                flexDirection: 'row',
                alignItems: 'center', 
                backgroundColor: alpha(theme.palette.primary.light, 0.05),
                position: 'relative',
                borderTopLeftRadius: 'inherit',
                borderTopRightRadius: 'inherit',
            }}>
                <Avatar
                    sx={{
                        width: 32,
                        height: 32,
                        bgcolor: alpha(theme.palette.primary.main, 0.85),
                        boxShadow: '0 4px 12px rgba(0,0,0,0.12)',
                        mr: 2,
                        transition: theme.transitions.create(['transform', 'box-shadow'], {
                            duration: theme.transitions.duration.shorter,
                        }),
                        '&:hover': {
                            transform: 'scale(1.05)',
                            boxShadow: '0 6px 14px rgba(0,0,0,0.14)',
                        }
                    }}
                >
                    <PersonIcon />
                </Avatar>
                <Typography 
                    variant="subtitle1" 
                    sx={{ 
                        fontWeight: 600,
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        maxWidth: '100%'
                    }}
                >
                    {operator.name}
                </Typography>
                <Tooltip title={t('common.delete')} placement="top">
                    <IconButton
                        color="error"
                        size="small"
                        onClick={() => handleDeleteOperator(
                            operator.id,
                            operator.name,
                            operator.username
                        )}
                        sx={{ 
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            backgroundColor: alpha(theme.palette.error.light, 0.1),
                            '&:hover': {
                                backgroundColor: alpha(theme.palette.error.light, 0.2),
                                transform: 'scale(1.1)',
                            },
                            transition: theme.transitions.create(['transform', 'background-color'], {
                                duration: theme.transitions.duration.shorter,
                            }),
                        }}
                    >
                        <DeleteIcon fontSize="small" />
                    </IconButton>
                </Tooltip>
            </Box>
            <CardContent sx={{ 
                flexGrow: 1, 
                py: 2, 
                px: 2,
                '&:last-child': { pb: 2 },
                display: 'flex',
                flexDirection: 'column',
                gap: 1.5,
            }}>
                <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'flex-start',
                    overflow: 'hidden'
                }}>
                    <EmailIcon 
                        fontSize="small" 
                        sx={{ 
                            color: theme.palette.text.secondary,
                            mr: 1.5,
                            opacity: 0.7,
                            flexShrink: 0,
                            mt: 0.5
                        }} 
                    />
                    <Box sx={{ overflow: 'hidden' }}>
                        <Typography 
                            variant="caption" 
                            color="text.secondary" 
                            display="block"
                            sx={{ fontWeight: 500 }}
                        >
                            {t('common.fields.email')}
                        </Typography>
                        <Tooltip title={operator.email} placement="top">
                            <Typography 
                                variant="body2" 
                                sx={{ 
                                    wordBreak: 'break-all',
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis'
                                }}
                            >
                                {operator.email}
                            </Typography>
                        </Tooltip>
                    </Box>
                </Box>
                
                <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'flex-start',
                    overflow: 'hidden',
                    mt: 0.5
                }}>
                    <BadgeIcon 
                        fontSize="small" 
                        sx={{ 
                            color: theme.palette.text.secondary,
                            mr: 1.5,
                            opacity: 0.7,
                            flexShrink: 0,
                            mt: 0.5
                        }} 
                    />
                    <Box sx={{ overflow: 'hidden' }}>
                        <Typography 
                            variant="caption" 
                            color="text.secondary" 
                            display="block"
                            sx={{ fontWeight: 500 }}
                        >
                            {t('common.fields.username')}
                        </Typography>
                        <Typography variant="body2">
                            {operator.username}
                        </Typography>
                    </Box>
                </Box>
            </CardContent>
        </Card>
    );
});

const ManageOperator: React.FC = () => {
    const { showSnackbar } = useSnackbar();
    const [operators, setOperators] = useState<Operator[]>([]);
    const [loading, setLoading] = useState(true);
    const [enterpriseID] = useState(getUserInfoFromStorage().enterprise_id || '');
    const [newOperator, setNewOperator] = useState<CreateOperatorData>({
        name: '',
        username: '',
        password: '',
        email: '',
    });
    const dialog = useContext(DialogContext);
    const { t } = useTranslation();
    const theme = useTheme();

    const fetchOperators = async () => {
        setLoading(true);
        try {
            const response = await getOperators();
            setOperators(response.operators || []);
        } catch (error) {
            setOperators([]);
            showSnackbar(t('operator.messages.fetchError'), 'error');
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchOperators();
    }, []);


    const handleDeleteOperator = async (id: string, name: string, username: string) => {
        if (!dialog) return;
        
        dialog.openDialog(
            <Box sx={{ p: 3, maxWidth: { xs: '90vw', sm: '400px' } }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <DeleteIcon color="error" />
                    {t('operator.deleteOperator')}
                </Typography>
                
                <Alert severity="warning" sx={{ mb: 3 }}>
                    <Typography variant="body2" sx={{ fontWeight: 'medium' }}>
                        {t('operator.deleteConfirm', { name, username })}
                    </Typography>
                </Alert>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {t('common.deleteConfirm.warning')}
                </Typography>
                
                <Divider sx={{ my: 2 }} />
                
                <Stack direction="row" spacing={2} justifyContent="flex-end">
                    <Button 
                        variant="outlined" 
                        color="inherit"
                        onClick={() => dialog.closeDialog()}
                    >
                        {t('common.cancel')}
                    </Button>
                    <Button
                        variant="contained"
                        color="error"
                        startIcon={<DeleteIcon />}
                        onClick={async () => {
                            try {
                                await deleteOperator(id, enterpriseID);
                                showSnackbar(t('common.messages.deleteSuccess', { item: name }), 'success');
                                fetchOperators();
                            } catch (error: any) {
                                showSnackbar(error.response?.data?.error || t('common.messages.deleteError', { item: name }), 'error');
                            } finally {
                                dialog.closeDialog();
                            }
                        }}
                    >
                        {t('common.delete')}
                    </Button>
                </Stack>
            </Box>,
            t('operator.deleteOperator')
        );
    };

    const openCreateOperatorDialog = () => {
        if (!dialog) return;
        
        const CreateOperatorForm = () => {
            // 在组件内部使用状态，这样表单可以正常工作
            const [formData, setFormData] = useState({...newOperator});
            const [formErrors, setFormErrors] = useState<ValidationErrors>({});
            const [isSubmitting, setIsSubmitting] = useState(false);
            
            const handleFormSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
                event.preventDefault();
                
                const errors: ValidationErrors = {};
                
                if (!formData.name.trim()) {
                    errors.name = t('operator.validation.nameRequired');
                }

                if (!formData.email) {
                    errors.email = t('operator.validation.emailRequired');
                } else if (!isValidEmail(formData.email)) {
                    errors.email = t('operator.validation.emailInvalid');
                }

                if (!formData.username) {
                    errors.username = t('operator.validation.usernameRequired');
                } else if (!isValidUsername(formData.username)) {
                    errors.username = t('operator.validation.usernameInvalid', { 
                        max: USERNAME_MAX_LENGTH 
                    });
                }

                if (!formData.password) {
                    errors.password = t('operator.validation.passwordRequired');
                } else if (!isValidPassword(formData.password)) {
                    errors.password = t('operator.validation.passwordInvalid', {
                        min: PASSWORD_MIN_LENGTH,
                        max: PASSWORD_MAX_LENGTH,
                        chars: PASSWORD_SPECIAL_CHARS
                    });
                }

                setFormErrors(errors);
                
                if (Object.keys(errors).length === 0) {
                    setIsSubmitting(true);
                    try {
                        await createOperator(formData as CreateOperatorParams);
                        showSnackbar(t('operator.messages.createSuccess'), 'success');
                        setNewOperator({ name: '', username: '', password: '', email: ''});
                        fetchOperators();
                        if (dialog) {
                            dialog.closeDialog();
                        }
                    } catch (error: any) {
                        showSnackbar(error.response?.data?.error || t('operator.messages.createError'), 'error');
                    } finally {
                        setIsSubmitting(false);
                    }
                }
            };
            
            return (
                <form onSubmit={handleFormSubmit}>
                    <Box sx={{ width:{sm:'90vw', md:'500px'}, display: 'flex', flexDirection: 'column', gap: 2.5, p: 3 }}>
                        <TextField
                            label={t('common.fields.name')}
                            fullWidth
                            required
                            value={formData.name}
                            error={!!formErrors.name}
                            helperText={formErrors.name}
                            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                            InputProps={{
                                startAdornment: (
                                    <PersonIcon color="action" sx={{ mr: 1, opacity: 0.7 }} />
                                ),
                            }}
                        />
                        <TextField
                            label={t('common.fields.email')}
                            type="email"
                            fullWidth
                            required
                            value={formData.email}
                            error={!!formErrors.email}
                            helperText={formErrors.email}
                            onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                            InputProps={{
                                startAdornment: (
                                    <EmailIcon color="action" sx={{ mr: 1, opacity: 0.7 }} />
                                ),
                            }}
                        />
                        <TextField
                            label={t('common.fields.username')}
                            fullWidth
                            required
                            value={formData.username}
                            error={!!formErrors.username}
                            helperText={formErrors.username}
                            onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                            InputProps={{
                                startAdornment: (
                                    <BadgeIcon color="action" sx={{ mr: 1, opacity: 0.7 }} />
                                ),
                            }}
                        />
                        <TextField
                            label={t('common.fields.password')}
                            type="password"
                            fullWidth
                            required
                            value={formData.password}
                            error={!!formErrors.password}
                            helperText={formErrors.password}
                            onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                            FormHelperTextProps={{
                                sx: { 
                                    maxWidth: '100%'
                                }
                            }}
                        />
                        
                        <Divider sx={{ my: 1 }} />
                        
                        <Stack direction="row" spacing={2} justifyContent="flex-end">
                            <Button 
                                onClick={() => {
                                    dialog?.closeDialog();
                                }}
                                variant="outlined"
                                color="inherit"
                            >
                                {t('common.cancel')}
                            </Button>
                            <Button 
                                type="submit"
                                variant="contained"
                                disabled={isSubmitting}
                                startIcon={isSubmitting ? <CircularProgress size={20} /> : <AddIcon />}
                            >
                                {isSubmitting ? t('common.loading') : t('common.create')}
                            </Button>
                        </Stack>
                    </Box>
                </form>
            );
        };
        
        dialog.openDialog(
            <CreateOperatorForm />,
            t('operator.createNew')
        );
    };

    // Memoize the operator grid for better performance
    const operatorGrid = useMemo(() => {
        if (loading) {
            return (
                <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column',
                    justifyContent: 'center', 
                    alignItems: 'center', 
                    p: 8,
                    backgroundColor: alpha(theme.palette.background.default, 0.7),
                    borderRadius: 2
                }}>
                    <CircularProgress size={52} thickness={4} sx={{ mb: 3 }} color="primary" />
                    <Typography variant="h6" color="text.secondary" gutterBottom>
                        {t('common.loading')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        {t('operator.loadingMessage')}
                    </Typography>
                </Box>
            );
        }
        
        if (operators.length === 0) {
            return (
                <Paper 
                    variant="outlined" 
                    sx={{ 
                        textAlign: 'center', 
                        py: 8, 
                        px: 3,
                        backgroundColor: alpha(theme.palette.background.default, 0.5),
                        borderStyle: 'dashed',
                        borderRadius: 2,
                        borderWidth: '2px',
                        borderColor: alpha(theme.palette.divider, 0.4)
                    }}
                >
                    <Box sx={{
                        width: 100,
                        height: 100,
                        borderRadius: '50%',
                        backgroundColor: alpha(theme.palette.primary.light, 0.15),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 3,
                        animation: 'pulse 2s infinite ease-in-out',
                        '@keyframes pulse': {
                            '0%': { opacity: 0.7, transform: 'scale(0.95)' },
                            '50%': { opacity: 1, transform: 'scale(1)' },
                            '100%': { opacity: 0.7, transform: 'scale(0.95)' },
                        },
                    }}>
                        <ManageAccountsIcon 
                            color="primary" 
                            fontSize="large" 
                            sx={{ 
                                fontSize: 46,
                                opacity: 0.8 
                            }} 
                        />
                    </Box>
                    <Typography variant="h5" gutterBottom color="text.primary" sx={{ fontWeight: 600 }}>
                        {t('operator.noOperators')}
                    </Typography>
                    <Typography variant="body1" color="text.secondary" mb={4} sx={{ maxWidth: 500, mx: 'auto' }}>
                        {t('operator.emptyState')}
                    </Typography>
                    <Button
                        variant="contained"
                        size="large"
                        startIcon={<AddIcon />}
                        onClick={openCreateOperatorDialog}
                        sx={{
                            px: 4,
                            py: 1.2,
                            borderRadius: 2,
                            boxShadow: 3,
                            '&:hover': {
                                boxShadow: 5,
                                transform: 'translateY(-2px)'
                            },
                            transition: theme.transitions.create(['box-shadow', 'transform'], {
                                duration: theme.transitions.duration.standard,
                            }),
                        }}
                    >
                        {t('operator.addOperator')}
                    </Button>
                </Paper>
            );
        }
        
        return (
            <Grid2 container spacing={2.5}>
                {operators.map((operator) => (
                    <Grid2 size={{xs:12,sm:6,md:4,lg:3}} key={operator.id}>
                        <OperatorCard 
                            operator={operator} 
                            handleDeleteOperator={handleDeleteOperator}
                            t={t}
                            theme={theme}
                        />
                    </Grid2>
                ))}
            </Grid2>
        );
    }, [operators, loading, t, theme, openCreateOperatorDialog]);

    return (
        <Box>
            <Box sx={{ 
                display: 'flex',
                alignItems: 'center',
                mb: 3,
            }}>
                <StickyBreadcrumbs items={BREADCRUMB_ITEMS} />
            </Box>
            
            <Card 
                elevation={0}
                sx={{ 
                    p: 3,
                    border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                    borderRadius: 2,
                    transition: theme.transitions.create(['box-shadow']),
                    '&:hover': {
                        boxShadow: 1
                    }
                }}
            >
                <Box sx={{ 
                    display: 'flex', 
                    flexDirection: { xs: 'column', sm: 'row' }, 
                    justifyContent: 'space-between', 
                    alignItems: { xs: 'flex-start', sm: 'center' },
                    mb: 4,
                    pb: 3,
                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                }}>
                    <Stack direction="row" spacing={2.5} alignItems="center">
                        <Box 
                            sx={{ 
                                bgcolor: alpha(theme.palette.primary.main, 0.08),
                                p: 1.8,
                                borderRadius: 2,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center'
                            }}
                        >
                            <ManageAccountsIcon 
                                color="primary" 
                                sx={{ 
                                    fontSize: '2.5rem',
                                    opacity: 0.9
                                }} 
                            />
                        </Box>
                        <Box>
                            <Typography variant="h5" component="h2" gutterBottom={false} sx={{ fontWeight: 600 }}>
                                {t('operator.management')}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                                {t('operator.managementDescription')}
                            </Typography>
                        </Box>
                    </Stack>
                    
                    <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={openCreateOperatorDialog}
                        disabled={loading}
                        sx={{ 
                            fontWeight: 500,
                            boxShadow: 2,
                            borderRadius: 1.5,
                            px: 2.5,
                            py: 1,
                            mt: { xs: 3, sm: 0 },
                            transition: theme.transitions.create(['box-shadow', 'transform', 'background-color']),
                            '&:hover': { 
                                boxShadow: 4,
                                transform: 'translateY(-2px)'
                            },
                        }}
                    >
                        {t('operator.addOperator')}
                    </Button>
                </Box>

                {operatorGrid}
            </Card>
        </Box>
    );
};

export default ManageOperator;
