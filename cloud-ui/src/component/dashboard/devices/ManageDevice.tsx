/**
 * ManageDevice Component
 * 
 * A container component that displays the device management page with breadcrumb navigation.
 * Allows users to manage their devices and view device information.
 */

import React, { useState } from 'react';
import {
    Box,
    Card,
    Paper,
    Typography,
    Chip,
    IconButton,
    Tooltip,
    Stack,
    alpha,
    useTheme,
} from '@mui/material';
import {
    Visibility as VisibilityIcon,
    VisibilityOff as VisibilityOffIcon,
    ContentCopy as ContentCopyIcon,
    Devices as DevicesIcon,
} from '@mui/icons-material';
import StickyBreadcrumbs from '../../utils/StickyBreadcrumbs';
import DeviceList from './devices';
import { useSnackbar } from '../../../context/SnackbarContext';
import { useTranslation } from 'react-i18next';
import { getUserInfoFromStorage } from '../../../context/UserContext';
// Define the type for breadcrumb items
interface BreadcrumbItem {
    label: string;
    isCurrent: boolean;
}

// Define breadcrumb navigation items
const BREADCRUMB_ITEMS: BreadcrumbItem[] = [
    { label: 'Device', isCurrent: false },
    { label: 'Device Management', isCurrent: true }
];

const ClientIdDisplay = () => {
    const [showClientId, setShowClientId] = useState(false);
    const { showSnackbar } = useSnackbar();
    const { t } = useTranslation();
    const theme = useTheme();
    const { enterprise_id } = getUserInfoFromStorage();
    const clientId = enterprise_id;

    const handleCopyClientId = async () => {
        if (clientId) {
            try {
                await navigator.clipboard.writeText(clientId);
                showSnackbar(t('device.messages.clientIdCopied'), 'success');
            } catch (err) {
                const tempInput = document.createElement('input');
                tempInput.value = clientId;
                document.body.appendChild(tempInput);
                tempInput.select();
                document.execCommand('copy');
                document.body.removeChild(tempInput);
                showSnackbar(t('device.messages.clientIdCopied'), 'success');
            }
        }
    };

    if (!clientId) return null;

    return (
        <Paper 
            variant="outlined" 
            sx={{ 
                mb: 3, 
                p: 2, 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'space-between',
                gap: 2,
                borderColor: alpha(theme.palette.primary.main, 0.3),
                backgroundColor: alpha(theme.palette.primary.light, 0.08),
                boxShadow: `inset 0 0 20px ${alpha(theme.palette.primary.light, 0.1)}`,
                borderRadius: 2,
            }}
        >
            <Stack direction="row" alignItems="center" spacing={2}>
                <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    bgcolor: alpha(theme.palette.primary.main, 0.15),
                    borderRadius: '50%',
                    p: 1,
                    width: 40,
                    height: 40
                }}>
                    <DevicesIcon color="primary" sx={{ opacity: 0.9 }} />
                </Box>
                <Box>
                    <Typography variant="subtitle2" color="primary" gutterBottom 
                        sx={{ 
                            fontSize: '0.75rem', 
                            fontWeight: 600,
                            textTransform: 'uppercase',
                            letterSpacing: '0.5px'
                        }}>
                        {t('device.fields.clientId')}
                    </Typography>
                    <Stack direction="row" alignItems="center" sx={{ flexWrap: 'wrap' }}>
                        <Chip
                            label={showClientId ? clientId : '•'.repeat(Math.min(clientId.length, 20))}
                            color="primary"
                            variant="outlined"
                            size="small"
                            sx={{ 
                                fontFamily: 'monospace',
                                maxWidth: { xs: '180px', sm: '300px', md: '400px' },
                                mr: 1,
                                borderRadius: 1,
                                fontWeight: 500,
                                '& .MuiChip-label': {
                                    whiteSpace: 'nowrap',
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis'
                                }
                            }}
                        />
                    </Stack>
                </Box>
            </Stack>
            <Stack direction="row" spacing={1}>
                <Tooltip title={showClientId ? t('device.clientId.hide') : t('device.clientId.show')} arrow placement="top">
                    <IconButton
                        onClick={() => setShowClientId(!showClientId)}
                        size="small"
                        color="primary"
                        sx={{ 
                            border: 1, 
                            borderColor: alpha(theme.palette.primary.main, 0.3),
                        }}
                    >
                        {showClientId ? <VisibilityOffIcon fontSize="small" /> : <VisibilityIcon fontSize="small" />}
                    </IconButton>
                </Tooltip>
                <Tooltip title={t('device.clientId.copy')} arrow placement="top">
                    <IconButton 
                        onClick={handleCopyClientId} 
                        size="small"
                        color="primary"
                        sx={{ 
                            border: 1, 
                            borderColor: alpha(theme.palette.primary.main, 0.3),
                        }}
                    >
                        <ContentCopyIcon fontSize="small" />
                    </IconButton>
                </Tooltip>
            </Stack>
        </Paper>
    );
};

const ManageDevice: React.FC = () => {
    const theme = useTheme();
    
    return (
        <Box sx={{
            position: 'relative',
        }}>
            <Box sx={{ 
                display: 'flex',
                alignItems: 'center',
                mb: 2,
            }}>
                <StickyBreadcrumbs items={BREADCRUMB_ITEMS} />
            </Box>
            
            <Card 
                elevation={0}
                sx={{ 
                    mt: 2, 
                    p: 3,
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                    backgroundColor: alpha(theme.palette.background.paper, 0.8),
                    backdropFilter: 'blur(8px)',
                    boxShadow: `0 8px 30px ${alpha(theme.palette.common.black, 0.07)}`,
                    transition: 'all 0.3s ease',
                }}
            >
                <ClientIdDisplay />
                <DeviceList />
            </Card>
        </Box>
    );
};

export default ManageDevice; 