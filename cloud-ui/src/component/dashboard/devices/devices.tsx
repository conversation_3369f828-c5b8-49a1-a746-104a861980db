import React, { useContext, useEffect, useState } from 'react';
import {
    Box,
    Typography,
    IconButton,
    Tooltip,
    Button,
    Stack,
    Card,
    CardContent,
    Chip,
    CardActions,
    Divider,
    CircularProgress,
    alpha,
    useTheme,
    Paper,
    Grid2,
    TextField,
    InputAdornment
} from '@mui/material';
import { useSnackbar } from '../../../context/SnackbarContext';
import { DialogContext } from '../../../context/GlobalDialog';
import { 
    Refresh as RefreshIcon, 
    Add as AddIcon,
    InfoOutlined,
    Warning as WarningIcon,
    Topic as TopicIcon,
    ArrowForward as ArrowForwardIcon,
    Edit as EditIcon,
    Check as CheckIcon,
    Close as CloseIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { Device, getDevices, updateDevice } from '../../../services/deviceService';
import { RegisterDeviceDialog } from './components/RegisterDeviceDialog';
import { DeviceDetailDialog } from './components/DeviceDetailDialog';
import { AssignOperatorDialog } from './components/AssignOperatorDialog';
import { CreateTopicDialog } from './components/CreateTopicDialog';

const DeviceList: React.FC = () => {
    const [devices, setDevices] = useState<Device[]>([]);
    const [loading, setLoading] = useState(true);
    const [editingDeviceId, setEditingDeviceId] = useState<string | null>(null);
    const [newDeviceName, setNewDeviceName] = useState('');
    const { showSnackbar } = useSnackbar();
    const dialog = useContext(DialogContext);
    const { t } = useTranslation();
    const theme = useTheme();
    
    if (!dialog) return null;
    const { openDialog } = dialog;

    const fetchDevices = async () => {
        try {
            const response = await getDevices({});
            setDevices(response.devices || []);
            return response.devices || [];
        } catch (error: any) {
            showSnackbar(t('device.messages.fetchError', { error: error.response?.data?.error }), 'error');
            return [];
        } finally {
            setLoading(false);
        }
    };

    const refreshDevice = async (deviceSN: string, fallbackDevice: Device): Promise<Device> => {
        const devices = await fetchDevices();
        return devices.find(d => d.device_sn === deviceSN) || fallbackDevice;
    };

    useEffect(() => {
        fetchDevices();
    }, []);

    const handleDeviceClick = (device: Device) => {
        openDialog(
            <DeviceDetailDialog 
                device={device} 
                onSuccess={fetchDevices}
                refreshDevice={() => refreshDevice(device.device_sn, device)}
            />,
            ""
        );
    };

    const handleEditDeviceName = (device: Device) => {
        setEditingDeviceId(device.id);
        setNewDeviceName(device.name || '');
    };

    const handleCancelEdit = () => {
        setEditingDeviceId(null);
        setNewDeviceName('');
    };

    const handleSaveDeviceName = async (device: Device) => {
        if (newDeviceName.trim() === '') {
            showSnackbar(t('device.messages.nameRequired'), 'error');
            return;
        }

        setLoading(true);
        try {
            await updateDevice({
                device_sn: device.device_sn,
                name: newDeviceName.trim()
            });
            
            showSnackbar(t('device.messages.nameUpdated'), 'success');
            fetchDevices();
        } catch (error: any) {
            showSnackbar(t('device.messages.updateError', { error: error.response?.data?.error }), 'error');
        } finally {
            setEditingDeviceId(null);
            setNewDeviceName('');
        }
    };

    if (loading) {
        return (
            <Box sx={{ 
                display: 'flex', 
                flexDirection: 'column', 
                justifyContent: 'center', 
                alignItems: 'center', 
                p: 5,
                gap: 2
            }}>
                <CircularProgress 
                    size={48} 
                    thickness={4} 
                    sx={{ 
                        color: theme.palette.primary.main,
                        '& .MuiCircularProgress-circle': {
                            strokeLinecap: 'round',
                        }
                    }} 
                />
                <Typography 
                    variant="h6" 
                    color="text.secondary"
                    sx={{ 
                        fontWeight: 500,
                        opacity: 0.8
                    }}
                >
                    {t('common.loading')}
                </Typography>
            </Box>
        );
    }

    return (
        <Box>
            <Box sx={{ 
                mb: 3, 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems:'center',
                '@media (max-width: 600px)': {
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    gap: 2
                }
            }}>
                <Stack 
                    direction={{ xs: 'column', sm: 'row' }} 
                    spacing={1.5}
                    sx={{ width: { xs: '100%', sm: 'auto' } }}
                >
                    <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={() => openDialog(
                            <RegisterDeviceDialog onClose={dialog.closeDialog} onSuccess={fetchDevices} />,
                            t('device.register')
                        )}
                        sx={{
                            fontWeight: 600,
                            boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
                            background: `linear-gradient(145deg, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.dark, 0.9)})`,
                            borderRadius: 2,
                            px: 2.5,
                            py: 1,
                        }}
                    >
                        {t('device.register')}
                    </Button>
                    <Button
                        variant="outlined"
                        startIcon={<TopicIcon />}
                        onClick={() => openDialog(
                            <CreateTopicDialog onClose={dialog.closeDialog} onSuccess={fetchDevices} />,
                            t('device.topics.create.title')
                        )}
                        disabled={devices.length === 0}
                        sx={{ 
                            fontWeight: 500,
                            borderRadius: 2,
                            borderWidth: '1.5px',
                        }}
                    >
                        {t('device.createTopic')}
                    </Button>
                </Stack>
                <Tooltip title={t('device.refresh')} arrow>
                    <IconButton 
                        onClick={fetchDevices}
                        color="primary"
                        sx={{ 
                            border: `1.5px solid ${alpha(theme.palette.primary.main, 0.3)}`,
                            borderRadius: 2,
                            transition: 'all 0.3s ease'
                        }}
                    >
                        <RefreshIcon />
                    </IconButton>
                </Tooltip>
            </Box>

            {devices.length === 0 ? (
                <Paper 
                    variant="outlined" 
                    sx={{ 
                        textAlign: 'center', 
                        py: 6, 
                        px: 3,
                        backgroundColor: alpha(theme.palette.background.default, 0.5),
                        borderStyle: 'dashed',
                        borderWidth: 2,
                        borderRadius: 3,
                        borderColor: alpha(theme.palette.primary.main, 0.2),
                    }}
                >
                    <Box 
                        sx={{
                            width: 60,
                            height: 60,
                            borderRadius: '50%',
                            backgroundColor: alpha(theme.palette.warning.light, 0.15),
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            margin: '0 auto 24px',
                        }}
                    >
                        <WarningIcon color="warning" fontSize="large" sx={{ opacity: 0.8 }} />
                    </Box>
                    <Typography variant="h6" gutterBottom color="text.secondary" sx={{ mb: 3 }}>
                        {t('device.noDevices')}
                    </Typography>
                    <Button
                        variant="contained"
                        startIcon={<AddIcon />}
                        onClick={() => openDialog(
                            <RegisterDeviceDialog onClose={dialog.closeDialog} onSuccess={fetchDevices} />,
                            t('device.register')
                        )}
                        sx={{
                            fontWeight: 600,
                            boxShadow: '0 6px 12px rgba(0, 0, 0, 0.1)',
                            background: `linear-gradient(145deg, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.dark, 0.9)})`,
                            borderRadius: 2,
                            px: 3,
                            py: 1.2,
                        }}
                    >
                        {t('device.register')}
                    </Button>
                </Paper>
            ) : (
                <Grid2 container spacing={3}>
                    {devices.map((device) => (
                        <Grid2 size={{xs:12, sm:6, md:4, lg:3}} key={device.id}>
                            <Card 
                                variant="outlined" 
                                sx={{ 
                                    height: '100%',
                                    display: 'flex',
                                    flexDirection: 'column',
                                    position: 'relative',
                                    borderRadius: 3,
                                    borderColor: alpha(theme.palette.primary.main, 0.1),
                                    transition: theme.transitions.create(['box-shadow', 'transform', 'border-color'], {
                                        duration: theme.transitions.duration.standard,
                                    }),
                                    '&::before': {
                                        content: '""',
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        right: 0,
                                        height: 6,
                                        background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${alpha(theme.palette.primary.light, 0.7)})`,
                                        borderTopLeftRadius: 12,
                                        borderTopRightRadius: 12,
                                    },
                                    '&:hover': {
                                        boxShadow: `0 12px 28px ${alpha(theme.palette.primary.main, 0.1)}`,
                                        transform: 'translateY(-6px)',
                                        borderColor: alpha(theme.palette.primary.main, 0.2),
                                    }
                                }}
                            >
                                <CardContent sx={{ flexGrow: 1, pb: 1, pt: 3 }}>

                                    
                                    {editingDeviceId === device.id ? (
                                        <Box sx={{ mb: 1.5 }}>
                                            <TextField
                                                fullWidth
                                                size="small"
                                                value={newDeviceName}
                                                onChange={(e) => setNewDeviceName(e.target.value)}
                                                placeholder={t('device.namePlaceholder')}
                                                autoFocus
                                                InputProps={{
                                                    sx: { borderRadius: 2 },
                                                    endAdornment: (
                                                        <InputAdornment position="end">
                                                            <IconButton 
                                                                size="small" 
                                                                onClick={() => handleSaveDeviceName(device)}
                                                                color="primary"
                                                                sx={{
                                                                    transition: 'transform 0.2s',
                                                                    '&:hover': { transform: 'scale(1.1)' }
                                                                }}
                                                            >
                                                                <CheckIcon fontSize="small" />
                                                            </IconButton>
                                                            <IconButton 
                                                                size="small" 
                                                                onClick={handleCancelEdit}
                                                                color="default"
                                                                sx={{
                                                                    transition: 'transform 0.2s',
                                                                    '&:hover': { transform: 'scale(1.1)' }
                                                                }}
                                                            >
                                                                <CloseIcon fontSize="small" />
                                                            </IconButton>
                                                        </InputAdornment>
                                                    )
                                                }}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        handleSaveDeviceName(device);
                                                    } else if (e.key === 'Escape') {
                                                        handleCancelEdit();
                                                    }
                                                }}
                                            />
                                        </Box>
                                    ) : (
                                        <Box sx={{ 
                                            display: 'flex', 
                                            alignItems: 'center', 
                                            justifyContent: 'space-between',
                                            mb: 0.5
                                        }}>
                                            <Typography 
                                                variant="h6" 
                                                noWrap 
                                                sx={{ 
                                                    fontWeight: 600,
                                                    flexGrow: 1,
                                                    fontSize: '1.1rem',
                                                    color: theme.palette.text.primary
                                                }}
                                            >
                                                {device.name || t('device.unnamedDevice')}
                                            </Typography>
                                            <Tooltip title={t('device.editName')} arrow>
                                                <IconButton 
                                                    size="small" 
                                                    onClick={() => handleEditDeviceName(device)}
                                                    sx={{ 
                                                        ml: 0.5, 
                                                        opacity: 0.6,
                                                        '&:hover': { 
                                                            opacity: 1,
                                                            transform: 'rotate(15deg)'
                                                        },
                                                        transition: 'all 0.2s ease'
                                                    }}
                                                >
                                                    <EditIcon fontSize="small" />
                                                </IconButton>
                                            </Tooltip>
                                        </Box>
                                    )}
                                    <Typography 
                                        color="text.secondary" 
                                        gutterBottom
                                        sx={{
                                            fontFamily: 'monospace',
                                            fontSize: '0.8rem',
                                            backgroundColor: alpha(theme.palette.background.default, 0.6),
                                            padding: '4px 8px',
                                            borderRadius: 1,
                                            display: 'inline-block',
                                        }}
                                    >
                                        SN: {device.device_sn}<br/>
                                        IMEI: {device.imei_code}
                                    </Typography>
                                    
                                    <Box sx={{ 
                                        minHeight: '40px'
                                    }}>
                                        {device.topics && device.topics.length > 0 ? (
                                            <Box sx={{
                                                display: 'flex',
                                                flexWrap: 'wrap',
                                                gap: 1,
                                                mt: 1,
                                            }}>
                                                {device.topics.slice(0, 3).map((topic, _) => (
                                                    <Chip
                                                        key={topic}
                                                        label={topic.split('/').pop()}
                                                        size="small"
                                                        color="primary"
                                                        sx={{
                                                            maxWidth: '120px',
                                                            borderRadius: 1,
                                                            fontSize: '0.7rem',
                                                            fontWeight: 500,
                                                            '& .MuiChip-label': {
                                                                whiteSpace: 'nowrap',
                                                                overflow: 'hidden',
                                                                textOverflow: 'ellipsis'
                                                            },
                                                            boxShadow: `0 2px 4px ${alpha(theme.palette.primary.main, 0.1)}`,
                                                            '&:hover': {
                                                                boxShadow: `0 2px 6px ${alpha(theme.palette.primary.main, 0.2)}`,
                                                            },
                                                            transition: 'all 0.2s ease'
                                                        }}
                                                    />
                                                ))}
                                                {device.topics.length > 3 && (
                                                    <Chip
                                                        size="small"
                                                        label={`+${device.topics.length - 3}`}
                                                        variant="outlined"
                                                        color="primary"
                                                        sx={{
                                                            borderRadius: 1,
                                                            fontSize: '0.7rem',
                                                            fontWeight: 500
                                                        }}
                                                    />
                                                )}
                                            </Box>
                                        ) : (
                                            <Typography 
                                                variant="body2" 
                                                color="text.secondary" 
                                                sx={{ 
                                                    mt: 1, 
                                                    fontStyle: 'italic',
                                                    opacity: 0.7,
                                                    fontSize: '0.8rem'
                                                }}
                                            >
                                                {t('device.noTopics')}
                                            </Typography>
                                        )}
                                    </Box>
                                </CardContent>
                                <Divider />
                                <CardActions sx={{ 
                                    p: 1.5, 
                                    pt: 1, 
                                    pb: 1, 
                                    justifyContent: 'space-between',
                                    bgcolor: alpha(theme.palette.background.default, 0.5)
                                }}>
                                    <Button
                                        size="small"
                                        startIcon={<InfoOutlined fontSize="small" />}
                                        onClick={() => handleDeviceClick(device)}
                                        sx={{ 
                                            fontSize: '0.75rem',
                                            fontWeight: 500,
                                            borderRadius: 1.5,
                                        }}
                                    >
                                        {t('device.details')}
                                    </Button>
                                    <Button
                                        size="small"
                                        color="primary"
                                        endIcon={<ArrowForwardIcon fontSize="small" />}
                                        onClick={() => openDialog(
                                            <AssignOperatorDialog device={device} onClose={dialog.closeDialog} onSuccess={fetchDevices} />,
                                            t('device.operators.assign')
                                        )}
                                        sx={{ 
                                            fontSize: '0.75rem',
                                            fontWeight: 500,
                                            borderRadius: 1.5,
                                        }}
                                    >
                                        {t('device.operators.assign')}
                                    </Button>
                                </CardActions>
                            </Card>
                        </Grid2>
                    ))}
                </Grid2>
            )}
        </Box>
    );
};

export default DeviceList; 