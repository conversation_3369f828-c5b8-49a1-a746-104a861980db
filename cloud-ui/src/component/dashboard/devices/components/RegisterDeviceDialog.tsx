import React, { useState } from 'react';
import {
    Box,
    TextField,
    Button,
    Stack,
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { registerDevice } from '../../../../services/deviceService';
import { getUserInfoFromStorage } from '../../../../context/UserContext';

interface Props {
    onClose: () => void;
    onSuccess: () => void;
}

export const RegisterDeviceDialog: React.FC<Props> = ({ onClose, onSuccess }) => {
    const [deviceSN, setDeviceSN] = useState('');
    const [deviceName, setDeviceName] = useState('');
    const { t } = useTranslation();
    const { showSnackbar } = useSnackbar();
    const [registering, setRegistering] = useState(false);
    const enterpriseID = getUserInfoFromStorage().enterprise_id;

    const handleRegister = async () => {
        if (!deviceSN.trim() || !deviceName.trim() || !enterpriseID?.trim()) {
            showSnackbar(t('common.noData'), 'warning');
            return;
        }

        setRegistering(true);
        try {
            await registerDevice({
                device_sn: deviceSN.trim(),
                name: deviceName.trim(),
            });
            showSnackbar(t('device.messages.deviceRegisterSuccess'), 'success');
            onSuccess();
            onClose();
        } catch (error: any) {
            showSnackbar(t('device.messages.deviceRegisterError', { error: error.response?.data?.error }), 'error');
        } finally {
            setRegistering(false);
        }
    };

    return (
        <Box sx={{ p: 2, maxWidth: 500 }}>
            <TextField
                fullWidth
                label={t('device.fields.sn')}
                value={deviceSN}
                onChange={(e) => setDeviceSN(e.target.value)}
                sx={{ mb: 2 }}
            />
            <TextField
                fullWidth
                label={t('device.fields.name')}
                value={deviceName}
                onChange={(e) => setDeviceName(e.target.value)}
                sx={{ mb: 2 }}
            />
            <Stack direction="row" spacing={2} justifyContent="flex-end">
                <Button onClick={onClose}>{t('common.cancel')}</Button>
                <Button
                    variant="contained"
                    onClick={handleRegister}
                    disabled={registering || !deviceSN.trim() || !deviceName.trim() || !enterpriseID?.trim()}
                >
                    {t('device.register')}
                </Button>
            </Stack>
        </Box>
    );
}; 