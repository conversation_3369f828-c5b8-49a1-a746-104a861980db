import { useState, useEffect, useContext } from 'react';
import {
  Box,
  Button,
  Typography,
  CircularProgress,
  TextField,
  Alert,
  Stack,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Table,
  TableContainer,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Paper,
  Divider,
  IconButton,
} from '@mui/material';
import { Add as AddIcon, Settings as SettingsIcon, Delete as DeleteIcon } from '@mui/icons-material';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { DialogContext } from '../../../../context/GlobalDialog';
import { useTranslation } from 'react-i18next';
import { 
  AddDeviceFormData, 
  DeviceType, 
  getDeviceTypes,
  addDeviceType,
  deleteDeviceType
} from '../../../../services/deviceService';

// TypeManager is needed for the AddDeviceDialog functionality
interface TypeManagerProps {
  onClose: () => void;
}

const TypeManager: React.FC<TypeManagerProps> = ({ onClose }) => {
  const { t } = useTranslation();
  const { showSnackbar } = useSnackbar();
  const [types, setTypes] = useState<DeviceType[]>([]);
  const [loading, setLoading] = useState(false);
  const [newTypeName, setNewTypeName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  // Fetch device types
  const fetchTypes = async () => {
    setLoading(true);
    try {
      const response = await getDeviceTypes();
      setTypes(response.types || []);
    } catch (err: any) {
      showSnackbar(t('device.type.messages.fetchError', { error: err.response?.data?.error }), 'error');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTypes();
  }, []);

  // Add new device type
  const handleAddType = async () => {
    if (!newTypeName.trim()) {
      setError(t('device.type.nameRequired'));
      return;
    }

    setIsSubmitting(true);
    setError('');
    
    try {
      await addDeviceType(newTypeName.trim());
      showSnackbar(t('device.type.messages.addSuccess'), 'success');
      setNewTypeName('');
      fetchTypes();
    } catch (err: any) {
      const errorMsg = err.response?.data?.error || t('device.type.messages.addError');
      setError(errorMsg);
      showSnackbar(errorMsg, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete device type
  const handleDeleteType = async (typeName: string) => {
    setIsSubmitting(true);
    try {
      await deleteDeviceType(typeName);
      showSnackbar(t('device.type.messages.deleteSuccess'), 'success');
      fetchTypes();
    } catch (err: any) {
      const response = err.response?.data;
      let errorMsg = response?.error || t('device.type.messages.deleteError');
      
      // If type is in use, show the device count
      if (response?.device_count) {
        errorMsg = t('device.type.messages.typeInUse', { count: response.device_count });
      }
      
      showSnackbar(errorMsg, 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ minWidth: { xs: '80vw', sm: '75vw', md: "600px" }, p: 2 }}>
      <Box sx={{ mb: 2 }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          {t('device.type.manageInfo')}
        </Alert>
        <Stack direction="row" spacing={1}>
          <TextField
            size="small"
            value={newTypeName}
            onChange={(e) => setNewTypeName(e.target.value)}
            label={t('device.type.nameLabel')}
            error={!!error}
            helperText={error}
            disabled={isSubmitting}
          />
          <Button
            variant="contained"
            onClick={handleAddType}
            disabled={isSubmitting || !newTypeName.trim()}
            startIcon={isSubmitting ? <CircularProgress size={20} /> : <AddIcon />}
            sx={{ height: '40px' }}
          >
            {t('device.type.add')}
          </Button>
        </Stack>
      </Box>

      <Divider />
 
      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle1" gutterBottom>
          {t('device.type.existingTypes')}
        </Typography>
        
        {loading ? (
          <Box display="flex" justifyContent="center" my={3}>
            <CircularProgress size={24} />
          </Box>
        ) : types.length === 0 ? (
          <Alert severity="warning">{t('device.type.noTypes')}</Alert>
        ) : (
          <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>{t('device.type.name')}</TableCell>
                  <TableCell align="right">{t('common.actions')}</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {types.map((type) => (
                  <TableRow key={type.id}>
                    <TableCell>{type.name}</TableCell>
                    <TableCell align="right">
                      <IconButton
                        size="small"
                        color="error"
                        onClick={() => handleDeleteType(type.name)}
                        disabled={isSubmitting}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        )}
      </Box>
      
      <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={onClose} variant="outlined">
          {t('common.close')}
        </Button>
      </Box>
    </Box>
  );
};

// Main component definition
export interface AddDeviceDialogProps {
  onAdd: (formData: AddDeviceFormData) => Promise<any>;
  onClose: () => void;
}

// Main component to export
const AddDeviceDialog: React.FC<AddDeviceDialogProps> = ({ onAdd, onClose }) => {
  const { t } = useTranslation();
  const { showSnackbar } = useSnackbar();
  const dialogContext = useContext(DialogContext);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [types, setTypes] = useState<DeviceType[]>([]);
  const [typesLoading, setTypesLoading] = useState(false);
  const [formData, setFormData] = useState<AddDeviceFormData>({
    device_sn: '',
    type: '',
    imei_code: '',
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch device types
  const fetchTypes = async () => {
    setTypesLoading(true);
    try {
      const response = await getDeviceTypes();
      setTypes(response.types || []);
      
      // If there are no types, show an info dialog
      if (response.types.length === 0) {
        showSnackbar(t('device.type.noTypesAvailable'), 'warning');
      }
      
    } catch (err: any) {
      showSnackbar(t('device.type.messages.fetchError', { error: err.response?.data?.error }), 'error');
    } finally {
      setTypesLoading(false);
    }
  };

  useEffect(() => {
    fetchTypes();
  }, []);

  const handleInputChange = (field: keyof AddDeviceFormData, value: string) => {
    setFormData({
      ...formData,
      [field]: value,
    });
    
    // Clear error when input changes
    if (errors[field]) {
      setErrors({
        ...errors,
        [field]: '',
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};
    
    // Validate serial number
    if (!formData.device_sn) {
      newErrors.device_sn = t('device.validation.snRequired');
    } else if (!/^[a-zA-Z0-9]+$/.test(formData.device_sn)) {
      newErrors.device_sn = t('device.validation.snAlphanumeric');
    } else if (formData.device_sn.length > 24) {
      newErrors.device_sn = t('device.validation.snLength');
    }
    
    // Validate IMEI
    if (!formData.imei_code) {
      newErrors.imei_code = t('device.validation.imeiRequired');
    } else if (!/^[a-zA-Z0-9]+$/.test(formData.imei_code)) {
      newErrors.imei_code = t('device.validation.imeiAlphanumeric');
    } else if (formData.imei_code.length > 24) {
      newErrors.imei_code = t('device.validation.imeiLength');
    }
    
    // Validate type
    if (!formData.type) {
      newErrors.type = t('device.validation.typeRequired');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }
    
    try {
      setIsSubmitting(true);
      await onAdd(formData);
      onClose();
    } catch (error: any) {
      // Handle validation errors from the server
      const responseData = error.response?.data;
      if (responseData && responseData.field) {
        setErrors({
          ...errors,
          [responseData.field]: responseData.details || responseData.error,
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open the type manager dialog
  const handleOpenTypeManager = () => {
    if (!dialogContext) return;
    
    dialogContext.openDialog(
      <TypeManager 
        onClose={() => {
          dialogContext.closeDialog();
          fetchTypes(); // Refresh types after managing them
        }} 
      />,
      t('device.type.manageTitle')
    );
  };

  return (
    <Box sx={{ minWidth: { xs: '300px', sm: '450px' }, p: 3 }}>
      <Alert 
        severity="info" 
        sx={{ mb: 3, '& .MuiAlert-message': { width: '100%' } }}
      >
        <Typography variant="body2">
          {t('device.add.instruction')}
        </Typography>
      </Alert>
      
      <Stack spacing={2}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <InputLabel id="device-type-label">{t('device.fields.type')}</InputLabel>
          <Button
            size="small"
            startIcon={<SettingsIcon fontSize="small" />}
            onClick={handleOpenTypeManager}
          >
            {t('device.type.manageTitle')}
          </Button>
        </Box>
        <FormControl fullWidth error={!!errors.type}>
          <Select
            labelId="device-type-label"
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
            disabled={isSubmitting || typesLoading}
          >
            {typesLoading ? (
              <MenuItem disabled>
                <Box display="flex" alignItems="center">
                  <CircularProgress size={20} sx={{ mr: 1 }} />
                  {t('common.loading')}
                </Box>
              </MenuItem>
            ) : (
              types.map((type) => (
                <MenuItem key={type.id} value={type.name}>
                  {type.name}
                </MenuItem>
              ))
            )}
          </Select>
          {errors.type && <FormHelperText>{errors.type}</FormHelperText>}
        </FormControl>
        
        <TextField
          fullWidth
          label={t('device.fields.sn')}
          value={formData.device_sn}
          onChange={(e) => handleInputChange('device_sn', e.target.value)}
          error={!!errors.device_sn}
          helperText={errors.device_sn || t('device.validation.snHelp')}
          disabled={isSubmitting}
          inputProps={{ maxLength: 24 }}
        />
        
        <TextField
          fullWidth
          label={t('device.fields.imei')}
          value={formData.imei_code}
          onChange={(e) => handleInputChange('imei_code', e.target.value)}
          error={!!errors.imei_code}
          helperText={errors.imei_code || t('device.validation.imeiHelp')}
          disabled={isSubmitting}
          inputProps={{ maxLength: 24 }}
        />
      </Stack>
      
      <Stack 
        direction="row" 
        spacing={2} 
        sx={{ 
          mt: 4, 
          justifyContent: 'flex-end'
        }}
      >
        <Button 
          onClick={onClose} 
          disabled={isSubmitting}
          variant="outlined"
          color="inherit"
        >
          {t('common.cancel')}
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          color="primary"
          disabled={isSubmitting}
          startIcon={isSubmitting ? <CircularProgress size={20} /> : <AddIcon />}
        >
          {isSubmitting ? t('device.add.adding') : t('device.add.add')}
        </Button>
      </Stack>
    </Box>
  );
};

export default AddDeviceDialog;
