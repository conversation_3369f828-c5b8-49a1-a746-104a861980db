import React, { useState, useContext } from 'react';
import {
    Box,
    Typography,
    IconButton,
    Grid2,
    Chip,
    List,
    ListItem,
    ListItemText,
    Tooltip,
    Paper,
    alpha,
    useTheme,
    Avatar,
    ListItemIcon,
    Button,
} from '@mui/material';
import {
    Add as AddIcon,
    Info as InfoIcon,
    DevicesOutlined as DevicesIcon,
    Fingerprint as FingerprintIcon,
    CalendarMonth as CalendarIcon,
    Person as PersonIcon,
    DeleteOutline as DeleteIcon,
    PersonAdd as PersonAddIcon,
    ConfirmationNumber as ConfirmationNumberIcon
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { DialogContext } from '../../../../context/GlobalDialog';
import { Device } from '../../../../services/deviceService';
import { TopicInfoDialog } from './TopicInfoDialog';
import { AssignOperatorDialog } from './AssignOperatorDialog';
import { CreateTopicDialog } from './CreateTopicDialog';
import { deleteTopic } from '../../../../services/topicService';
import { unassignOperator } from '../../../../services/operatorService';

interface Props {
    device: Device;
    onSuccess: () => void;
    refreshDevice: () => Promise<Device>;
}

export const DeviceDetailDialog: React.FC<Props> = ({ 
    device: initialDevice, 
    onSuccess, 
    refreshDevice, 
}) => {
    const [device, setDevice] = useState(initialDevice);
    const dialog = useContext(DialogContext);
    const { showSnackbar } = useSnackbar();
    const { t } = useTranslation();
    const theme = useTheme();

    const handleTopicClick = (topic: string) => {
        if (!dialog) return;
        dialog.openDialog(
            <TopicInfoDialog 
                topic={topic} 
                deviceSn={device.device_sn} 
                onClose={dialog.closeDialog}
            />,""
        );
    };

    const handleDeleteTopic = async (topic: string) => {
        try {
            await deleteTopic({
                topic,
                device_sn: device.device_sn
            });
            showSnackbar(t('device.messages.topicDeleteSuccess'), 'success');
            const updatedDevice = await refreshDevice();
            setDevice(updatedDevice);
            onSuccess();
        } catch (error: any) {
            showSnackbar(t('device.messages.topicDeleteError', { error: error.response?.data?.error }), 'error');
        }
    };

    const handleAssignOperators = () => {
        if (!dialog) return;
        dialog.openDialog(
            <AssignOperatorDialog
                device={device}
                onClose={dialog.closeDialog}
                onSuccess={async () => {
                    const updatedDevice = await refreshDevice();
                    setDevice(updatedDevice);
                    onSuccess();
                }}
            />,
            t('device.operators.assign')
        );
    };

    const handleUnassignOperator = async (operator: string) => {
        try {
            await unassignOperator({
                device_sn: device.device_sn,
                operator_id: operator
            });
            showSnackbar(t('device.messages.operatorUnassignSuccess'), 'success');
            const updatedDevice = await refreshDevice();
            setDevice(updatedDevice);
        } catch (error: any) {
            showSnackbar(t('device.messages.operatorUnassignError', { error: error.response?.data?.error }), 'error');
        }
    };


    const handleCreateTopic = () => {
        if (!dialog) return;
        dialog.openDialog(
            <CreateTopicDialog 
                onClose={dialog.closeDialog} 
                onSuccess={onSuccess}
                preSelectedDevice={device}
            />,
            t('device.createTopic')
        );
    };

    const formatDate = (dateString: string) => {
        try {
            return new Date(dateString).toLocaleString();
        } catch (e) {
            return '-';
        }
    };

    return (
        <Box p={1} sx={{ 
            minWidth: { xs: 320, sm: 450, md: 550 },
            maxWidth: 600
        }}>
            <Box 
                sx={{ 
                    mb: 3,
                    p: 2,
                    borderRadius: 2,
                    bgcolor: alpha(theme.palette.primary.main, 0.07),
                    display: 'flex',
                    alignItems: 'center',
                    gap: 2
                }}
            >
                <Avatar
                    sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.15),
                        color: theme.palette.primary.main,
                        width: 48,
                        height: 48
                    }}
                >
                    <DevicesIcon />
                </Avatar>
                <Box>
                    <Typography 
                        variant="h6" 
                        color="primary.main" 
                        sx={{ 
                            fontWeight: 600,
                            mb: 0.5
                        }}
                    >
                        {device.name || t('device.unnamedDevice')}
                    </Typography>
                    <Typography 
                        variant="body2"
                        sx={{
                            color: alpha(theme.palette.text.primary, 0.7),
                            fontFamily: 'monospace',
                            fontSize: '0.85rem'
                        }}
                    >
                        SN: {device.device_sn}
                    </Typography>
                </Box>
            </Box>

            <Paper
                elevation={0}
                variant="outlined"
                sx={{
                    borderRadius: 2,
                    mb: 3,
                    overflow: 'hidden',
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.05)}`
                }}
            >
                <Grid2 container>
                    <Grid2 size={{xs: 12, md: 6}}>
                        <List disablePadding>
                            <ListItem 
                                divider={true} 
                                sx={{ 
                                    borderRight: { xs: 'none', md: `1px solid ${alpha(theme.palette.divider, 0.1)}` }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <FingerprintIcon color="action" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText 
                                    primary={
                                        <Typography variant="body1" fontWeight={500}>
                                            {device.device_sn || '-'}
                                        </Typography>
                                    }
                                    secondary={
                                        <Typography variant="caption" color="text.secondary">
                                            {t('device.fields.sn')}
                                        </Typography>
                                    }
                                />
                            </ListItem>
                        </List>
                    </Grid2>
                    <Grid2 size={{xs: 12, md: 6}}>
                        <List disablePadding>
                            <ListItem 
                                sx={{ 
                                    borderBottom: { xs: 'none', md: `1px solid ${alpha(theme.palette.divider, 0.1)}` }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <ConfirmationNumberIcon color="action" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText 
                                    primary={
                                        <Typography variant="body1" fontWeight={500}>
                                            {device.imei_code || '-'}
                                        </Typography>
                                    }
                                    secondary={
                                        <Typography variant="caption" color="text.secondary">
                                            IMEI
                                        </Typography>
                                    }
                                />
                            </ListItem>
                        </List>
                    </Grid2>
                    <Grid2 size={{xs: 12, md: 6}}>
                        <List disablePadding>
                            <ListItem                                 sx={{ 
                                    borderRight: { xs: 'none', md: `1px solid ${alpha(theme.palette.divider, 0.1)}` }
                                }}>
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <CalendarIcon color="action" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText 
                                    primary={
                                        <Typography variant="body1" fontWeight={500}>
                                            {formatDate(device.registered_at)}
                                        </Typography>
                                    }
                                    secondary={
                                        <Typography variant="caption" color="text.secondary">
                                            {t('device.fields.registeredAt')}
                                        </Typography>
                                    }
                                />
                            </ListItem>
                        </List>
                    </Grid2>
                    <Grid2 size={{xs: 12, md: 6}}>
                        <List disablePadding>
                            <ListItem>
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <PersonIcon color="action" fontSize="small" />
                                </ListItemIcon>
                                <ListItemText 
                                    primary={
                                        <Typography variant="body1" fontWeight={500}>
                                            {device.registered_by || '-'}
                                        </Typography>
                                    }
                                    secondary={
                                        <Typography variant="caption" color="text.secondary">
                                            {t('device.fields.registeredBy')}
                                        </Typography>
                                    }
                                />
                            </ListItem>
                        </List>
                    </Grid2>
                </Grid2>
            </Paper>
            
            <Paper
                elevation={0}
                variant="outlined"
                sx={{
                    borderRadius: 2,
                    mb: 3,
                    overflow: 'hidden',
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.05)}`
                }}
            >
                <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    bgcolor: alpha(theme.palette.success.main, 0.07),
                    p: 2,
                    pl: 2.5
                }}>
                    <Typography 
                        variant="subtitle1" 
                        sx={{ 
                            fontWeight: 600,
                            color: theme.palette.success.dark,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                        }}
                    >
                        <PersonIcon fontSize="small" />
                        {t('device.fields.operators')}
                    </Typography>
                    <Tooltip title={t('device.operators.assign')} arrow>
                        <IconButton 
                            aria-label={t('device.operators.assign')}
                            size="small"
                            color="success"
                            onClick={handleAssignOperators}
                            sx={{
                                bgcolor: alpha(theme.palette.success.main, 0.1),
                                border: `1px solid ${alpha(theme.palette.success.main, 0.2)}`,
                                '&:hover': {
                                    bgcolor: alpha(theme.palette.success.main, 0.2),
                                }
                            }}
                        >
                            <PersonAddIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                </Box>
                <Box sx={{ mb: 2, p: 2, pt: 1.5 }}>
                    {device.operators && device.operators.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {device.operators.map((operator) => (
                                <Chip
                                    key={operator}
                                    label={operator}
                                    size="medium"
                                    color="success"
                                    variant="outlined"
                                    onDelete={() => handleUnassignOperator(operator)}
                                    sx={{
                                        borderRadius: 1.5,
                                        transition: 'all 0.2s ease',
                                        '&:hover': {
                                            boxShadow: `0 2px 8px ${alpha(theme.palette.success.main, 0.2)}`,
                                            borderColor: theme.palette.success.main
                                        }
                                    }}
                                    deleteIcon={
                                        <DeleteIcon fontSize="small" />
                                    }
                                />
                            ))}
                        </Box>
                    ) : (
                        <Typography 
                            color="text.secondary"
                            sx={{
                                fontStyle: 'italic',
                                p: 1,
                                textAlign: 'center',
                                bgcolor: alpha(theme.palette.background.default, 0.5),
                                borderRadius: 1
                            }}
                        >
                            {t('device.operators.none')}
                        </Typography>
                    )}
                </Box>
            </Paper>
            
            <Paper
                elevation={0}
                variant="outlined"
                sx={{
                    borderRadius: 2,
                    overflow: 'hidden',
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.05)}`
                }}
            >
                <Box sx={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'space-between',
                    bgcolor: alpha(theme.palette.primary.main, 0.07),
                    p: 2,
                    pl: 2.5
                }}>
                    <Typography 
                        variant="subtitle1" 
                        sx={{ 
                            fontWeight: 600,
                            color: theme.palette.primary.dark,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                        }}
                    >
                        <InfoIcon fontSize="small" />
                        {t('device.fields.topics')}
                    </Typography>
                    <Tooltip title={t('device.createTopic')} arrow>
                        <IconButton
                            aria-label={t('device.createTopic')}
                            size="small"
                            color="primary"
                            onClick={handleCreateTopic}
                            sx={{
                                bgcolor: alpha(theme.palette.primary.main, 0.1),
                                border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
                                '&:hover': {
                                    bgcolor: alpha(theme.palette.primary.main, 0.2),
                                }
                            }}
                        >
                            <AddIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                </Box>
                
                <Box sx={{ p: 2, pt: 1.5 }}>
                    {device.topics && device.topics.length > 0 ? (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                            {device.topics.map((topic) => (
                                <Chip
                                    key={topic}
                                    label={topic}
                                    size="medium"
                                    onClick={() => handleTopicClick(topic)}
                                    onDelete={() => handleDeleteTopic(topic)}
                                    color="primary"
                                    deleteIcon={
                                        <DeleteIcon fontSize="small" />
                                    }
                                    sx={{
                                        borderRadius: 1.5,
                                        transition: 'all 0.2s ease',
                                        '&:hover': {
                                            boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.2)}`,
                                        }
                                    }}
                                />
                            ))}
                        </Box>
                    ) : (
                        <Typography 
                            color="text.secondary"
                            sx={{
                                fontStyle: 'italic',
                                p: 1,
                                textAlign: 'center',
                                bgcolor: alpha(theme.palette.background.default, 0.5),
                                borderRadius: 1
                            }}
                        >
                            {t('device.topics.none')}
                        </Typography>
                    )}
                    
                    {device.topics && device.topics.length > 0 && (
                        <Box sx={{ mt: 2 }}>
                            <Button
                                fullWidth
                                startIcon={<AddIcon />}
                                variant="outlined"
                                onClick={handleCreateTopic}
                                sx={{
                                    borderRadius: 2,
                                    fontWeight: 500,
                                    borderWidth: '1.5px',
                                    borderStyle: 'dashed',
                                    '&:hover': {
                                        borderWidth: '1.5px',
                                        boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.15)}`,
                                    },
                                    transition: 'all 0.2s ease'
                                }}
                            >
                                {t('device.createTopic')}
                            </Button>
                        </Box>
                    )}
                </Box>
            </Paper>
        </Box>
    );
}; 