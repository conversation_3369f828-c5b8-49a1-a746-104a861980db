import React, { useState, useEffect } from 'react';
import {
    Box,
    Typography,
    List,
    ListItem,
    Paper,
    Skeleton,
    Chip,
    alpha,
    useTheme,
    Divider,
    Alert
} from '@mui/material';
import { 
    Schedule as ScheduleIcon,
    Person as PersonIcon,
    CalendarToday as CalendarIcon,
    Topic as TopicIcon,
    Code as CodeIcon 
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { TopicInfo, getTopicInfo } from '../../../../services/topicService';

interface Props {
    topic: string;
    deviceSn: string;
    onClose: () => void;
}

export const TopicInfoDialog: React.FC<Props> = ({ topic, deviceSn }) => {
    const [topicInfo, setTopicInfo] = useState<TopicInfo | null>(null);
    const [loading, setLoading] = useState(true);
    const { showSnackbar } = useSnackbar();
    const { t } = useTranslation();
    const theme = useTheme();

    useEffect(() => {
        const fetchTopicInfo = async () => {
            try {
                const response = await getTopicInfo(deviceSn, topic);
                setTopicInfo(response.topic);
            } catch (error: any) {
                showSnackbar(t('device.messages.fetchTopicError', { error: error.response?.data?.error }), 'error');
            } finally {
                setLoading(false);
            }
        };
        fetchTopicInfo();
    }, [topic, deviceSn]);

    const formatDate = (dateString: string) => {
        try {
            return new Date(dateString).toLocaleString();
        } catch (e) {
            return '-';
        }
    };

    if (loading) {
        return (
            <Box p={3} sx={{ 
                minWidth: 450,
                display: 'flex',
                flexDirection: 'column',
                gap: 2
            }}>
                <Skeleton variant="rectangular" height={48} width="100%" sx={{ borderRadius: 1 }} />
                <Skeleton variant="rectangular" height={48} width="100%" sx={{ borderRadius: 1 }} />
                <Skeleton variant="rectangular" height={48} width="100%" sx={{ borderRadius: 1 }} />
                <Skeleton variant="rectangular" height={48} width="100%" sx={{ borderRadius: 1 }} />
                <Skeleton variant="rectangular" height={48} width="100%" sx={{ borderRadius: 1 }} />
            </Box>
        );
    }

    if (!topicInfo) {
        return (
            <Box p={3}>
                <Alert 
                    severity="error" 
                    sx={{ 
                        borderRadius: 2,
                        display: 'flex',
                        alignItems: 'center'
                    }}
                >
                    <Typography>{t('device.topics.loadError')}</Typography>
                </Alert>
            </Box>
        );
    }

    // 生成主题路径的彩色显示
    const renderTopicPath = (topic: string) => {
        const segments = topic.split('/');
        return (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, alignItems: 'center' }}>
                {segments.map((segment, index) => (
                    <React.Fragment key={index}>
                        {index > 0 && (
                            <Typography color="text.secondary" sx={{ mx: 0.5 }}>/</Typography>
                        )}
                        <Chip 
                            label={segment}
                            size="small"
                            color={index === segments.length - 1 ? "primary" : "default"}
                            variant={index === segments.length - 1 ? "filled" : "outlined"}
                            sx={{ 
                                borderRadius: 1, 
                                height: 24,
                                fontSize: '0.75rem',
                            }}
                        />
                    </React.Fragment>
                ))}
            </Box>
        );
    };

    const getDataTypeChip = (dataType: string) => {
        const colorMap: Record<string, any> = {
            'json': { color: 'success', icon: <CodeIcon fontSize="small" /> },
            'string': { color: 'primary', icon: <CodeIcon fontSize="small" /> },
            'binary': { color: 'secondary', icon: <CodeIcon fontSize="small" /> },
            'number': { color: 'info', icon: <CodeIcon fontSize="small" /> },
            'default': { color: 'default', icon: <CodeIcon fontSize="small" /> }
        };
        
        const type = dataType?.toLowerCase() || 'default';
        const chipProps = colorMap[type] || colorMap['default'];
        
        return (
            <Chip 
                icon={chipProps.icon}
                label={dataType || 'unknown'}
                color={chipProps.color}
                size="small"
                sx={{ 
                    borderRadius: 1, 
                    fontWeight: 500,
                    fontSize: '0.8rem'
                }}
            />
        );
    };

    return (
        <Box sx={{ 
            minWidth: 450,
            maxWidth: 600
        }}>
            <Paper 
                elevation={0}
                sx={{ 
                    overflow: 'hidden',
                    borderRadius: 3,
                    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                    boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.08)}`,
                }}
            >
                <Box sx={{ 
                    p: 2, 
                    backgroundColor: alpha(theme.palette.primary.main, 0.08),
                    borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                }}>
                    <Typography 
                        variant="subtitle1" 
                        sx={{ 
                            fontWeight: 600, 
                            color: theme.palette.primary.main,
                            mb: 1,
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1
                        }}
                    >
                        <TopicIcon fontSize="small" />
                        {t('device.topics.details')}
                    </Typography>
                    
                    <Box sx={{ mt: 1 }}>
                        {renderTopicPath(topicInfo.topic)}
                    </Box>
                </Box>
                
                <List disablePadding sx={{ 
                    '& .MuiListItem-root': {
                        py: 2,
                        px: 2.5
                    }
                }}>
                    <ListItem>
                        <Box sx={{ 
                            display: 'flex', 
                            width: '100%', 
                            alignItems: 'flex-start',
                            gap: 2
                        }}>
                            <Box sx={{ 
                                minWidth: 36, 
                                display: 'flex', 
                                justifyContent: 'center',
                                pt: 0.5
                            }}>
                                <CodeIcon color="action" fontSize="small" />
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                                <Typography 
                                    variant="caption" 
                                    color="text.secondary"
                                    sx={{ 
                                        display: 'block', 
                                        mb: 0.5,
                                        fontWeight: 500,
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.5px'
                                    }}
                                >
                                    {t('common.fields.dataType')}
                                </Typography>
                                {getDataTypeChip(topicInfo.data_type)}
                            </Box>
                        </Box>
                    </ListItem>
                    
                    <Divider component="li" sx={{ ml: 7 }} />
                    
                    <ListItem>
                        <Box sx={{ 
                            display: 'flex', 
                            width: '100%', 
                            alignItems: 'flex-start',
                            gap: 2
                        }}>
                            <Box sx={{ 
                                minWidth: 36, 
                                display: 'flex', 
                                justifyContent: 'center',
                                pt: 0.5
                            }}>
                                <ScheduleIcon color="action" fontSize="small" />
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                                <Typography 
                                    variant="caption" 
                                    color="text.secondary"
                                    sx={{ 
                                        display: 'block', 
                                        mb: 0.5,
                                        fontWeight: 500,
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.5px'
                                    }}
                                >
                                    {t('common.fields.lastUpdated')}
                                </Typography>
                                <Typography variant="body1" color="text.primary" fontWeight={500}>
                                    {formatDate(topicInfo.last_updated)}
                                </Typography>
                            </Box>
                        </Box>
                    </ListItem>
                    
                    <Divider component="li" sx={{ ml: 7 }} />
                    
                    <ListItem>
                        <Box sx={{ 
                            display: 'flex', 
                            width: '100%', 
                            alignItems: 'flex-start',
                            gap: 2
                        }}>
                            <Box sx={{ 
                                minWidth: 36, 
                                display: 'flex', 
                                justifyContent: 'center',
                                pt: 0.5
                            }}>
                                <PersonIcon color="action" fontSize="small" />
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                                <Typography 
                                    variant="caption" 
                                    color="text.secondary"
                                    sx={{ 
                                        display: 'block', 
                                        mb: 0.5,
                                        fontWeight: 500,
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.5px'
                                    }}
                                >
                                    {t('common.fields.createdBy')}
                                </Typography>
                                <Typography variant="body1" color="text.primary" fontWeight={500}>
                                    {topicInfo.created_by || '-'}
                                </Typography>
                            </Box>
                        </Box>
                    </ListItem>
                    
                    <Divider component="li" sx={{ ml: 7 }} />
                    
                    <ListItem>
                        <Box sx={{ 
                            display: 'flex', 
                            width: '100%', 
                            alignItems: 'flex-start',
                            gap: 2
                        }}>
                            <Box sx={{ 
                                minWidth: 36, 
                                display: 'flex', 
                                justifyContent: 'center',
                                pt: 0.5
                            }}>
                                <CalendarIcon color="action" fontSize="small" />
                            </Box>
                            <Box sx={{ flexGrow: 1 }}>
                                <Typography 
                                    variant="caption" 
                                    color="text.secondary"
                                    sx={{ 
                                        display: 'block', 
                                        mb: 0.5,
                                        fontWeight: 500,
                                        textTransform: 'uppercase',
                                        letterSpacing: '0.5px'
                                    }}
                                >
                                    {t('common.fields.createdAt')}
                                </Typography>
                                <Typography variant="body1" color="text.primary" fontWeight={500}>
                                    {formatDate(topicInfo.created_at)}
                                </Typography>
                            </Box>
                        </Box>
                    </ListItem>
                </List>
            </Paper>
        </Box>
    );
}; 