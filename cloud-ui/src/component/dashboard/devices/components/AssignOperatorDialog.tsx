import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    Typo<PERSON>,
    <PERSON>ert,
    TextField,
    Button,
    Stack,
    List,
    ListItem,
    ListItemText,
    ListItemButton,
    Checkbox,
    Paper,
    Divider,
    alpha,
    useTheme,
    CircularProgress,
    Avatar,
    InputAdornment,
    IconButton
} from '@mui/material';
import { 
    Add as AddIcon,
    Search as SearchIcon,
    Clear as ClearIcon,
    PersonAdd as PersonAddIcon 
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { Device } from '../../../../services/deviceService';
import { Operator, getOperators, assignOperators } from '../../../../services/operatorService';

interface Props {
    device: Device;
    onClose: () => void;
    onSuccess: () => void;
}

export const AssignOperatorDialog: React.FC<Props> = ({ device, onClose, onSuccess }) => {
    const [operators, setOperators] = useState<Operator[]>([]);
    const [selectedOperators, setSelectedOperators] = useState<string[]>(device.operators || []);
    const [loading, setLoading] = useState(true);
    const [searchQuery, setSearchQuery] = useState('');
    const [submitting, setSubmitting] = useState(false);
    const { showSnackbar } = useSnackbar();
    const { t } = useTranslation();
    const theme = useTheme();

    const filteredOperators = operators.filter(op => 
        op.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        op.username.toLowerCase().includes(searchQuery.toLowerCase()) ||
        op.email.toLowerCase().includes(searchQuery.toLowerCase())
    );

    useEffect(() => {
        const fetchOperators = async () => {
            try {
                const response = await getOperators();
                setOperators(response.operators || []);
            } catch (error: any) {
                showSnackbar(t('device.messages.fetchOperatorError', { error: error.response?.data?.error }), 'error');
            } finally {
                setLoading(false);
            }
        };
        fetchOperators();
    }, []);

    const handleAssign = async () => {
        setSubmitting(true);
        try {
            await assignOperators({
                device_sn: device.device_sn,
                operator_id: selectedOperators
            });
            showSnackbar(t('device.messages.operatorAssignSuccess'), 'success');
            onSuccess();
            onClose();
        } catch (error: any) {
            showSnackbar(t('device.messages.operatorAssignError', { error: error.response?.data?.error }), 'error');
        } finally {
            setSubmitting(false);
        }
    };

    const handleToggleOperator = (username: string) => {
        setSelectedOperators(prev => 
            prev.includes(username)
                ? prev.filter(u => u !== username)
                : [...prev, username]
        );
    };

    const handleClearSearch = () => {
        setSearchQuery('');
    };

    if (loading) {
        return (
            <Box sx={{ 
                p: 4, 
                display: 'flex', 
                justifyContent: 'center', 
                alignItems: 'center', 
                flexDirection: 'column',
                gap: 2
            }}>
                <CircularProgress color="primary" size={40} thickness={4} />
                <Typography variant="body1" color="text.secondary">
                    {t('common.loading')}
                </Typography>
            </Box>
        );
    }

    return (
        <Box sx={{ p: 2, width: '100%', maxWidth: 600 }}>
            <Alert 
                severity="info" 
                icon={<PersonAddIcon />}
                sx={{ 
                    mb: 3,
                    borderRadius: 2,
                    boxShadow: `0 2px 8px ${alpha(theme.palette.info.main, 0.15)}`,
                    '& .MuiAlert-icon': {
                        color: theme.palette.info.main
                    }
                }}
            >
                {t('device.operators.selectHint', { name: device.name || device.device_sn })}
            </Alert>
            
            <TextField
                fullWidth
                size="small"
                placeholder={t('common.search')}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                sx={{ 
                    mb: 2,
                    '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.2s ease',
                        '&:hover': {
                            boxShadow: `0 0 0 1px ${alpha(theme.palette.primary.main, 0.2)}`
                        },
                        '&.Mui-focused': {
                            boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.2)}`
                        }
                    }
                }}
                InputProps={{
                    startAdornment: (
                        <InputAdornment position="start">
                            <SearchIcon color="action" fontSize="small" />
                        </InputAdornment>
                    ),
                    endAdornment: searchQuery ? (
                        <InputAdornment position="end">
                            <IconButton
                                aria-label="clear search"
                                onClick={handleClearSearch}
                                edge="end"
                                size="small"
                            >
                                <ClearIcon fontSize="small" />
                            </IconButton>
                        </InputAdornment>
                    ) : null
                }}
            />

            <Paper 
                elevation={0}
                variant="outlined"
                sx={{ 
                    maxHeight: '350px', 
                    overflowY: 'auto',
                    border: '1px solid',
                    borderColor: 'divider',
                    borderRadius: 2,
                    mb: 3,
                    boxShadow: `inset 0 0 10px ${alpha(theme.palette.common.black, 0.03)}`
                }}
            >
                <List dense>
                    {filteredOperators.map((operator, index) => (
                        <React.Fragment key={operator.username}>
                            <ListItem
                                disablePadding
                                secondaryAction={
                                    <Checkbox
                                        edge="end"
                                        checked={selectedOperators.includes(operator.username)}
                                        onChange={() => handleToggleOperator(operator.username)}
                                        sx={{
                                            color: alpha(theme.palette.primary.main, 0.6),
                                            '&.Mui-checked': {
                                                color: theme.palette.primary.main,
                                            },
                                            '& .MuiSvgIcon-root': {
                                                fontSize: 22,
                                            },
                                        }}
                                    />
                                }
                            >
                                <ListItemButton 
                                    onClick={() => handleToggleOperator(operator.username)}
                                    sx={{
                                        borderRadius: 1,
                                        mx: 0.5,
                                        '&:hover': {
                                            backgroundColor: alpha(theme.palette.primary.main, 0.08),
                                        },
                                    }}
                                >
                                    <Avatar 
                                        sx={{ 
                                            width: 32, 
                                            height: 32, 
                                            mr: 1.5,
                                            bgcolor: selectedOperators.includes(operator.username) 
                                                ? theme.palette.primary.main
                                                : alpha(theme.palette.primary.light, 0.4),
                                            fontSize: '0.875rem',
                                            transition: 'all 0.2s ease'
                                        }}
                                    >
                                        {operator.name.charAt(0).toUpperCase()}
                                    </Avatar>
                                    <ListItemText
                                        primary={
                                            <Typography 
                                                variant="body1" 
                                                sx={{ 
                                                    fontWeight: 600,
                                                    color: selectedOperators.includes(operator.username)
                                                        ? theme.palette.primary.main
                                                        : theme.palette.text.primary
                                                }}
                                            >
                                                {operator.name}
                                            </Typography>
                                        }
                                        secondary={
                                            <Typography variant="body2" color="text.secondary" sx={{ fontSize: '0.75rem' }}>
                                                {operator.username} • {operator.email}
                                            </Typography>
                                        }
                                    />
                                </ListItemButton>
                            </ListItem>
                            {index < filteredOperators.length - 1 && (
                                <Divider variant="inset" component="li" sx={{ ml: 7 }} />
                            )}
                        </React.Fragment>
                    ))}
                    {filteredOperators.length === 0 && (
                        <ListItem sx={{ py: 4 }}>
                            <ListItemText 
                                primary={t('device.operators.noResults')}
                                sx={{ textAlign: 'center', color: 'text.secondary' }}
                            />
                        </ListItem>
                    )}
                </List>
            </Paper>

            <Box sx={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                borderTop: `1px solid ${alpha(theme.palette.divider, 0.3)}`,
                pt: 2
            }}>
                <Typography 
                    variant="body2" 
                    sx={{
                        color: theme.palette.text.secondary,
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                    }}
                >
                    <Box 
                        component="span" 
                        sx={{ 
                            px: 1, 
                            py: 0.5, 
                            bgcolor: alpha(theme.palette.primary.main, 0.1),
                            borderRadius: 1,
                            color: theme.palette.primary.main,
                            fontWeight: 600
                        }}
                    >
                        {selectedOperators.length}
                    </Box>
                    {t('device.operators.selected', { count: selectedOperators.length })}
                </Typography>
                <Stack direction="row" spacing={2}>
                    <Button 
                        onClick={onClose}
                        sx={{
                            borderRadius: 2,
                            px: 2,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                backgroundColor: alpha(theme.palette.action.active, 0.05)
                            }
                        }}
                    >
                        {t('common.cancel')}
                    </Button>
                    <Button
                        variant="contained"
                        onClick={handleAssign}
                        startIcon={submitting ? (
                            <CircularProgress size={16} color="inherit" />
                        ) : (
                            <AddIcon />
                        )}
                        disabled={submitting}
                        sx={{
                            borderRadius: 2,
                            px: 2.5,
                            boxShadow: `0 2px 8px ${alpha(theme.palette.primary.main, 0.25)}`,
                            transition: 'all 0.2s ease',
                            '&:hover': {
                                boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.35)}`,
                                transform: 'translateY(-2px)'
                            }
                        }}
                    >
                        {t('device.operators.assign')}
                    </Button>
                </Stack>
            </Box>
        </Box>
    );
}; 