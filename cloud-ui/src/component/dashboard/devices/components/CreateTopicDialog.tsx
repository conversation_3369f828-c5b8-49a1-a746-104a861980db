import React, { useEffect, useState } from 'react';
import {
    <PERSON>,
    <PERSON>po<PERSON>,
    Alert,
    TextField,
    Button,
    Stack,
    MenuItem,
    Autocomplete,
    Divider,
    CircularProgress,
    alpha,
    useTheme,
    Paper,
    InputAdornment,
} from '@mui/material';
import { useSnackbar } from '../../../../context/SnackbarContext';
import { 
    Add as AddIcon,
    Topic as TopicIcon,
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { Device, getDevices } from '../../../../services/deviceService';
import { DATA_TYPES, TopicCreateParams, createTopic } from '../../../../services/topicService';
import { getUserInfoFromStorage } from '../../../../context/UserContext';

export const CreateTopicDialog: React.FC<{
    onClose: () => void; 
    onSuccess: () => void;
    preSelectedDevice?: Device;
}> = ({ onClose, onSuccess, preSelectedDevice }) => {
    const [devices, setDevices] = useState<Device[]>([]);
    const [selectedDevice, setSelectedDevice] = useState<Device | null>(preSelectedDevice || null);
    const [inputValue, setInputValue] = useState('');
    const [topicName, setTopicName] = useState('');
    const [dataType, setDataType] = useState('number');
    const { showSnackbar } = useSnackbar();
    const [creating, setCreating] = useState(false);
    const [loading, setLoading] = useState(true);
    const enterpriseID = getUserInfoFromStorage().enterprise_id;
    const { t } = useTranslation();
    const theme = useTheme();

    useEffect(() => {
        const fetchDevices = async () => {
            setLoading(true);
            try {
                const response = await getDevices({});
                setDevices(response.devices || []);
            } catch (error: any) {
                showSnackbar(t('device.messages.fetchError', { error: error.response?.data?.error }), 'error');
            } finally {
                setLoading(false);
            }
        };
        fetchDevices();
    }, []);

    useEffect(() => {
        if (preSelectedDevice) {
            setSelectedDevice(preSelectedDevice);
        }
    }, [preSelectedDevice]);

    const handleCreate = async () => {
        if (!topicName.trim()) {
            showSnackbar(t('device.topics.create.nameRequired'), 'warning');
            return;
        }

        if (!selectedDevice) {
            showSnackbar(t('device.topics.create.deviceRequired'), 'warning');
            return;
        }

        if (!/^[a-zA-Z0-9/]+$/.test(topicName)) {
            showSnackbar(t('device.topics.create.invalidName'), 'warning');
            return;
        }

        setCreating(true);
        try {
            const topicParams: TopicCreateParams = {
                topic: topicName.trim(),
                data_type: dataType,
                user_name: getUserInfoFromStorage().name || '',
                device_sn: selectedDevice?.device_sn,
                client_id: enterpriseID || ''
            };
            
            await createTopic(topicParams);
            showSnackbar(t('device.messages.topicCreateSuccess'), 'success');
            onSuccess();
            onClose();
        } catch (error: any) {
            showSnackbar(t('device.messages.topicCreateError', { error: error.response?.data?.error }), 'error');
        } finally {
            setCreating(false);
        }
    };

    return (
        <Box p={3} sx={{ minWidth: { xs: '300px', sm: '450px' } }}>
            <Alert 
                severity="info" 
                sx={{ 
                    mb: 3,
                    '& .MuiAlert-message': { width: '100%' },
                    backgroundColor: alpha(theme.palette.info.main, 0.1)
                }}
                icon={<TopicIcon />}
            >
                <Typography variant="body2" gutterBottom>
                    {t('device.topics.create.prefixInfo', { deviceSn: selectedDevice?.device_sn || 'device_sn' })}
                </Typography>
                <Typography variant="body2">
                    {t('device.topics.create.formatInfo')}
                </Typography>
            </Alert>

            {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', my: 3 }}>
                    <CircularProgress size={30} />
                </Box>
            ) : (
                <Autocomplete
                    value={selectedDevice}
                    onChange={(_, newValue: Device | null) => {
                        setSelectedDevice(newValue);
                    }}
                    inputValue={inputValue}
                    onInputChange={(_, newInputValue) => {
                        setInputValue(newInputValue);
                    }}
                    options={devices}
                    getOptionLabel={(option) => `${option.name || t('device.unnamedDevice')} (${option.device_sn})`}
                    renderInput={(params) => (
                        <TextField 
                            {...params} 
                            label={t('device.topics.create.selectDevice')}
                            fullWidth
                            variant="outlined"
                        />
                    )}
                    sx={{ mb: 3 }}
                    noOptionsText={t('device.noDevices')}
                    disabled={!!preSelectedDevice}
                />
            )}

            <Box sx={{ position: 'relative', mb: 3 }}>
                <TextField
                    fullWidth
                    label={t('device.topics.create.name')}
                    value={topicName}
                    onChange={(e) => setTopicName(e.target.value)}
                    placeholder={t('device.topics.create.placeholder')}
                    error={topicName !== '' && !/^[a-zA-Z0-9/]+$/.test(topicName)}
                    InputProps={{
                        startAdornment: selectedDevice && (
                            <InputAdornment position="start" sx={{ color: 'text.secondary' }}>
                                <Typography variant="body2" sx={{ fontFamily: 'monospace' }}>
                                    {selectedDevice.device_sn}/
                                </Typography>
                            </InputAdornment>
                        ),
                    }}
                />
                
                <Paper
                    variant="outlined"
                    sx={{
                        mt: 1,
                        p: 1.5,
                        borderColor: theme.palette.divider,
                        backgroundColor: alpha(theme.palette.background.default, 0.6),
                    }}
                >
                    <Typography variant="caption" display="block" sx={{ color: 'text.secondary' }}>
                        {t('device.topics.create.finalTopic', {
                            deviceSn: selectedDevice?.device_sn || 'device_sn',
                            topic: topicName || 'topicName'
                        })}
                    </Typography>
                </Paper>
            </Box>
            
            <TextField
                select
                fullWidth
                label={t('common.fields.dataType')}
                value={dataType}
                onChange={(e) => setDataType(e.target.value)}
                sx={{ mb: 4 }}
            >
                {DATA_TYPES.map(({value, label}) => (
                    <MenuItem key={value} value={value}>{label}</MenuItem>
                ))}
            </TextField>
            
            <Divider sx={{ mb: 3 }} />
            
            <Stack direction="row" spacing={2} justifyContent="flex-end">
                <Button 
                    onClick={onClose}
                    variant="outlined"
                    color="inherit"
                >
                    {t('common.cancel')}
                </Button>
                <Button
                    variant="contained"
                    onClick={handleCreate}
                    disabled={creating || !topicName.trim() || !selectedDevice || !/^[a-zA-Z0-9/]+$/.test(topicName)}
                    startIcon={creating ? <CircularProgress size={20} /> : <AddIcon />}
                >
                    {creating ? t('common.loading') : t('common.create')}
                </Button>
            </Stack>
        </Box>
    );
};

export default CreateTopicDialog;
