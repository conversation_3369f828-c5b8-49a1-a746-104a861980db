import React, { useEffect, useRef, useState } from 'react';
import { Box, Typography, CircularProgress, Paper, FormControlLabel, Switch } from '@mui/material';
import Autocomplete from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { 
  Device, 
  DeviceTracksResponse, 
  getDevices, 
  getDeviceTracks 
} from '../../../services/deviceService';
import { useSnackbar } from '../../../context/SnackbarContext';
import { useTranslation } from 'react-i18next';

declare global {
  interface Window {
    AMap: any;
    _AMapSecurityConfig: {
      securityJsCode: string;
    };
  }
}

// 轨迹线路样式配置
const trackColors = [
  '#409EFF', // 蓝色
  '#67C23A', // 绿色
  '#E6A23C', // 橙色
  '#F56C6C', // 红色
  '#9C27B0', // 紫色
  '#795548', // 棕色
  '#607D8B', // 蓝灰色
  '#FF9800', // 橙色
  '#009688', // 青色
  '#3F51B5', // 靛蓝色
];

// 根据设备ID获取颜色
const getDeviceColor = (deviceSN: string) => {
  let hash = 0;
  for (let i = 0; i < deviceSN.length; i++) {
    hash = deviceSN.charCodeAt(i) + ((hash << 5) - hash);
  }
  return trackColors[Math.abs(hash) % trackColors.length];
};

const getTrackLineStyle = (deviceSN: string) => ({
  strokeColor: getDeviceColor(deviceSN),
  strokeWeight: 6,
  strokeOpacity: 0.8,
  strokeStyle: 'solid',
  lineJoin: 'round',
  lineCap: 'round',
  showDir: true,
  dirColor: getDeviceColor(deviceSN),
  borderWeight: 2,
  outlineColor: '#ffffff',
  isOutline: true,
  zIndex: 50
});

// 轨迹起点和终点样式
const trackPointStyle = {
  start: {
    color: '#67C23A', // 绿色
    size: 12,
    borderColor: '#ffffff',
    borderWidth: 2
  },
  end: {
    color: '#F56C6C', // 红色
    size: 12,
    borderColor: '#ffffff',
    borderWidth: 2
  }
};

const DeviceMap: React.FC = () => {
  const { t } = useTranslation();
  const mapContainer = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<any>(null);
  const [devices, setDevices] = useState<Device[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [showTrack, setShowTrack] = useState<boolean>(false);
  const [deviceTracks, setDeviceTracks] = useState<DeviceTracksResponse>({});
  const [trackPolylines, setTrackPolylines] = useState<any[]>([]);
  const [loadingTracks, setLoadingTracks] = useState<boolean>(false);
  const { showSnackbar } = useSnackbar();
  
  // Default center coordinates
  const defaultCenter = [104.100959, 30.542196];
  
  const fetchDevices = async () => {
    try {
      const response = await getDevices({
        mode: 'map'
      });
      setDevices(response.devices || [])
      return response.devices || [];
    } catch (error: any) {
      showSnackbar(t("device.messages.fetchError", { error: error.response?.data?.error }),'error');
      return []
    }
  };

  // 获取指定设备的轨迹数据
  const fetchDeviceTrack = async (deviceSN: string) => {
    try {
      setLoadingTracks(true);
      // console.log('Fetching tracks for device:', deviceSN, 'enterprise:', enterpriseId);
      
      const response = await getDeviceTracks(deviceSN);
      // console.log('Track response:', response);
      
      if (response && response[deviceSN.toLowerCase()] && Array.isArray(response[deviceSN.toLowerCase()])) {
        const deviceTrack = response[deviceSN.toLowerCase()];
        // 确保轨迹点数据格式正确
        const validTrackPoints = deviceTrack.filter(point => 
          point && 
          typeof point.latitude === 'number' && 
          typeof point.longitude === 'number' &&
          !isNaN(point.latitude) && 
          !isNaN(point.longitude)
        );
        
        if (validTrackPoints.length === 0) {
          showSnackbar(t('device.map.invalidTrackData'), 'warning');
          return [];
        }
        
        // console.log('Valid track points:', validTrackPoints);
        setDeviceTracks({ [deviceSN]: validTrackPoints });
        return validTrackPoints;
      }
      return [];
    } catch (error: any) {
      console.error('failed to get device tracks:', error);
      showSnackbar(t("device.messages.fetchError", { error: error.response?.data?.error }), 'error');
      return [];
    } finally {
      setLoadingTracks(false);
    }
  };

  useEffect(() => {
    window._AMapSecurityConfig = {
      securityJsCode: '79af5828ca8c82ab83fa681f6677b4ef',
    };

    const script = document.createElement('script');
    script.src = `https://webapi.amap.com/maps?v=2.0&key=7efabeda1ac0d1067a6e2bdb3146261d`;
    script.async = true;
    script.onload = () => {
      initializeMap();
    };
    document.head.appendChild(script);

    return () => {
      if (script.parentNode) {
        document.head.removeChild(script);
      }
      if (map) {
        map.destroy();
      }
    };
  }, []);

  const initializeMap = async () => {
    if (!mapContainer.current || !window.AMap) return;
    
    try {
      const deviceList = await fetchDevices();
      const newMap = new window.AMap.Map(mapContainer.current, {
        resizeEnable: true,
        viewMode: '2D',
        center: deviceList.length > 0 ? [deviceList[0].longitude, deviceList[0].latitude] : defaultCenter,
        zoom: 12,
      });

      setMap(newMap);
      if (deviceList.length > 0) {
        addDeviceMarkers(newMap, deviceList);
      }
    } catch (error) {
      console.error('Error initializing map:', error);
    }
  };

  const addDeviceMarkers = (mapInstance: any, deviceList: Device[]) => {
    if (!deviceList.length) return;
    
    deviceList.forEach(device => {
      const marker = new window.AMap.Marker({
        position: [device.longitude, device.latitude],
        icon: new window.AMap.Icon({
          image: '/gw1-5.png',
          size: new window.AMap.Size(32, 32),
          imageSize: new window.AMap.Size(32, 32)
        }),
        anchor: 'bottom-center',
        offset: new window.AMap.Pixel(0, 0),
        title: device.name,
      });

      const content = `
        <div style="padding: 10px; background-color: white; color: #333; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); font-family: Arial, sans-serif; max-width: 300px; position: relative;">
          <div style="position: absolute; top: 10px; right: 10px; cursor: pointer; width: 20px; height: 20px; background-color: #f0f0f0; border-radius: 50%; display: flex; justify-content: center; align-items: center;" class="info-window-close">
            <span style="color: #666; font-size: 14px; line-height: 1; font-weight: bold;">×</span>
          </div>
          <div style="border-bottom: 1px solid #eee; padding-bottom: 10px; margin-bottom: 10px; padding-right: 20px;">
            <h3 style="margin: 0; color: #1976d2; font-size: 16px; font-weight: 600;">${device.name}</h3>
          </div>
          <div style="display: grid; grid-template-columns: auto 1fr; grid-gap: 8px; align-items: center;font-size: 14px;">
            <span style="color: #666; font-weight: 500;">IMEI:</span>
            <span style="color: #333; font-weight: 400;">${device.imei_code}</span>
            
            <span style="color: #666; font-weight: 500;">${t('device.fields.type')}:</span>
            <span style="color: #333; font-weight: 400;">${device.type}</span>
            
            <span style="color: #666; font-weight: 500;">${t('device.map.trackInfo.lastUpdate')}:</span>
            <span style="color: #333; font-weight: 400;">${new Date(device.last_location_update).toLocaleString()}</span>
          </div>
          <div style="margin-top: 12px; text-align: left; font-size: 12px; color: #888;">
            ${t('device.map.trackInfo.longitude')}: ${device.longitude.toFixed(6)} | ${t('device.map.trackInfo.latitude')}: ${device.latitude.toFixed(6)}
          </div>
        </div>
      `;

      const infoWindow = new window.AMap.InfoWindow({
        content,
        offset: new window.AMap.Pixel(0, -20),
        isCustom: true,
        autoMove: true,
        closeWhenClickMap: true
      });

      marker.on('click', () => {
        infoWindow.open(mapInstance, marker.getPosition());
        
        setTimeout(() => {
          const closeButton = document.querySelector('.info-window-close');
          if (closeButton) {
            closeButton.addEventListener('click', () => {
              infoWindow.close();
            });
          }
        }, 100);
      });

      mapInstance.add(marker);

      const text = new window.AMap.Text({
        text: device.name,
        anchor: 'center',
        draggable: false,
        cursor: 'pointer',
        angle: 0,
        style: {
          'margin-bottom': '.5rem',
          'border-radius': '.25rem',
          'background-color': 'white',
          'width': '5rem',
          'border-width': 0,
          'box-shadow': '0 2px 6px 0 rgba(114, 124, 245, .5)',
          'text-align': 'center',
          'font-size': '10px',
          'color': 'blue',
        },
        position: [device.longitude, device.latitude],
        offset: new window.AMap.Pixel(0, -40),
      });
      text.setMap(mapInstance);
    });
  };

  // 处理设备选择变化
  const handleDeviceChange = async (device: Device | null) => {
    setSelectedDevice(device);
    if (device) {
      map.setCenter([device.longitude, device.latitude]);
      map.setZoom(14);
      if (showTrack) {
        await handleShowTrackChange(true, device.device_sn);
      }
    } else {
      handleShowTrackChange(false);
    }
  };

  // 处理轨迹显示/隐藏
  const handleShowTrackChange = async (show: boolean, deviceSN?: string) => {
    setShowTrack(show);
    
    if (show && (deviceSN || selectedDevice?.device_sn)) {
      const targetDeviceSN = deviceSN || selectedDevice?.device_sn;
      if (!targetDeviceSN) return;

      const trackPoints = await fetchDeviceTrack(targetDeviceSN);
      if (trackPoints && trackPoints.length > 0) {
        showDeviceTrack(trackPoints);
      } else {
        showSnackbar(t('device.map.noTracksData'), 'warning');
        setShowTrack(false);
      }
    } else {
      hideDeviceTracks();
    }
  };

  // 显示设备轨迹
  const showDeviceTrack = (trackPoints: any[]) => {
    if (!map || !selectedDevice || !trackPoints.length) {
      console.log('Cannot show track:', {
        hasMap: !!map,
        hasDevice: !!selectedDevice,
        pointsCount: trackPoints.length
      });
      return;
    }

    // 清除现有轨迹
    hideDeviceTracks();

    try {
      // console.log('Processing track points:', trackPoints);
      const path = trackPoints.map(point => {
        const lng = Number(point.longitude);
        const lat = Number(point.latitude);
        if (isNaN(lng) || isNaN(lat)) {
          console.error('Invalid coordinates:', point);
          return null;
        }
        return [lng, lat];
      }).filter(point => point !== null);

      if (path.length < 2) {
        console.error('Not enough valid points to draw track');
        showSnackbar(t('device.map.insufficientPoints'), 'warning');
        return;
      }

      // console.log('Creating track with path:', path);
      const trackLineStyle = getTrackLineStyle(selectedDevice.device_sn);

      // 创建轨迹折线
      const polyline = new window.AMap.Polyline({
        path,
        ...trackLineStyle,
        map: map // 直接设置map属性
      });

      // 创建起点和终点标记
      const startMarker = new window.AMap.CircleMarker({
        center: path[0],
        radius: trackPointStyle.start.size / 2,
        fillColor: trackPointStyle.start.color,
        strokeColor: trackPointStyle.start.borderColor,
        strokeWeight: trackPointStyle.start.borderWidth,
        fillOpacity: 1,
        zIndex: 100,
        bubble: true,
        map: map
      });

      const endMarker = new window.AMap.CircleMarker({
        center: path[path.length - 1],
        radius: trackPointStyle.end.size / 2,
        fillColor: trackPointStyle.end.color,
        strokeColor: trackPointStyle.end.borderColor,
        strokeWeight: trackPointStyle.end.borderWidth,
        fillOpacity: 1,
        zIndex: 100,
        bubble: true,
        map: map
      });

      setTrackPolylines([polyline, startMarker, endMarker]);

      // 添加起点和终点的信息窗体
      const createInfoWindow = (position: any, isStart: boolean) => {
        const point = trackPoints[isStart ? 0 : trackPoints.length - 1];
        const content = `
          <div style="font-weight: bold; margin-bottom: 8px; color: ${isStart ? '#67C23A' : '#F56C6C'}; display: flex; align-items: center;">
            <span style="margin-right: 5px;">${isStart ? '📍' : '🏁'} ${selectedDevice.name}</span>
            ${isStart ? t('device.map.trackInfo.start') : t('device.map.trackInfo.end')}
          </div>
          <div style="display: flex; flex-direction: column; gap: 4px;">
            <div style="font-size: 13px; color: #606266;">
              <span style="font-weight: 500;">time:</span>${new Date(point.ts).toLocaleString()}
            </div>
            <div style="font-size: 13px; color: #606266;"><span style="font-weight: 500;">${t('device.map.trackInfo.longitude')}:</span>${point.longitude.toFixed(6)}</div>
            <div style="font-size: 13px; color: #606266;"><span style="font-weight: 500;">${t('device.map.trackInfo.latitude')}:</span>${point.latitude.toFixed(6)}</div>
          </div>
        `;
        
        return new window.AMap.InfoWindow({
          content,
          position,
          offset: new window.AMap.Pixel(0, -15)
        });
      };

      const startInfo = createInfoWindow(path[0], true);
      const endInfo = createInfoWindow(path[path.length - 1], false);

      startMarker.on('click', () => startInfo.open(map));
      endMarker.on('click', () => endInfo.open(map));

      // 调整视野以包含整个轨迹
      map.setFitView([polyline, startMarker, endMarker]);
      
    } catch (error) {
      console.error('Error showing device track:', error);
      showSnackbar(t('device.map.trackDisplayError'), 'error');
    }
  };
  
  // 隐藏设备轨迹
  const hideDeviceTracks = () => {
    trackPolylines.forEach(polyline => {
      polyline.setMap(null);
    });
    setTrackPolylines([]);
  };

  // 更新设备列表和地图
  useEffect(() => {
    if (map) {
      map.clearMap();
      if (devices.length > 0) {
        addDeviceMarkers(map, devices);
      }
      
      if (showTrack && selectedDevice) {
        const tracks = deviceTracks[selectedDevice.device_sn];
        if (tracks?.length > 0) {
          showDeviceTrack(tracks);
        }
      }
    }
  }, [devices]);

  return (
    <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
      <Paper 
        elevation={3} 
        sx={{ 
          position: 'absolute', 
          top: 16, 
          left: 16, 
          zIndex: 10, 
          p: 2,
          borderRadius: 2,
          display: 'flex',
          flexDirection: { xs: 'column', md: 'row' },
          gap: 2
        }}
      >
        <Autocomplete
          sx={{ width: 300 }}
          size="small"
          value={selectedDevice}
          options={devices}
          getOptionLabel={(option: Device) =>
            `${option.name} (${option.device_sn || option.imei_code})`
          }
          filterOptions={(options, { inputValue }) =>
            options.filter(
              (option) =>
                option.name.toLowerCase().includes(inputValue.toLowerCase()) ||
                (option.device_sn || '').toLowerCase().includes(inputValue.toLowerCase())
            )
          }
          onChange={(_, value) => handleDeviceChange(value)}
          renderInput={(params) => (
            <TextField 
              {...params} 
              label={t('device.map.searchDevice')} 
              variant="outlined"
              InputProps={{
                ...params.InputProps,
                endAdornment: (params.InputProps.endAdornment),
              }}
            />
          )}
        />
        <FormControlLabel
          control={
            <Switch
              checked={showTrack}
              onChange={(_, checked) => handleShowTrackChange(checked, selectedDevice?.device_sn)}
              color="primary"
            />
          }
          label={t('device.map.trackInfo.toggleTrack')}
        />
        {loadingTracks && (
          <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
            <CircularProgress size={16} sx={{ mr: 1 }} />
            <Typography variant="caption" color="text.secondary">
              {t('common.loading')}
            </Typography>
          </Box>
        )}
      </Paper>

      <div 
        id="container" 
        ref={mapContainer} 
        style={{ width: '100%', height: '100%' }} 
      />
    </Box>
  );
};

export default DeviceMap;
