import Box from '@mui/material/Box';
import Sidebar from '../layout/Sidebar';
import Header from '../layout/Header-MiniScreen';
import Footer from '../layout/Footer';
import { Outlet } from 'react-router-dom';
import { styled, Paper, alpha } from '@mui/material';

const MainContentBox = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'component'
})<{ component?: React.ElementType }>(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  minWidth: 0,
  // gap: 0,
  padding: `${theme.spacing(1)} ${theme.spacing(1)} 0 ${theme.spacing(1)}`,
  transition: 'all 0.3s ease',
  backgroundImage: theme.palette.mode === 'light' 
    ? `linear-gradient(to bottom right, ${alpha(theme.palette.background.paper, 0.8)}, ${alpha(theme.palette.background.default, 0.5)})`
    : `linear-gradient(to bottom right, ${alpha(theme.palette.background.paper, 0.6)}, ${alpha(theme.palette.background.default, 0.8)})`,
}));

const ContentWrapper = styled(Paper)(({ theme }) => ({
  flex: 1,
  borderRadius: theme.shape.borderRadius * 2,
  boxShadow: theme.shadows[2],
  padding: theme.spacing(1),
  backgroundImage: `linear-gradient(to bottom, ${alpha(theme.palette.background.paper, 0.8)}, ${theme.palette.background.paper})`,
  backdropFilter: 'blur(4px)',
  [theme.breakpoints.down('sm')]: {
    backdropFilter: 'none',
    backgroundColor: theme.palette.background.paper,
    padding: theme.spacing(2),
  },
  overflow: 'auto',
}));

export default function Dashboard() {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100dvh',
        background: (theme) => theme.palette.mode === 'light'
          ? 'linear-gradient(145deg, #f5f7fa 0%, #e4e8eb 100%)'
          : 'linear-gradient(145deg, #1f2023 0%, #121212 100%)',
      }}
    >
      {/* Header */}
      <Header />

      {/* 主要内容区域和 Sidebar */}
      <Box
        sx={{
          display: 'flex',
          flex: 1,
          mt: { xs: 'var(--Header-height)', md: 0 }
        }}
      >
        {/* Sidebar */}
        <Sidebar />

        {/* Content */}
        <MainContentBox component="main" className="MainContent">
          {/* 主要内容区域 */}
          <ContentWrapper>
            <Outlet />
          </ContentWrapper>

          {/* Footer */}
          <Footer />
        </MainContentBox>
      </Box>
    </Box>
  );
}