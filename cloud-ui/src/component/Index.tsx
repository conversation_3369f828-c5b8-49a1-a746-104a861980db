import * as React from 'react';
import { GlobalStyles, Box, CssBaseline } from '@mui/material';
import LoginForm from './auth/SignInForm';
import SignUpForm from './auth/SignUpForm';
import Footer from './layout/Footer';
import ColorSchemeToggle from './theme/ColorSchemeToggle';
import LanguageToggle from './theme/LanguageSwitch';
import { WechatQRCode } from './layout/Sidebar';
export default function SignInSideTemplate() {
  const [isSignUp, setIsSignUp] = React.useState(false); // 控制显示登录还是注册表单

  return (
    <>
      <CssBaseline />
      <GlobalStyles
        styles={{
          ':root': {
            '--Form-maxWidth': '800px',
            '--Transition-duration': '0.4s', // set to `none` to disable transition
            '--Header-height': '52px',
          },
        }}
      />
      <Box
        sx={(theme) => ({
          width: { xs: '100%', md: '50vw' },
          transition: 'width var(--Transition-duration)',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          position: 'relative',
          zIndex: 1,
          display: 'flex',
          justifyContent: 'flex-end',
          backdropFilter: 'blur(12px)',
          backgroundColor: 'rgba(255 255 255 / 0.2)',
          ...(theme.palette.mode === 'dark' && {
            backgroundColor: 'rgba(19 19 24 / 0.4)',
          }),
        })}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            minHeight: '100dvh',
            width: '100%',
            px: 2,
          }}
        >
          <Box
            component="header"
            sx={{ py: 1, display: 'flex', justifyContent: 'space-between' }}
          >
            <Box sx={{height: 50}}>
              <img src="/logo.png" alt="logo" style={{ maxWidth: '150px', height: '100%' }} />
            </Box>
            <Box sx={{ gap: 2, display: 'flex', alignItems: 'center' }}>  
              <LanguageToggle />
              <ColorSchemeToggle />
              <WechatQRCode/>
            </Box>
          </Box>
          <Box
            component="main"
            sx={{
              my: 'auto',
              py: 2,
              pb: 5,
              display: 'flex',
              flexDirection: 'column',
              gap: 2,
              width: 400,
              maxWidth: '100%',
              mx: 'auto',
              borderRadius: '4px',
              '& form': {
                display: 'flex',
                flexDirection: 'column',
                gap: 2,
              },
              [`& .MuiFormLabel-asterisk`]: {
                visibility: 'hidden',
              },
            }}
          >
            {isSignUp ? (
              <SignUpForm onSwitchToLogin={() => setIsSignUp(false)} />
            ) : (
              <LoginForm onSwitchToSignUp={() => setIsSignUp(true)} />
            )}
          </Box>
          <Footer />
        </Box>
      </Box>
      <Box
        sx={(theme) => ({
          height: '100%',
          position: 'fixed',
          right: 0,
          top: 0,
          bottom: 0,
          left: { xs: 0, md: '50vw' },
          transition:
            'background-image var(--Transition-duration), left var(--Transition-duration) !important',
          transitionDelay: 'calc(var(--Transition-duration) + 0.1s)',
          backgroundColor: 'background.default',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundImage: `url("/light-bgimg.jpg")`,
          ...(theme.palette.mode === 'dark' && {
            backgroundImage: `url("/dark-bgimg.jpg")`,
          }),
        })}
      />
    </>
  );
}