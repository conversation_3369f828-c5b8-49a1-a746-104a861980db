import React, { useState, useRef, useEffect, useCallback } from 'react';
import { Box, Paper, Typography, IconButton, Tooltip, Divider, Stack, <PERSON>nackbar, Alert, CircularProgress, List, ListItem, ListItemText, ListItemIcon, TextField, Button, Menu, MenuItem } from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import AddIcon from '@mui/icons-material/Add';
import SaveIcon from '@mui/icons-material/Save';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import StopIcon from '@mui/icons-material/Stop';
import DeleteIcon from '@mui/icons-material/Delete';
import UndoIcon from '@mui/icons-material/Undo';
import RedoIcon from '@mui/icons-material/Redo';
import GridOnIcon from '@mui/icons-material/GridOn';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import ZoomOutIcon from '@mui/icons-material/ZoomOut';
import AspectRatioIcon from '@mui/icons-material/AspectRatio';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import DashboardIcon from '@mui/icons-material/Dashboard';
import FolderIcon from '@mui/icons-material/Folder';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import WarningIcon from '@mui/icons-material/Warning';
import OpenInNewIcon from '@mui/icons-material/OpenInNew';
// 导入组件库图标
import SpeedIcon from '@mui/icons-material/Speed'; // 仪表盘
import BarChartIcon from '@mui/icons-material/BarChart'; // 图表
import SmartButtonIcon from '@mui/icons-material/SmartButton'; // 按钮
import TextFieldsIcon from '@mui/icons-material/TextFields'; // 文本
import WaterIcon from '@mui/icons-material/Water'; // 水箱
import LinearScaleIcon from '@mui/icons-material/LinearScale'; // 管道
import SliderIcon from '@mui/icons-material/Tune'; // 滑块
import ToggleOnIcon from '@mui/icons-material/ToggleOn'; // 开关
import LightbulbIcon from '@mui/icons-material/Lightbulb'; // 指示灯
import SensorsIcon from '@mui/icons-material/Sensors'; // 传感器
import ImageIcon from '@mui/icons-material/Image'; // 图片
import TableChartIcon from '@mui/icons-material/TableChart'; // 数据表格
import EqualizerIcon from '@mui/icons-material/Equalizer'; // 进度条
import MotorIcon from '@mui/icons-material/SettingsPower'; // 电机/设备
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';
import '../styles/HMIEditor.css';
import { PropertiesPanel } from './PropertiesPanel';
import { HMICanvas } from './HMICanvas';
import { IndustrialIconWidget } from './widgets/IndustrialIconWidget';
import { debounce } from 'lodash';
import { saveSCADAProject, loadSCADAProject, listSCADAProjects, deleteSCADAProject, SCADAConfig } from '../../../services/scadaService';
import { useDialogContext } from '../../../context/GlobalDialog';
// 导入主题服务
import { getAvailableTopics, MQTTTopic } from '../../../services/topicService';

// 定义历史记录中的操作类型
type ActionType = 'ADD_WIDGET' | 'DELETE_WIDGET' | 'UPDATE_WIDGET' | 'UPDATE_PROPERTY' | 'MOVE_WIDGET';

// 定义历史记录中的操作接口
interface HistoryAction {
  type: ActionType;
  payload: any;
  previousState?: any;
}

export interface HMIEditorProps {
  projectId?: string;
}

// 在组件外部定义组件库数据类型
interface ComponentItem {
  type: string;
  label: string;
  icon: any;  // React icon component or null
  iconType?: string;  // Optional for industrial icons
}

interface ComponentCategory {
  category: string;
  items: ComponentItem[];
}

// 在组件外部定义组件库数据
const componentLibraryItems: ComponentCategory[] = [
  // 数据显示类组件
  {
    category: 'dataDisplay',
    items: [
      { type: 'gauge', label: 'gauge', icon: SpeedIcon },
      { type: 'chart', label: 'chart', icon: BarChartIcon },
      { type: 'sensor', label: 'sensor', icon: SensorsIcon },
      { type: 'text', label: 'text', icon: TextFieldsIcon },
      { type: 'table', label: 'table', icon: TableChartIcon },
      { type: 'image', label: 'image', icon: ImageIcon },
    ]
  },
  // 控制设备类组件
  {
    category: 'controlDevice',
    items: [
      { type: 'button', label: 'button', icon: SmartButtonIcon },
      { type: 'switch', label: 'switch', icon: ToggleOnIcon },
      { type: 'slider', label: 'slider', icon: SliderIcon },
      { type: 'tank', label: 'tank', icon: WaterIcon },
      { type: 'pipe', label: 'pipe', icon: LinearScaleIcon },
      { type: 'led', label: 'led', icon: LightbulbIcon },
      { type: 'progressbar', label: 'progressbar', icon: EqualizerIcon },
      { type: 'motor', label: 'motor', icon: MotorIcon },
    ]
  },
  // 工业图标组件
  {
    category: 'industrialIcons',
    items: [
      { type: 'industrialIcon', label: 'pump', icon: null, iconType: 'pump' },
      { type: 'industrialIcon', label: 'valve', icon: null, iconType: 'valve' },
      { type: 'industrialIcon', label: 'tank', icon: null, iconType: 'tank' },
      { type: 'industrialIcon', label: 'motor', icon: null, iconType: 'motor' },
      { type: 'industrialIcon', label: 'fan', icon: null, iconType: 'fan' },
      { type: 'industrialIcon', label: 'heater', icon: null, iconType: 'heater' },
      { type: 'industrialIcon', label: 'filter', icon: null, iconType: 'filter' },
      { type: 'industrialIcon', label: 'reactor', icon: null, iconType: 'reactor' },
      { type: 'industrialIcon', label: 'compressor', icon: null, iconType: 'compressor' },
      { type: 'industrialIcon', label: 'battery', icon: null, iconType: 'battery' },
      { type: 'industrialIcon', label: 'gauge', icon: null, iconType: 'gauge' },
      { type: 'industrialIcon', label: 'controlValve', icon: null, iconType: 'controlValve' },
      { type: 'industrialIcon', label: 'switch', icon: null, iconType: 'switch' },
      { type: 'industrialIcon', label: 'transformer', icon: null, iconType: 'transformer' },
    ]
  }
];

export const HMIEditor: React.FC<HMIEditorProps> = ({ projectId: externalProjectId }) => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { confirm } = useDialogContext();
  const [showGrid, setShowGrid] = useState(true);
  const [selectedElement, setSelectedElement] = useState<any>(null);
  const [widgets, setWidgets] = useState<any[]>([]);
  const [zoom, setZoom] = useState(1);
  const [isComponentLibraryCollapsed, setIsComponentLibraryCollapsed] = useState(false);
  const canvasRef = useRef<HTMLDivElement>(null);
  // 当前最高的zIndex值
  const [maxZIndex, setMaxZIndex] = useState(1000);
  // 添加预览模式状态
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  
  // 内部保存当前选中的项目ID
  const [currentProjectId, setCurrentProjectId] = useState<string | undefined>(externalProjectId);
  
  // 历史记录状态
  const [history, setHistory] = useState<HistoryAction[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isPerformingUndoRedo, setIsPerformingUndoRedo] = useState(false);

  // 用于颜色选择防抖的状态和引用
  const [isColorUpdating, setIsColorUpdating] = useState(false);
  const colorUpdateRef = useRef<{
    widgetId: string | null;
    property: string | null;
    initialValue: any;
  }>({
    widgetId: null,
    property: null,
    initialValue: null
  });
  
  // 添加移动组件防抖状态
  const [isMovingWidget, setIsMovingWidget] = useState(false);
  const moveWidgetRef = useRef<{
    widgetId: string | null;
    initialState: any;
  }>({
    widgetId: null,
    initialState: null
  });
  
  // 选中的组件类型（从组件库选择）
  const [selectedComponentType, setSelectedComponentType] = useState<{
    type: string;
    iconType?: string;
  } | null>(null);
  
  // 拖框创建的状态
  const [creatingWidgetRect, setCreatingWidgetRect] = useState<{
    startX: number;
    startY: number;
    x: number;
    y: number;
    width: number;
    height: number;
  } | null>(null);
  
  // 添加保存状态
  const [isSaving, setIsSaving] = useState(false);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [openSnackbar, setOpenSnackbar] = useState(false);
  const [projectName, setProjectName] = useState(t('scada.editor.untitled'));
  
  // 添加项目列表状态
  const [projectList, setProjectList] = useState<SCADAConfig[]>([]);
  const [showProjectList, setShowProjectList] = useState(false);
  const [isLoadingProjects, setIsLoadingProjects] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [projectMenuAnchor, setProjectMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedProjectForMenu, setSelectedProjectForMenu] = useState<SCADAConfig | null>(null);
  
  // 添加主题列表状态
  const [availableTopics, setAvailableTopics] = useState<MQTTTopic[]>([]);
  const [isLoadingTopics, setIsLoadingTopics] = useState(false);
  
  // 防抖函数：记录颜色更新历史
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedAddColorHistory = useCallback(
    debounce((widgetId: string, property: string, value: any, previousValue: any) => {
      // 仅当值发生变化时记录历史
      if (value !== previousValue) {
        addHistory({
          type: 'UPDATE_PROPERTY',
          payload: {
            widgetId,
            property,
            value
          },
          previousState: previousValue
        });
      }
      
      // 重置颜色更新状态
      setIsColorUpdating(false);
      colorUpdateRef.current = {
        widgetId: null,
        property: null,
        initialValue: null
      };
    }, 500), // 500ms延迟，可根据需要调整
    []  // 移除addHistory依赖
  );
  
  // 添加组件移动防抖函数
  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedAddMoveHistory = useCallback(
    debounce((_widgetId: string, currentWidget: any, initialState: any) => {
      // 检查位置是否发生变化
      if (
        initialState.x !== currentWidget.x ||
        initialState.y !== currentWidget.y ||
        initialState.w !== currentWidget.w ||
        initialState.h !== currentWidget.h
      ) {
        addHistory({
          type: 'MOVE_WIDGET',
          payload: currentWidget,
          previousState: initialState
        });
      }
      
      // 重置移动状态
      setIsMovingWidget(false);
      moveWidgetRef.current = {
        widgetId: null,
        initialState: null
      };
    }, 500), // 500ms延迟
    []  // 空依赖数组，因为我们会手动更新引用
  );

  // 添加历史记录
  const addHistory = useCallback((action: HistoryAction) => {
    if (isPerformingUndoRedo) return; // 如果正在执行撤销/重做，则不记录历史

    const newHistory = history.slice(0, historyIndex + 1);
    newHistory.push(action);
    
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
  }, [history, historyIndex, isPerformingUndoRedo]);

  // 撤销操作
  const undo = useCallback(() => {
    if (historyIndex < 0) return; // 没有可以撤销的操作
    
    setIsPerformingUndoRedo(true);
    
    const action = history[historyIndex];
    
    switch (action.type) {
      case 'ADD_WIDGET':
        // 删除添加的组件
        setWidgets(widgets.filter(w => w.id !== action.payload.id));
        if (selectedElement && selectedElement.id === action.payload.id) {
          setSelectedElement(null);
        }
        break;
        
      case 'DELETE_WIDGET':
        // 恢复被删除的组件
        setWidgets([...widgets, action.payload]);
        break;
        
      case 'UPDATE_WIDGET':
      case 'MOVE_WIDGET':
        // 恢复组件的上一个状态
        setWidgets(widgets.map(w => 
          w.id === action.payload.id ? action.previousState : w
        ));
        
        // 如果当前选中的元素是被撤销的元素，更新选中状态
        if (selectedElement && selectedElement.id === action.payload.id) {
          setSelectedElement(action.previousState);
        }
        break;
        
      case 'UPDATE_PROPERTY':
        // 恢复属性的上一个状态
        const updatedWidgets = widgets.map(w => {
          if (w.id === action.payload.widgetId) {
            return {
              ...w,
              properties: {
                ...w.properties,
                [action.payload.property]: action.previousState
              }
            };
          }
          return w;
        });
        
        setWidgets(updatedWidgets);
        
        // 如果当前选中的元素是被撤销的元素，更新选中状态
        if (selectedElement && selectedElement.id === action.payload.widgetId) {
          const updatedWidget = updatedWidgets.find(w => w.id === action.payload.widgetId);
          if (updatedWidget) {
            setSelectedElement(updatedWidget);
          }
        }
        break;
    }
    
    setHistoryIndex(historyIndex - 1);
    setIsPerformingUndoRedo(false);
  }, [history, historyIndex, widgets, selectedElement]);

  // 重做操作
  const redo = useCallback(() => {
    if (historyIndex >= history.length - 1) return; // 没有可以重做的操作
    
    setIsPerformingUndoRedo(true);
    
    const action = history[historyIndex + 1];
    
    switch (action.type) {
      case 'ADD_WIDGET':
        // 重新添加组件
        setWidgets([...widgets, action.payload]);
        break;
        
      case 'DELETE_WIDGET':
        // 重新删除组件
        setWidgets(widgets.filter(w => w.id !== action.payload.id));
        if (selectedElement && selectedElement.id === action.payload.id) {
          setSelectedElement(null);
        }
        break;
        
      case 'UPDATE_WIDGET':
      case 'MOVE_WIDGET':
        // 重新应用更新
        setWidgets(widgets.map(w => 
          w.id === action.payload.id ? action.payload : w
        ));
        
        // 如果当前选中的元素是被重做的元素，更新选中状态
        if (selectedElement && selectedElement.id === action.payload.id) {
          setSelectedElement(action.payload);
        }
        break;
        
      case 'UPDATE_PROPERTY':
        // 重新应用属性更新
        const updatedWidgets = widgets.map(w => {
          if (w.id === action.payload.widgetId) {
            return {
              ...w,
              properties: {
                ...w.properties,
                [action.payload.property]: action.payload.value
              }
            };
          }
          return w;
        });
        
        setWidgets(updatedWidgets);
        
        // 如果当前选中的元素是被重做的元素，更新选中状态
        if (selectedElement && selectedElement.id === action.payload.widgetId) {
          const updatedWidget = updatedWidgets.find(w => w.id === action.payload.widgetId);
          if (updatedWidget) {
            setSelectedElement(updatedWidget);
          }
        }
        break;
    }
    
    setHistoryIndex(historyIndex + 1);
    setIsPerformingUndoRedo(false);
  }, [history, historyIndex, widgets, selectedElement]);
  
  // 点击组件库项
  const handleComponentItemClick = useCallback((type: string, iconType?: string) => {
    // 如果已经选中了相同的组件类型，点击第二次取消选中
    if (selectedComponentType && selectedComponentType.type === type && 
        selectedComponentType.iconType === iconType) {
      setSelectedComponentType(null);
    } else {
      // 否则，选中新的组件类型
      setSelectedComponentType({ type, iconType });
    }
  }, [selectedComponentType]);
  
  // 清除选中的组件类型
  const clearSelectedComponentType = useCallback(() => {
    setSelectedComponentType(null);
  }, []);
  
  // 创建新的部件
  const createWidget = useCallback((type: string, id: string, x: number = 0, y: number = 0, zIndex: number = maxZIndex + 1, iconType?: string) => {
    const baseWidget = {
      id,
      type,
      x,
      y,
      w: 6,
      h: 6,
      zIndex, // 使用传入的zIndex
      properties: {}
    };

    switch (type) {
      case 'gauge':
        return {
          ...baseWidget,
          properties: {
            min: 0,
            max: 100,
            value: 50,
            unit: '%',
            title: '仪表盘',
          }
        };
      case 'chart':
        return {
          ...baseWidget,
          w: 12,
          h: 8,
          properties: {
            chartType: 'line',
            dataSource: 'tag1',
            title: '图表',
          }
        };
      case 'button':
        return {
          ...baseWidget,
          w: 3,
          h: 3,
          properties: {
            label: '按钮',
            action: 'toggle',
            targetTag: '',
          }
        };
      case 'text':
        return {
          ...baseWidget,
          w: 5,
          h: 2,
          properties: {
            text: '文本标签',
            fontSize: 16,
            color: '#000000',
            alignment: 'center',
          }
        };
      case 'tank':
        return {
          ...baseWidget,
          w: 4,
          h: 8,
          properties: {
            min: 0,
            max: 100,
            value: 60,
            unit: 'L',
            fillColor: '#3f51b5',
            title: '水箱',
          }
        };
      case 'pipe':
        return {
          ...baseWidget,
          w: 10,
          h: 3,
          properties: {
            flowDirection: 'right',
            flowSpeed: 5,
            flowActive: true,
            pipeColor: '#666666',
            flowColor: '#2196f3',
          }
        };
      case 'slider':
        return {
          ...baseWidget,
          w: 8,
          h: 2,
          properties: {
            min: 0,
            max: 100,
            value: 50,
            unit: '%',
            showLabels: true,
            orientation: 'horizontal',
            step: 0.1,
          }
        };
      case 'switch':
        return {
          ...baseWidget,
          w: 3,
          h: 2,
          properties: {
            state: false,
            label: '开关',
            onColor: '#4caf50',
            offColor: '#f44336',
          }
        };
      case 'led':
        return {
          ...baseWidget,
          w: 2,
          h: 2,
          properties: {
            state: false,
            color: '#f44336',
            size: 'medium',
            label: '指示灯',
          }
        };
      case 'sensor':
        return {
          ...baseWidget,
          w: 3,
          h: 4,
          properties: {
            value: '25.5',
            unit: '°C',
            title: 'Sensor',
            precision: 1,
            type: 'temperature',
          }
        };
      case 'image':
        return {
          ...baseWidget,
          w: 6,
          h: 6,
          properties: {
            src: '',
          }
        };
      case 'table':
        return {
          ...baseWidget,
          w: 12,
          h: 8,
          properties: {
            columns: [
              { field: 'id', headerName: 'ID' },
              { field: 'name', headerName: 'Name' },
              { field: 'value', headerName: 'Value' },
            ],
            dataSource: 'tagList',
          }
        };
      case 'progressbar':
        return {
          ...baseWidget,
          w: 8,
          h: 2,
          properties: {
            min: 0,
            max: 100,
            value: 65,
            showValue: true,
            color: 'primary',
            orientation: 'horizontal',
          }
        };
      case 'motor':
        return {
          ...baseWidget,
          w: 4,
          h: 4,
          properties: {
            running: false,
            speed: 50,
            direction: 'clockwise',
            label: 'Motor',
          }
        };
      case 'industrialIcon':
        return {
          ...baseWidget,
          w: 4,
          h: 4,
          properties: {
            iconType: iconType || 'pump', // 使用传入的图标类型，默认为pump
            color: '#333333',
            backgroundColor: 'transparent',
            rotation: 0,
          }
        };
      default:
        return null;
    }
  }, [maxZIndex]);
  
  // 完成组件创建
  const finishCreatingWidget = useCallback((x: number, y: number, width: number, height: number) => {
    if (!selectedComponentType) return;
    
    const id = `widget-${Date.now()}`;
    const newZIndex = maxZIndex + 1;
    setMaxZIndex(newZIndex);
    
    // 将像素转换为网格单位（从20像素改为10像素，让网格更密集）
    const gridW = Math.max(1, Math.round(width / 10));
    const gridH = Math.max(1, Math.round(height / 10));
    
    // 创建小部件并应用自定义大小
    const baseWidget = createWidget(
      selectedComponentType.type,
      id,
      x,
      y,
      newZIndex,
      selectedComponentType.iconType
    );
    
    if (baseWidget) {
      // 应用用户绘制的尺寸
      const newWidget = {
        ...baseWidget,
        w: gridW,
        h: gridH
      };
      
      setWidgets([...widgets, newWidget]);
      setSelectedElement(newWidget);
      
      // 记录添加组件的操作
      addHistory({
        type: 'ADD_WIDGET',
        payload: newWidget
      });
      
      // 创建完成后，清除选中的组件类型
      clearSelectedComponentType();
    }
  }, [selectedComponentType, maxZIndex, widgets, createWidget, addHistory, clearSelectedComponentType]);

  // 画布点击事件 - 开始创建组件
  const handleCanvasMouseDown = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    // 如果没有选中组件类型或处于预览模式，则不处理
    if (!selectedComponentType || isPreviewMode) return;
    
    // 获取相对于画布的点击位置
    const canvasRect = canvasRef.current?.getBoundingClientRect();
    if (!canvasRect) return;
    
    // 计算鼠标位置（考虑缩放）
    const mouseX = (e.clientX - canvasRect.left) / zoom;
    const mouseY = (e.clientY - canvasRect.top) / zoom;
    
    // 设置拖框起始点
    setCreatingWidgetRect({
      startX: mouseX,
      startY: mouseY,
      x: mouseX,
      y: mouseY,
      width: 0,
      height: 0
    });
  }, [selectedComponentType, isPreviewMode, zoom]);
  
  // 画布鼠标移动事件 - 更新拖框大小
  const handleCanvasMouseMove = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!creatingWidgetRect || !canvasRef.current) return;
    
    const canvasRect = canvasRef.current.getBoundingClientRect();
    const currentX = (e.clientX - canvasRect.left) / zoom;
    const currentY = (e.clientY - canvasRect.top) / zoom;
    
    const width = Math.abs(currentX - creatingWidgetRect.startX);
    const height = Math.abs(currentY - creatingWidgetRect.startY);
    
    // 确定矩形的左上角坐标
    const x = Math.min(currentX, creatingWidgetRect.startX);
    const y = Math.min(currentY, creatingWidgetRect.startY);
    
    setCreatingWidgetRect({
      ...creatingWidgetRect,
      x, y, width, height
    });
  }, [creatingWidgetRect, zoom]);
  
  // 画布鼠标释放事件 - 完成创建组件
  const handleCanvasMouseUp = useCallback((_e: React.MouseEvent<HTMLDivElement>) => {
    if (!creatingWidgetRect || !selectedComponentType) return;
    
    // 最小尺寸检查
    if (creatingWidgetRect.width < 20 || creatingWidgetRect.height < 20) {
      // 如果太小，创建默认大小的组件
      finishCreatingWidget(
        creatingWidgetRect.startX,
        creatingWidgetRect.startY,
        selectedComponentType.type === 'text' ? 100 : 120,
        selectedComponentType.type === 'text' ? 40 : 120
      );
    } else {
      // 创建用户定义大小的组件
      finishCreatingWidget(
        creatingWidgetRect.x,
        creatingWidgetRect.y,
        creatingWidgetRect.width,
        creatingWidgetRect.height
      );
    }
    
    // 重置创建状态
    setCreatingWidgetRect(null);
  }, [creatingWidgetRect, selectedComponentType, finishCreatingWidget]);

  // 清除选中状态
  const clearSelection = () => {
    setSelectedElement(null);
  };

  // 处理部件选择
  const handleWidgetSelect = (widget: any) => {
    if (widget) {
      // 选中组件时，自动将其设置为最高层级
      const newZIndex = maxZIndex + 1;
      
      // 记录原始状态用于撤销
      const originalWidget = widgets.find(w => w.id === widget.id);
      
      const updatedWidgets = widgets.map(w => {
        if (w.id === widget.id) {
          return {
            ...w,
            zIndex: newZIndex
          };
        }
        return w;
      });
      
      setWidgets(updatedWidgets);
      setMaxZIndex(newZIndex);
      
      // 使用更新后的组件作为selectedElement
      const updatedWidget = updatedWidgets.find(w => w.id === widget.id);
      setSelectedElement(updatedWidget || widget);
      
      // 记录层级更新操作
      if (originalWidget && originalWidget.zIndex !== newZIndex) {
        addHistory({
          type: 'UPDATE_WIDGET',
          payload: updatedWidget || widget,
          previousState: originalWidget
        });
      }
    } else {
      setSelectedElement(null);
    }
  };

  // 更新部件属性
  const updateWidgetProperty = (widgetId: string, property: string, value: any) => {
    // 获取更新前的属性值
    const widget = widgets.find(w => w.id === widgetId);
    const previousValue = widget?.properties[property];
    
    // 更新属性时也提升层级
    const newZIndex = maxZIndex + 1;
    
    const updatedWidgets = widgets.map(w => {
      if (w.id === widgetId) {
        return {
          ...w,
          zIndex: newZIndex,  // 更新层级
          properties: {
            ...w.properties,
            [property]: value
          }
        };
      }
      return w;
    });
    
    setWidgets(updatedWidgets);
    setMaxZIndex(newZIndex);
    
    // 更新selectedElement引用，确保属性面板显示最新的属性值
    if (selectedElement && selectedElement.id === widgetId) {
      const updatedSelectedWidget = updatedWidgets.find(w => w.id === widgetId);
      if (updatedSelectedWidget) {
        setSelectedElement(updatedSelectedWidget);
      }
    }
    
    // 检查属性名是否包含'color'（不区分大小写）
    const isColorProperty = /color/i.test(property);
    
    if (isColorProperty) {
      // 颜色属性的特殊处理
      if (!isColorUpdating) {
        // 首次更新颜色，记录初始值
        setIsColorUpdating(true);
        colorUpdateRef.current = {
          widgetId,
          property,
          initialValue: previousValue
        };
      }
      
      // 如果是同一个组件的同一个颜色属性，使用debounce
      if (colorUpdateRef.current.widgetId === widgetId && 
          colorUpdateRef.current.property === property) {
        // 使用防抖函数添加历史记录
        debouncedAddColorHistory(
          widgetId, 
          property, 
          value, 
          colorUpdateRef.current.initialValue
        );
      } else {
        // 如果切换了属性或组件，需要先保存之前的记录
        if (isColorUpdating && colorUpdateRef.current.widgetId && colorUpdateRef.current.property) {
          // 取消之前的防抖，立即执行
          debouncedAddColorHistory.flush();
        }
        
        // 更新为新的颜色属性
        colorUpdateRef.current = {
          widgetId,
          property,
          initialValue: previousValue
        };
        
        // 启动新的防抖
        debouncedAddColorHistory(widgetId, property, value, previousValue);
      }
    } else {
      // 非颜色属性的正常处理
      // 如果正在进行颜色更新，需要先完成它
      if (isColorUpdating) {
        debouncedAddColorHistory.flush();
      }
      
      // 记录属性更新操作（只有当值实际发生变化时才记录）
      if (value !== previousValue) {
        addHistory({
          type: 'UPDATE_PROPERTY',
          payload: {
            widgetId,
            property,
            value
          },
          previousState: previousValue
        });
      }
    }
  };

  // 删除选中的部件
  const deleteSelectedWidget = () => {
    if (selectedElement) {
      // 记录被删除的组件，以便撤销
      const deletedWidget = { ...selectedElement };
      
      setWidgets(widgets.filter(widget => widget.id !== selectedElement.id));
      setSelectedElement(null);
      
      // 记录删除操作
      addHistory({
        type: 'DELETE_WIDGET',
        payload: deletedWidget
      });
    }
  };

  // 处理布局变化
  const handleLayoutChange = (updatedWidgets: any[]) => {
    // 移动或调整大小组件时，将其层级提升
    if (selectedElement) {
      // 记录原始状态
      const originalWidget = widgets.find(w => w.id === selectedElement.id);
      const newZIndex = maxZIndex + 1;
      
      const widgetsWithUpdatedZIndex = updatedWidgets.map(widget => {
        if (widget.id === selectedElement.id) {
          return {
            ...widget,
            zIndex: newZIndex
          };
        }
        return widget;
      });
      
      setWidgets(widgetsWithUpdatedZIndex);
      setMaxZIndex(newZIndex);
      
      // 更新选中元素的引用
      const updatedSelectedWidget = widgetsWithUpdatedZIndex.find(
        widget => widget.id === selectedElement.id
      );
      
      if (updatedSelectedWidget && originalWidget) {
        setSelectedElement(updatedSelectedWidget);
        
        // 处理移动历史记录的防抖
        const positionChanged = (
          originalWidget.x !== updatedSelectedWidget.x ||
          originalWidget.y !== updatedSelectedWidget.y ||
          originalWidget.w !== updatedSelectedWidget.w ||
          originalWidget.h !== updatedSelectedWidget.h
        );
        
        if (positionChanged) {
          // 如果位置变化且不是同一个组件的移动，需要记录新的初始状态
          if (!isMovingWidget || moveWidgetRef.current.widgetId !== selectedElement.id) {
            // 如果之前有其他组件正在移动，需要先完成那个记录
            if (isMovingWidget && moveWidgetRef.current.widgetId) {
              const previousWidget = widgets.find(w => w.id === moveWidgetRef.current.widgetId);
              if (previousWidget && moveWidgetRef.current.initialState) {
                debouncedAddMoveHistory.flush();
              }
            }
            
            // 开始追踪新的移动
            setIsMovingWidget(true);
            moveWidgetRef.current = {
              widgetId: selectedElement.id,
              initialState: { ...originalWidget }
            };
          }
          
          // 使用防抖函数延迟添加移动历史
          debouncedAddMoveHistory(
            selectedElement.id,
            updatedSelectedWidget,
            moveWidgetRef.current.initialState
          );
        }
      }
    } else {
      setWidgets(updatedWidgets);
    }
  };

  // 放大
  const zoomIn = () => {
    setZoom(prev => Math.min(prev + 0.1, 2));
  };

  // 缩小
  const zoomOut = () => {
    setZoom(prev => Math.max(prev - 0.1, 0.5));
  };

  // 重置缩放
  const resetZoom = () => {
    setZoom(1);
  };

  // 切换组件库折叠状态
  const toggleComponentLibrary = () => {
    setIsComponentLibraryCollapsed(!isComponentLibraryCollapsed);
  };
  
  // 切换预览模式
  const togglePreviewMode = () => {
    setIsPreviewMode(!isPreviewMode);
    // 如果进入预览模式，清除选中状态
    if (!isPreviewMode) {
      setSelectedElement(null);
    }
  };
  
  // 添加键盘快捷键支持
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 如果在预览模式下，不处理键盘事件
      if (isPreviewMode) return;
      
      // 获取当前的撤销/重做状态
      const currentCanUndo = historyIndex >= 0;
      const currentCanRedo = historyIndex < history.length - 1;
      
      // 删除键：删除选中元素
      if (e.key === 'Delete' && selectedElement) {
        deleteSelectedWidget();
      }
      
      // Ctrl + Z：撤销操作
      if (e.key === 'z' && (e.ctrlKey || e.metaKey) && currentCanUndo) {
        e.preventDefault(); // 阻止浏览器默认的撤销行为
        undo();
      }
      
      // Ctrl + Y：重做操作
      if (e.key === 'y' && (e.ctrlKey || e.metaKey) && currentCanRedo) {
        e.preventDefault(); // 阻止浏览器默认的重做行为
        redo();
      }
    };
    
    // 添加键盘事件监听
    window.addEventListener('keydown', handleKeyDown);
    
    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [selectedElement, isPreviewMode, historyIndex, history.length, deleteSelectedWidget, undo, redo]); // 更新依赖项
  
  // 清理防抖函数
  useEffect(() => {
    // 组件卸载时取消所有挂起的防抖操作并清理
    return () => {
      if (debouncedAddColorHistory) {
        debouncedAddColorHistory.cancel();
      }
      if (debouncedAddMoveHistory) {
        debouncedAddMoveHistory.cancel();
      }
    };
  }, [debouncedAddColorHistory, debouncedAddMoveHistory]);
  
  // 根据撤销/重做状态判断按钮是否可用
  const canUndo = historyIndex >= 0;
  const canRedo = historyIndex < history.length - 1;

  // 渲染组件库项
  const renderComponentItem = (
    icon: React.ReactNode,
    label: string,
    type: string,
    iconType?: string
  ) => {
    // 检查是否选中了组件类型（高亮显示）
    const isTypeSelected = selectedComponentType && 
      selectedComponentType.type === type && 
      (type !== 'industrialIcon' || iconType === undefined || selectedComponentType.iconType === iconType);
    
    // 只有当用户直接在组件库中选择组件时才高亮显示
    const isSelected = isTypeSelected;
    
    return (
      <Tooltip key={`tooltip-${type}-${iconType || ''}`} title={label} placement={isComponentLibraryCollapsed ? 'right' : 'bottom'}>
        <Box sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          alignItems: 'center',
          p: 0.8,
          border: '1px solid',
          borderColor: isSelected ? 'primary.main' : 'divider',
          borderRadius: 1,
          cursor: 'pointer',
          backgroundColor: isSelected ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
          '&:hover': {
            bgcolor: 'action.hover',
          }
        }}
        onClick={() => handleComponentItemClick(type, iconType)}
        >
          <Box sx={{ width: 32, height: 32, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
            {icon}
          </Box>
        </Box>
      </Tooltip>
    );
  };

  // 渲染折叠状态下的组件库
  const renderCollapsedComponentLibrary = useCallback(() => {
    return (
      <Box sx={{ 
        display: 'flex', 
        flexDirection: 'column', 
        gap: 1.5, 
        alignItems: 'center',
        width: '100%',
        pt: 1
      }}>
        {componentLibraryItems.flatMap(category => 
          category.items.map(item => {
            // 获取标签本地化键
            const labelKey = item.iconType ? 
              `scada.components.icons.${item.label}` :
              `scada.components.types.${item.label}`;

            const iconElement = item.icon ? 
              <item.icon fontSize="small" /> : 
              (item.iconType && <IndustrialIconWidget iconType={item.iconType} size={16} />);

            return (
              <Tooltip 
                key={`${item.type}-${item.iconType || ''}`}
                title={t(labelKey)} 
                placement="right"
              >
                <Box 
                  sx={{ 
                    cursor: 'pointer',
                    width: 36,
                    height: 36,
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    borderRadius: '4px',
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                    bgcolor: selectedComponentType?.type === item.type && 
                            selectedComponentType?.iconType === item.iconType ? 
                            'rgba(25, 118, 210, 0.08)' : 'transparent'
                  }} 
                  onClick={() => handleComponentItemClick(item.type, item.iconType)}
                >
                  {iconElement}
                </Box>
              </Tooltip>
            );
          })
        )}
      </Box>
    );
  }, [handleComponentItemClick, t, selectedComponentType]);

  // 渲染展开状态下的组件库
  const renderExpandedComponentLibrary = useCallback(() => {
    return (
      <>
        {componentLibraryItems.map((category, index) => (
          <Box key={category.category}>
            <Typography 
              variant="subtitle2" 
              sx={{ mb: 1, color: 'text.secondary', borderBottom: '1px solid', borderColor: 'divider', pb: 0.5 }}
            >
              {t(`scada.components.categories.${category.category}`)}
            </Typography>
            <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 1 }}>
              {category.items.map(item => {
                // 创建图标元素
                const iconElement = item.icon ? 
                  <item.icon fontSize="small" /> : 
                  (item.iconType && <IndustrialIconWidget iconType={item.iconType} size={24} />);

                // 获取标签本地化键
                const labelKey = item.iconType ? 
                  `scada.components.icons.${item.label}` :
                  `scada.components.types.${item.label}`;

                return renderComponentItem(
                  iconElement,
                  t(labelKey),
                  item.type,
                  item.iconType
                );
              })}
            </Box>
            {index < componentLibraryItems.length - 1 && <Box sx={{ mb: 2 }} />}
          </Box>
        ))}
      </>
    );
  }, [renderComponentItem, t]);

  // 加载项目列表
  const loadProjectList = useCallback(async () => {
    setIsLoadingProjects(true);
    try {
      const projects = await listSCADAProjects();
      setProjectList(projects || []);
    } catch (error) {
      console.error('failed to load project list:', error);
      setSaveError(t('scada.editor.loadError'));
      setOpenSnackbar(true);
      setProjectList([]);
    } finally {
      setIsLoadingProjects(false);
    }
  }, [t]);

  // 创建新项目
  const createNewProject = useCallback(async () => {
    if (!newProjectName.trim()) {
      setSaveError(t('scada.editor.emptyProjectName'));
      setOpenSnackbar(true);
      return;
    }
    
    setIsSaving(true);
    setSaveError(null);
    
    try {
      // 创建新项目的数据结构
      const newProject: SCADAConfig = {
        projectId: `scada-${Date.now()}`, // 生成临时ID
        name: newProjectName.trim(),
        widgets: [],
      };
      
      // 保存新项目
      const savedProject = await saveSCADAProject(newProject);
      
      // 重置表单
      setNewProjectName('');
      
      // 刷新项目列表
      await loadProjectList();
      
      // 导航到新项目
      setCurrentProjectId(savedProject.projectId);
      
      setSaveSuccess(true);
      setOpenSnackbar(true);
    } catch (error) {
      console.error('failed to create project:', error);
      setSaveError(error instanceof Error ? error.message : t('scada.editor.createError'));
      setOpenSnackbar(true);
    } finally {
      setIsSaving(false);
    }
  }, [newProjectName, loadProjectList, t, setCurrentProjectId]);

  // 显示创建项目对话框
  const showCreateProjectDialog = useCallback(() => {
    // 创建一个临时状态存储项目名称
    let tempProjectName = '';
    
    // 使用全局对话框显示创建项目表单
    const dialogContent = (
      <Box sx={{ p: 3, minWidth: 400 }}>
        <TextField
          autoFocus
          margin="dense"
          label={t('scada.editor.projectName')}
          type="text"
          fullWidth
          variant="outlined"
          defaultValue=""
          onChange={(e) => tempProjectName = e.target.value}
          placeholder={t('scada.editor.projectNamePlaceholder')}
        />
      </Box>
    );
    
    confirm(
      t('scada.editor.createProject'),
      dialogContent,
      <AddIcon color="primary" />
    ).then(async (confirmed) => {
      if (confirmed && tempProjectName.trim()) {
        setNewProjectName(tempProjectName);
        // 延迟执行以确保状态更新
        setTimeout(() => {
          createNewProject();
        }, 0);
      }
    });
  }, [confirm, t, createNewProject]);

  // 点击新建按钮
  const handleAddClick = useCallback(() => {
    showCreateProjectDialog();
  }, [showCreateProjectDialog]);

  // 修改工具栏的新建按钮处理函数
  const handleToolbarNewProject = useCallback(() => {
    showCreateProjectDialog();
  }, [showCreateProjectDialog]);

  // 选择项目
  const selectProject = useCallback((project: SCADAConfig) => {
    // 不再使用navigate更新URL，而是直接设置当前项目ID
    setCurrentProjectId(project.projectId);
  }, []);

  // 删除项目
  const handleDeleteProject = useCallback(async (project: SCADAConfig) => {
    if (!project.projectId) return;
    
    // 使用全局对话框组件替代 window.confirm
    const confirmDelete = await confirm(
      t('common.deleteConfirm.title', { item: project.name }),
      <Box sx={{p:2,minWidth: 300}}>
        <Typography>
          {t('scada.editor.deleteConfirm', { name: project.name })}
        </Typography>
        <Typography sx={{color: 'warning.main'}} variant="caption">
          {t('common.deleteConfirm.warning')}
        </Typography>
      </Box>,
      <WarningIcon color="warning" />
    );
    
    if (!confirmDelete) {
      return;
    }
    
    try {
      await deleteSCADAProject(project.projectId);
      await loadProjectList();
      
      // 如果删除的是当前项目，回到初始状态
      if (currentProjectId === project.projectId) {
        setCurrentProjectId(undefined);
      }
      
      setOpenSnackbar(true);
      setSaveError(null);
      setSaveSuccess(true);
    } catch (error) {
      console.error('删除项目失败:', error);
      setSaveError(error instanceof Error ? error.message : t('scada.editor.deleteError'));
      setOpenSnackbar(true);
    } finally {
      setProjectMenuAnchor(null);
    }
  }, [currentProjectId, loadProjectList, t, confirm]);

  // 打开项目菜单
  const handleOpenProjectMenu = (event: React.MouseEvent<HTMLButtonElement>, project: SCADAConfig) => {
    setProjectMenuAnchor(event.currentTarget);
    setSelectedProjectForMenu(project);
  };

  // 关闭项目菜单
  const handleCloseProjectMenu = () => {
    setProjectMenuAnchor(null);
    setSelectedProjectForMenu(null);
  };

  // 组件挂载时加载项目列表
  useEffect(() => {
    loadProjectList();
  }, [loadProjectList]);

  // Load project data
  const loadProject = useCallback(async () => {
    if (!currentProjectId) {
      return;
    }

    try {
      //console.log('Loading project:', currentProjectId);
      const projectData = await loadSCADAProject(currentProjectId);
      //console.log('Received project data:', projectData);
      
      if (projectData) {
        setProjectName(projectData.name || t('scada.editor.untitled'));
        
        // Validate widgets array
        if (Array.isArray(projectData.widgets)) {
          //console.log('Setting widgets:', projectData.widgets);
          
          // Verify each widget has required properties
          const validWidgets = projectData.widgets.filter(widget => {
            const isValid = widget && widget.id && widget.type && 
                           typeof widget.x === 'number' && 
                           typeof widget.y === 'number' && 
                           typeof widget.w === 'number' && 
                           typeof widget.h === 'number';
            
            if (!isValid) {
              console.warn('Invalid widget found:', widget);
            }
            
            return isValid;
          });
          
          //console.log(`Found ${validWidgets.length} valid widgets out of ${projectData.widgets.length}`);
          setWidgets(validWidgets);
        } else {
          console.error('Widgets is not an array:', projectData.widgets);
          setWidgets([]);
        }
        
        // Reset states
        setSelectedElement(null);
        setSaveSuccess(false);
        setSaveError(null);
      }
    } catch (error) {
      console.error('Failed to load SCADA project:', error);
      // Don't show error for new projects (404)
      if (error instanceof Error && !error.message.includes('404')) {
        setSaveError(error instanceof Error ? error.message : t('common.messages.fetchError', { item: t('scada.editor.title'), error: '' }));
        setOpenSnackbar(true);
      }
    }
  }, [currentProjectId, t]);

  // 组件加载时尝试加载项目数据
  useEffect(() => {
    if (currentProjectId) {
      loadProject();
    } else {
      // 无项目ID时重置状态
      setWidgets([]);
      setProjectName(t('scada.editor.untitled'));
      setSelectedElement(null);
      setSaveSuccess(false);
    }
  }, [currentProjectId, loadProject, t]);
  
  // Save project
  const saveProject = useCallback(async () => {
    if (!currentProjectId) {
      setSaveError(t('scada.editor.saveError'));
      setOpenSnackbar(true);
      return;
    }
    
    setIsSaving(true);
    setSaveError(null);
    
    try {
      // Validate widgets before saving
      const validWidgets = widgets.filter(widget => {
        const isValid = widget && widget.id && widget.type && 
                        typeof widget.x === 'number' && 
                        typeof widget.y === 'number' && 
                        typeof widget.w === 'number' && 
                        typeof widget.h === 'number';
                        
        if (!isValid) {
          console.warn('Invalid widget skipped during save:', widget);
        }
        return isValid;
      });
      
      //console.log(`Saving ${validWidgets.length} valid widgets out of ${widgets.length}`);
      
      // Build save data
      const saveData: SCADAConfig = {
        projectId: currentProjectId,
        name: projectName,
        widgets: validWidgets,
      };
      
      // Call API to save data
      await saveSCADAProject(saveData);
      
      // Refresh project list
      await loadProjectList();
      
      setSaveSuccess(true);
      setOpenSnackbar(true);
    } catch (error) {
      console.error('Failed to save SCADA project:', error);
      setSaveError(error instanceof Error ? error.message : t('common.messages.updateError', { item: t('scada.editor.title') }));
      setOpenSnackbar(true);
    } finally {
      setIsSaving(false);
    }
  }, [currentProjectId, projectName, widgets, loadProjectList, t]);
  
  // 设置自动保存功能
  useEffect(() => {
    // 如果没有项目ID或处于预览模式，不启用自动保存
    if (!currentProjectId || isPreviewMode) {
      return undefined;
    }
    
    // 设置每5分钟自动保存一次
    const autoSaveInterval = 5 * 60 * 1000; // 5分钟
    const autoSaveTimer = setInterval(() => {
      // 只有当有部件且未在保存过程中时才自动保存
      if (widgets.length > 0 && !isSaving) {
        //console.log(t('scada.editor.autoSaving'));
        saveProject().catch(error => {
          console.error('自动保存失败:', error);
        });
      }
    }, autoSaveInterval);
    
    // 清除定时器
    return () => {
      clearInterval(autoSaveTimer);
    };
  }, [currentProjectId, widgets, isSaving, isPreviewMode, saveProject, t]);
  
  // 处理Snackbar关闭
  const handleCloseSnackbar = (_event: React.SyntheticEvent | Event, reason?: string) => {
    if (reason === 'clickaway') {
      return;
    }
    setOpenSnackbar(false);
  };

  // 加载主题列表
  const loadTopicsList = useCallback(async () => {
    setIsLoadingTopics(true);
    try {
      const topicsList = await getAvailableTopics();
      setAvailableTopics(topicsList);
    } catch (error) {
      console.error('加载主题失败:', error);
      // 可以在这里添加错误提示
    } finally {
      setIsLoadingTopics(false);
    }
  }, []);

  // 组件挂载时加载主题列表
  useEffect(() => {
    loadTopicsList();
  }, [loadTopicsList]);

  return (
    <Box sx={{ 
      display: 'flex', 
      flexDirection: 'column', 
      height: '100vh', 
      bgcolor: theme.palette.background.default 
    }}>
      {/* 主要内容区域 */}
      <Box sx={{ display: 'flex', flex: 1, overflow: 'hidden', position: 'relative' }}>
        {/* 左侧部件面板 - 在预览模式下隐藏 */}
        {!isPreviewMode && (
          <Box
            sx={{
              width: isComponentLibraryCollapsed ? 50 : 250,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              borderRight: '1px solid rgba(0, 0, 0, 0.12)',
              bgcolor: 'background.paper',
              transition: 'width 0.3s ease-in-out',
              position: 'relative',
              zIndex: 5,
              userSelect: 'none', // 防止拖动时选中文本
              WebkitUserSelect: 'none',
              MozUserSelect: 'none',
              msUserSelect: 'none'
            }}
          >
            {/* 固定的顶部工具栏 */}
            <Box sx={{ 
              display: 'flex', 
              justifyContent: isComponentLibraryCollapsed ? 'center' : 'space-between', 
              alignItems: 'center',
              p: 1.5,
              borderBottom: '1px solid',
              borderColor: 'divider',
              backgroundColor: 'background.paper',
              position: 'sticky',
              top: 0,
              zIndex: 10,
              width: '100%'
            }}>
              {!isComponentLibraryCollapsed && (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography 
                    variant="subtitle1" 
                    fontWeight="bold" 
                    sx={{ cursor: 'pointer' }} 
                    onClick={() => setShowProjectList(!showProjectList)}
                  >
                    {showProjectList ? t('scada.editor.projectList') : t('scada.editor.componentLibrary')}
                  </Typography>
                  {!showProjectList && (
                    <IconButton 
                      size="small" 
                      onClick={() => setShowProjectList(true)}
                      sx={{ ml: 1 }}
                    >
                      <FolderIcon fontSize="small" />
                    </IconButton>
                  )}
                  {showProjectList && (
                    <IconButton 
                      size="small" 
                      onClick={() => setShowProjectList(false)}
                      sx={{ ml: 1 }}
                    >
                      <DashboardIcon fontSize="small" />
                    </IconButton>
                  )}
                </Box>
              )}
              <IconButton 
                size="small" 
                onClick={toggleComponentLibrary}
                sx={{ 
                  width: 36,
                  height: 36,
                  display: 'flex',
                  justifyContent: 'center',
                  alignItems: 'center',
                  borderRadius: '4px',
                  ml: isComponentLibraryCollapsed ? 'auto' : 0,
                  mr: isComponentLibraryCollapsed ? 'auto' : 0,
                  '&:hover': {
                    bgcolor: 'action.hover',
                  }
                }}
              >
                {isComponentLibraryCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
              </IconButton>
            </Box>
            
            {/* 可滚动的内容区域 */}
            <Box sx={{ 
              p: 1.5, 
              pt: 1, 
              flex: 1, 
              overflowY: 'auto', 
              overflowX: 'hidden' 
            }}>
              {isComponentLibraryCollapsed ? (
                // 折叠状态下的组件库
                renderCollapsedComponentLibrary()
              ) : (
                // 展开状态下的面板 - 根据状态显示项目列表或组件库
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  {showProjectList ? (
                    // 项目列表
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Typography variant="subtitle2" color="primary">
                          {t('scada.editor.myProjects')}
                        </Typography>
                        <IconButton size="small" onClick={handleAddClick} color="primary">
                          <AddIcon fontSize="small" />
                        </IconButton>
                      </Box>
                      
                      {isLoadingProjects ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
                          <CircularProgress size={24} />
                        </Box>
                      ) : (projectList && projectList.length > 0) ? (
                        <List dense sx={{ width: '100%', p: 0 }}>
                          {projectList.map((project) => (
                            <ListItem 
                              key={project.projectId}
                              sx={{ 
                                borderRadius: 1,
                                mb: 0.5,
                                bgcolor: currentProjectId === project.projectId ? 'rgba(25, 118, 210, 0.08)' : 'transparent',
                                borderLeft: currentProjectId === project.projectId ? '3px solid' : 'none',
                                borderColor: 'primary.main',
                                '&:hover': {
                                  bgcolor: 'action.hover',
                                }
                              }}
                            >
                              <ListItemIcon sx={{ minWidth: 36 }}>
                                <DashboardIcon fontSize="small" color={currentProjectId === project.projectId ? 'primary' : 'action'} />
                              </ListItemIcon>
                              <ListItemText 
                                primary={project.name} 
                                secondary={project.updatedAt ? new Date(project.updatedAt).toLocaleString() : ''}
                                sx={{ cursor: 'pointer' }}
                                onClick={() => selectProject(project)}
                              />
                              <IconButton 
                                edge="end" 
                                size="small"
                                onClick={(e) => handleOpenProjectMenu(e, project)}
                              >
                                <MoreVertIcon fontSize="small" />
                              </IconButton>
                            </ListItem>
                          ))}
                        </List>
                      ) : (
                        <Box sx={{ p: 2, textAlign: 'center' }}>
                          <Typography variant="body2" color="text.secondary">
                            {t('scada.editor.noProjects')}
                          </Typography>
                        </Box>
                      )}
                      
                      <Box sx={{ mt: 2 }}>
                        <Button 
                          fullWidth 
                          variant="outlined" 
                          startIcon={<DashboardIcon />}
                          onClick={() => setShowProjectList(false)}
                        >
                          {t('scada.editor.backToLibrary')}
                        </Button>
                      </Box>
                    </Box>
                  ) : (
                    // 组件库内容
                    renderExpandedComponentLibrary()
                  )}
                </Box>
              )}
            </Box>
          </Box>
        )}

        <Box sx={{ display: 'flex', flexDirection: 'column', flex: 1, overflow: 'hidden' }}>
          {/* 顶部工具栏 */}
          <Paper sx={{ 
            p: 1, 
            display: 'flex', 
            alignItems: 'center', 
            justifyContent: 'space-between', // 修改为两端对齐
            zIndex: 200,
            position: 'relative'
          }}>
            {/* 项目名称区域 */}
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography 
                variant="subtitle1" 
                component="div" 
                sx={{ 
                  fontWeight: 'medium',
                  mr: 1, 
                  cursor: 'pointer',
                  '&:hover': { textDecoration: 'underline' }
                }}
                onClick={() => {
                  const newName = prompt(t('scada.editor.projectName'), projectName);
                  if (newName && newName.trim()) {
                    setProjectName(newName.trim());
                  }
                }}
              >
                {projectName}
              </Typography>
              {saveSuccess && (
                <Typography variant="caption" color="success.main" sx={{ ml: 1 }}>
                  {t('scada.editor.saved')}
                </Typography>
              )}
            </Box>
            
            {/* 工具栏按钮 */}
            <Stack direction="row" spacing={1}>
              <Tooltip key="tooltip-save" title={isSaving ? t('scada.toolbar.saving') : t('scada.toolbar.save')}>
                <span>
                  <IconButton 
                    size="small" 
                    onClick={saveProject} 
                    disabled={isPreviewMode || isSaving || !currentProjectId}
                    color={saveSuccess && !saveError ? 'success' : 'default'}
                  >
                    {isSaving ? <CircularProgress size={20} /> : <SaveIcon />}
                  </IconButton>
                </span>
              </Tooltip>
              <Tooltip key="tooltip-preview" title={isPreviewMode ? t('scada.toolbar.edit') : t('scada.toolbar.preview')}>
                <span>
                  <IconButton 
                    size="small" 
                    onClick={togglePreviewMode} 
                    color={isPreviewMode ? "primary" : "default"}
                    disabled={!currentProjectId}
                  >
                    {isPreviewMode ? <StopIcon /> : <PlayArrowIcon />}
                  </IconButton>
                </span>
              </Tooltip>
              <Tooltip key="tooltip-new-window" title={t('scada.toolbar.newWindow')}>
                <span>
                  <IconButton 
                    size="small" 
                    onClick={() => {
                      if (currentProjectId) {
                        window.open(`/${currentProjectId}`, '_blank');
                      }
                    }}
                    disabled={!currentProjectId}
                  >
                    <OpenInNewIcon />
                  </IconButton>
                </span>
              </Tooltip>
              <Divider orientation="vertical" flexItem />
              <Tooltip key="tooltip-undo" title={t('scada.toolbar.undo')}>
                <span>
                  <IconButton size="small" onClick={undo} disabled={!canUndo || isPreviewMode}>
                    <UndoIcon color={canUndo && !isPreviewMode ? "inherit" : "disabled"} />
                  </IconButton>
                </span>
              </Tooltip>
              <Tooltip key="tooltip-redo" title={t('scada.toolbar.redo')}>
                <span>
                  <IconButton size="small" onClick={redo} disabled={!canRedo || isPreviewMode}>
                    <RedoIcon color={canRedo && !isPreviewMode ? "inherit" : "disabled"} />
                  </IconButton>
                </span>
              </Tooltip>
              <Tooltip key="tooltip-delete" title={t('scada.toolbar.delete')}>
                <IconButton size="small" onClick={deleteSelectedWidget} disabled={!selectedElement || isPreviewMode}>
                  <DeleteIcon />
                </IconButton>
              </Tooltip>
              <Divider orientation="vertical" flexItem />
              <Tooltip key="tooltip-grid" title={t('scada.toolbar.grid')}>
                <IconButton size="small" onClick={() => setShowGrid(!showGrid)} disabled={isPreviewMode}>
                  <GridOnIcon color={showGrid ? 'primary' : 'inherit'} />
                </IconButton>
              </Tooltip>
              <Tooltip key="tooltip-zoom-in" title={t('scada.toolbar.zoomIn')}>
                <IconButton size="small" onClick={zoomIn}>
                  <ZoomInIcon />
                </IconButton>
              </Tooltip>
              <Tooltip key="tooltip-zoom-out" title={t('scada.toolbar.zoomOut')}>
                <IconButton size="small" onClick={zoomOut}>
                  <ZoomOutIcon />
                </IconButton>
              </Tooltip>
              <Tooltip key="tooltip-reset-zoom" title={t('scada.toolbar.resetZoom')}>
                <IconButton size="small" onClick={resetZoom}>
                  <AspectRatioIcon />
                </IconButton>
              </Tooltip>
            </Stack>
          </Paper>

          {/* 中间画布区域 */}
          {currentProjectId ? (
            <Box 
              sx={{ 
                flex: 1, 
                position: 'relative',
                transform: `scale(${zoom})`,
                transformOrigin: '0 0',
                width: `${100 / zoom}%`,
                height: `${100 / zoom}%`,
                overflow: 'auto',
                cursor: selectedComponentType ? 'crosshair' : 'default', // 当选中组件类型时更改光标样式
                userSelect: 'none', // 防止拖动时选中文本
                WebkitUserSelect: 'none',
                MozUserSelect: 'none',
                msUserSelect: 'none'
              }}
              onMouseDown={handleCanvasMouseDown}
              onMouseMove={handleCanvasMouseMove}
              onMouseUp={handleCanvasMouseUp}
              ref={canvasRef}
            >
              <div 
                style={{ 
                  transform: `scale(${zoom})`,
                  transformOrigin: '0 0',
                  width: '3000px',
                  height: '2000px'
                }}
              >
                <HMICanvas 
                  widgets={widgets} 
                  showGrid={showGrid} 
                  selectedElement={selectedElement} 
                  onWidgetSelect={isPreviewMode ? () => {} : handleWidgetSelect} 
                  onLayoutChange={handleLayoutChange}
                  isPreviewMode={isPreviewMode}
                  gridSize={10} // 添加网格大小参数，配置为10像素
                />
                
                {/* 拖框创建的临时矩形 */}
                {creatingWidgetRect && (
                  <div
                    style={{
                      position: 'absolute',
                      left: `${creatingWidgetRect.x}px`,
                      top: `${creatingWidgetRect.y}px`,
                      width: `${creatingWidgetRect.width}px`,
                      height: `${creatingWidgetRect.height}px`,
                      border: '2px dashed #1976d2',
                      backgroundColor: 'rgba(25, 118, 210, 0.1)',
                      pointerEvents: 'none',
                      zIndex: 9999
                    }}
                  />
                )}
              </div>
            </Box>
          ) : (
            // 无项目时显示欢迎页面
            <Box sx={{ 
              display: 'flex', 
              flexDirection: 'column', 
              alignItems: 'center', 
              justifyContent: 'center', 
              height: '100%', 
              p: 3,
              flex: 1
            }}>
              <DashboardIcon sx={{ fontSize: 80, color: 'primary.main', mb: 2, opacity: 0.7 }} />
              <Typography variant="h5" gutterBottom>{t('scada.editor.welcome')}</Typography>
              <Typography variant="body1" color="text.secondary" align="center" paragraph>
                {t('scada.editor.welcomeMessage')}
              </Typography>
              <Button 
                variant="contained" 
                startIcon={<AddIcon />} 
                onClick={handleToolbarNewProject}
                sx={{ mt: 2 }}
              >
                {t('scada.editor.newProject')}
              </Button>
            </Box>
          )}
        </Box>

        {/* 右侧属性面板 - 只在编辑模式下显示，且需要有选中的组件 */}
        {!isPreviewMode && selectedElement && (
          <PropertiesPanel 
            selectedElement={selectedElement} 
            onPropertyChange={updateWidgetProperty}
            onClose={clearSelection}
            availableTopics={availableTopics}
            isLoadingTopics={isLoadingTopics}
          />
        )}
      </Box>
      
      {/* 保存反馈提示 */}
      <Snackbar 
        open={openSnackbar} 
        autoHideDuration={5000} 
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={saveError ? 'error' : 'success'} 
          sx={{ width: '100%' }}
        >
          {saveError || t('scada.editor.operationSuccess')}
        </Alert>
      </Snackbar>
      
      {/* 项目菜单 */}
      <Menu
        anchorEl={projectMenuAnchor}
        open={Boolean(projectMenuAnchor)}
        onClose={handleCloseProjectMenu}
      >
        <MenuItem onClick={() => {
          if (selectedProjectForMenu) {
            selectProject(selectedProjectForMenu);
            handleCloseProjectMenu();
          }
        }}>
          <ListItemIcon>
            <DashboardIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>{t('scada.editor.openProject')}</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => {
          if (selectedProjectForMenu) {
            handleDeleteProject(selectedProjectForMenu);
          }
        }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>{t('scada.editor.deleteProject')}</ListItemText>
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default HMIEditor; 