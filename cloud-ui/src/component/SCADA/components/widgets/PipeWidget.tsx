import React, { useEffect, useRef, useCallback, useMemo } from 'react';
import { Box } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface PipeWidgetProps {
  flowDirection: string;
  flowSpeed: number;
  flowActive: boolean;
  pipeColor: string;
  fluidColor: string;
  topicValue?: MqttValue | null;
}

export const PipeWidget: React.FC<PipeWidgetProps> = ({
  flowDirection = 'right',
  flowSpeed = 5,
  flowActive = true,
  pipeColor = '#666666',
  fluidColor = '#2196f3',
  topicValue = null
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const requestRef = useRef<number>(0);
  const offsetRef = useRef<number>(0);
  
  // 根据MQTT数据确定管道是否激活
  const actualFlowActive = useMemo(() => {
    if (topicValue) {
      if (topicValue.dataType === 'boolean') {
        return Boolean(topicValue.value);
      } else if (topicValue.dataType === 'number') {
        return Number(topicValue.value) > 0;
      } else if (topicValue.dataType === 'string' && typeof topicValue.value === 'string') {
        const val = topicValue.value.toLowerCase();
        return val === 'true' || val === 'on' || val === '1';
      }
    }
    return flowActive;
  }, [topicValue, flowActive]);
  
  // 计算动画方向和速度
  const getDirectionMultiplier = useCallback(() => {
    switch (flowDirection) {
      case 'left': return -1;
      case 'right': return 1;
      case 'up': return -1;
      case 'down': return 1;
      default: return 1;
    }
  }, [flowDirection]);
  
  // 判断是水平还是垂直流向
  const isHorizontal = useCallback(() => {
    return flowDirection === 'left' || flowDirection === 'right';
  }, [flowDirection]);
  
  // 渲染管道和流体
  const renderPipe = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const horizontal = isHorizontal();
    const length = horizontal ? canvas.width : canvas.height;
    const thickness = horizontal ? canvas.height : canvas.width;
    
    // 绘制管道背景
    ctx.fillStyle = pipeColor;
    ctx.fillRect(0, 0, canvas.width, canvas.height);
    
    // 如果流动动画未激活，则不绘制流体
    if (!actualFlowActive) return;
    
    // 获取方向乘数
    const directionMultiplier = getDirectionMultiplier();
    
    // 绘制流体
    ctx.fillStyle = fluidColor;
    
    const dashLength = 20; // 流体段长度
    const spaceLength = 10; // 流体段间距
    const totalLength = dashLength + spaceLength;
    
    // 流体偏移量（用于动画）
    const offset = offsetRef.current;
    
    // 计算有多少个流体段
    const dashCount = Math.ceil(length / totalLength) + 1;
    
    // 绘制每个流体段
    for (let i = -1; i < dashCount; i++) {
      let position;
      
      if (horizontal) {
        position = i * totalLength + (offset * directionMultiplier) % totalLength;
        ctx.fillRect(position, 0, dashLength, thickness);
      } else {
        position = i * totalLength + (offset * directionMultiplier) % totalLength;
        ctx.fillRect(0, position, thickness, dashLength);
      }
    }
  }, [flowDirection, actualFlowActive, pipeColor, fluidColor, isHorizontal, getDirectionMultiplier]);
  
  // 动画循环
  const animate = useCallback(() => {
    if (actualFlowActive) {
      // 更新偏移量来制造动画效果
      offsetRef.current = (offsetRef.current + (flowSpeed * 0.1)) % 100;
    }
    
    renderPipe();
    requestRef.current = requestAnimationFrame(animate);
  }, [actualFlowActive, flowSpeed, renderPipe]);
  
  // 初始化画布和动画
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    // 设置画布尺寸
    const resizeCanvas = () => {
      const parent = canvas.parentElement;
      if (parent) {
        canvas.width = parent.clientWidth;
        canvas.height = parent.clientHeight;
        renderPipe();
      }
    };
    
    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);
    
    // 启动动画循环
    if (actualFlowActive) {
      requestRef.current = requestAnimationFrame(animate);
    }
    
    // 清理函数
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [renderPipe, animate, actualFlowActive]);
  
  // 属性变化时重新渲染
  useEffect(() => {
    renderPipe();
  }, [renderPipe]);
  
  // 监听属性变化启动或停止动画
  useEffect(() => {
    // 如果flowActive变化，需要重新启动或停止动画
    if (actualFlowActive) {
      // 如果已经有动画正在运行，先取消
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
      // 启动新的动画
      requestRef.current = requestAnimationFrame(animate);
    } else {
      // 停止动画
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
      // 重新渲染静态管道
      renderPipe();
    }
    
    // 清理函数
    return () => {
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }
    };
  }, [actualFlowActive, animate, renderPipe]);
  
  return (
    <Box sx={{ width: '100%', height: '100%', overflow: 'hidden' }}>
      <canvas 
        ref={canvasRef} 
        style={{ 
          width: '100%', 
          height: '100%', 
          display: 'block' 
        }}
      />
    </Box>
  );
};

export default PipeWidget; 