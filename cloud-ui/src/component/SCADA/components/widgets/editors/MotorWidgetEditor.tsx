import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorC<PERSON>r,
  TextInputField,
  SelectField,
  SwitchField,
  SliderField
} from './EditorComponents';

const MotorWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();
  
  const rotationDirectionOptions = [
    { value: 'clockwise', label: t('scada.properties.components.clockwise') },
    { value: 'counterclockwise', label: t('scada.properties.components.counterclockwise') }
  ];

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.motor')}>
      <TextInputField
        label={t('scada.properties.common.label')}
        value={properties.label || t('scada.components.types.motor')}
        property="label"
        onChange={handleInputChange}
      />
      
      <SwitchField
        label={t('scada.properties.common.runningState')}
        checked={properties.running || false}
        property="running"
        onChange={handleInputChange}
      />
      
      <SliderField
        label={t('scada.properties.common.speed')}
        value={properties.speed === undefined ? 50 : properties.speed}
        min={0}
        max={100}
        step={5}
        property="speed"
        onChange={handleInputChange}
      />
      
      <SelectField
        label={t('scada.properties.components.rotationDirection')}
        value={properties.direction || 'clockwise'}
        property="direction"
        onChange={handleInputChange}
        options={rotationDirectionOptions}
      />
    </EditorContainer>
  );
};

export default MotorWidgetEditor; 