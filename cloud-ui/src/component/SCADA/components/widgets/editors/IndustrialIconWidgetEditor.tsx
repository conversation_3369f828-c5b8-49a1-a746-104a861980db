import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  ColorField,
  SliderField,
  SelectField
} from './EditorComponents';

const IndustrialIconWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();
  
  const iconOptions = [
    { value: 'pump', label: t('scada.components.icons.pump') },
    { value: 'valve', label: t('scada.components.icons.valve') },
    { value: 'tank', label: t('scada.components.icons.tank') },
    { value: 'motor', label: t('scada.components.icons.motor') },
    { value: 'fan', label: t('scada.components.icons.fan') },
    { value: 'heater', label: t('scada.components.icons.heater') },
    { value: 'filter', label: t('scada.components.icons.filter') },
    { value: 'reactor', label: t('scada.components.icons.reactor') },
    { value: 'sensor', label: t('scada.components.types.sensor') },
    { value: 'compressor', label: t('scada.components.icons.compressor') },
    { value: 'battery', label: t('scada.components.icons.battery') },
    { value: 'gauge', label: t('scada.components.icons.gauge') },
    { value: 'controlValve', label: t('scada.components.icons.controlValve') },
    { value: 'switch', label: t('scada.components.icons.switch') },
    { value: 'transformer', label: t('scada.components.icons.transformer') },
    { value: 'pipeConnection', label: t('scada.components.icons.pipeConnection') }
  ];

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.industrialIcon')}>
      <SelectField
        label={t('scada.properties.components.iconType')}
        value={properties.iconType || 'pump'}
        property="iconType"
        onChange={handleInputChange}
        options={iconOptions}
      />
      <ColorField
        label={t('scada.properties.components.iconColor')}
        value={properties.color || '#333333'}
        property="color"
        onChange={handleInputChange}
      />
      <SliderField
        label={t('scada.properties.common.rotation')}
        value={properties.rotation === undefined ? 0 : properties.rotation}
        property="rotation"
        onChange={handleInputChange}
        min={0}
        max={360}
        step={15}
      />
    </EditorContainer>
  );
};

export default IndustrialIconWidgetEditor; 