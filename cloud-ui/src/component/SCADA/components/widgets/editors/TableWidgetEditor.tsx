import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  NumberInputField,
  SwitchField,
  SelectField
} from './EditorComponents';

const TableWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  const densityOptions = [
    { value: 'small', label: t('scada.properties.common.compact') },
    { value: 'medium', label: t('scada.properties.common.standard') },
    { value: 'large', label: t('scada.properties.common.comfortable') }
  ];

  const paginationPositionOptions = [
    { value: 'bottom', label: t('scada.properties.components.bottom') },
    { value: 'top', label: t('scada.properties.components.top') },
    { value: 'both', label: t('scada.properties.components.both') },
    { value: 'none', label: t('scada.properties.components.none') }
  ];

  return (
    <EditorContainer title={t('scada.properties.components.table')}>
      <TextInputField
        label={t('scada.properties.common.title')}
        value={properties.title || t('scada.components.types.table')}
        property="title"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.components.rowsPerPage')}
        value={properties.pageSize || 10}
        property="pageSize"
        onChange={handleInputChange}
        min={5}
        max={100}
        step={5}
      />
      <SelectField
        label={t('scada.properties.components.density')}
        value={properties.density || 'medium'}
        property="density"
        onChange={handleInputChange}
        options={densityOptions}
      />
      <SelectField
        label={t('scada.properties.components.paginationPosition')}
        value={properties.paginationPosition || 'bottom'}
        property="paginationPosition"
        onChange={handleInputChange}
        options={paginationPositionOptions}
      />
      <SwitchField
        label={t('scada.properties.components.showIndex')}
        checked={properties.showIndex === undefined ? true : !!properties.showIndex}
        property="showIndex"
        onChange={handleInputChange}
      />
      <SwitchField
        label={t('scada.properties.components.sortable')}
        checked={properties.sortable === undefined ? true : !!properties.sortable}
        property="sortable"
        onChange={handleInputChange}
      />
      <SwitchField
        label={t('scada.properties.components.showTitle')}
        checked={properties.showTitle === undefined ? true : !!properties.showTitle}
        property="showTitle"
        onChange={handleInputChange}
      />
      <SwitchField
        label={t('scada.properties.components.selectable')}
        checked={properties.selectable === undefined ? false : !!properties.selectable}
        property="selectable"
        onChange={handleInputChange}
      />
    </EditorContainer>
  );
};

export default TableWidgetEditor; 