import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorC<PERSON>r,
  TextInputField,
  NumberInputField,
  ColorField,
  SwitchField
} from './EditorComponents';

const GaugeWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.gauge')}>
      <TextInputField
        label={t('scada.properties.common.title')}
        value={properties.title || t('scada.components.types.gauge')}
        property="title"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.minValue')}
        value={properties.min === undefined ? 0 : properties.min}
        property="min"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.maxValue')}
        value={properties.max === undefined ? 100 : properties.max}
        property="max"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.currentValue')}
        value={properties.value === undefined ? 0 : properties.value}
        property="value"
        onChange={handleInputChange}
      />
      <TextInputField
        label={t('scada.properties.common.unit')}
        value={properties.unit === undefined ? '%' : properties.unit}
        property="unit"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.precision')}
        value={properties.precision === undefined ? 0 : properties.precision}
        property="precision"
        onChange={handleInputChange}
        min={0}
        max={5}
      />
      <SwitchField
        label={t('scada.properties.common.showValue')}
        checked={properties.showValue === undefined ? true : properties.showValue}
        property="showValue"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.common.color')}
        value={properties.color || '#2196f3'}
        property="color"
        onChange={handleInputChange}
      />
    </EditorContainer>
  );
};

export default GaugeWidgetEditor; 