import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  NumberInputField,
  ColorField
} from './EditorComponents';

const TankWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.tank')}>
      <TextInputField
        label={t('scada.properties.common.title')}
        value={properties.title || t('scada.components.types.tank')}
        property="title"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.minValue')}
        value={properties.min === undefined ? 0 : properties.min}
        property="min"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.maxValue')}
        value={properties.max === undefined ? 100 : properties.max}
        property="max"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.currentValue')}
        value={properties.value === undefined ? 0 : properties.value}
        property="value"
        onChange={handleInputChange}
      />
      <TextInputField
        label={t('scada.properties.common.unit')}
        value={properties.unit === undefined ? 'L' : properties.unit}
        property="unit"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.common.fillColor')}
        value={properties.fillColor || '#3f51b5'}
        property="fillColor"
        onChange={handleInputChange}
      />
    </EditorContainer>
  );
};

export default TankWidgetEditor; 