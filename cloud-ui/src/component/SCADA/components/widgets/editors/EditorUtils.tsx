import React from 'react';
import { MuiColorInput } from 'mui-color-input';

// 处理文本输入变化
export const handleTextInputChange = (
  e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>, 
  property: string,
  onPropertyChange: (property: string, value: any) => void
) => {
  onPropertyChange(property, e.target.value);
};

// 处理数字输入变化
export const handleNumberInputChange = (
  e: React.ChangeEvent<HTMLInputElement>, 
  property: string,
  onPropertyChange: (property: string, value: any) => void
) => {
  const inputValue = e.target.value;
  
  if (inputValue === '') {
    onPropertyChange(property, '');
  } else {
    const numValue = parseFloat(inputValue);
    if (!isNaN(numValue)) {
      onPropertyChange(property, numValue);
    }
  }
};

// 处理选择变化
export const handleSelectChange = (
  e: any, 
  property: string,
  onPropertyChange: (property: string, value: any) => void
) => {
  onPropertyChange(property, e.target.value);
};

// 处理开关变化
export const handleSwitchChange = (
  e: React.ChangeEvent<HTMLInputElement>, 
  property: string,
  onPropertyChange: (property: string, value: any) => void
) => {
  onPropertyChange(property, e.target.checked);
};

// 处理滑块变化
export const handleSliderChange = (
  value: number, 
  property: string,
  onPropertyChange: (property: string, value: any) => void
) => {
  onPropertyChange(property, value);
};

// 处理颜色变化
export const handleColorChange = (
  color: string, 
  property: string,
  onPropertyChange: (property: string, value: any) => void
) => {
  onPropertyChange(property, color);
};

// 将文件转换为Base64
export const fileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });
};

// 颜色选择器组件
export const ColorInputField: React.FC<{
  label: string;
  value: string;
  defaultValue?: string;
  onChange: (color: string) => void;
}> = ({ label, value, defaultValue = '#000000', onChange }) => (
  <MuiColorInput
    format="hex"
    size="small"
    label={label}
    value={value || defaultValue}
    onChange={onChange}
    fullWidth
    margin="dense"
  />
); 