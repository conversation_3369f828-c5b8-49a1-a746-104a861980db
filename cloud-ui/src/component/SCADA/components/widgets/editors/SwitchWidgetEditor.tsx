import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  SwitchField,
  ColorField
} from './EditorComponents';

const SwitchWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.switch')}>
      <TextInputField
        label={t('scada.properties.common.label')}
        value={properties.label || t('scada.components.types.switch')}
        property="label"
        onChange={handleInputChange}
      />
      <SwitchField
        label={t('scada.properties.components.currentState')}
        checked={properties.state || false}
        property="state"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.components.onColor')}
        value={properties.onColor || '#4caf50'}
        property="onColor"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.components.offColor')}
        value={properties.offColor || '#f44336'}
        property="offColor"
        onChange={handleInputChange}
      />
    </EditorContainer>
  );
};

export default SwitchWidgetEditor; 