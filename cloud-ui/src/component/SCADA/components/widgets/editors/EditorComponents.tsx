import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Slider,
  Stack
} from '@mui/material';
import { MuiColorInput } from 'mui-color-input';
import { handleTextInputChange, handleSelectChange, handleSwitchChange } from './EditorUtils';

// 通用编辑器组件标题
export const EditorSectionTitle: React.FC<{title: string}> = ({ title }) => (
  <Typography variant="subtitle2" className="property-title">{title}</Typography>
);

// 文本输入字段
export const TextInputField: React.FC<{
  label: string;
  value: string;
  property: string;
  onChange: (property: string, value: any) => void;
  placeholder?: string;
  multiline?: boolean;
  rows?: number;
}> = ({ label, value, property, onChange, placeholder, multiline = false, rows = 1 }) => (
  <TextField
    size="small"
    label={label}
    value={value || ''}
    onChange={(e) => handleTextInputChange(e, property, onChange)}
    fullWidth
    margin="dense"
    placeholder={placeholder}
    multiline={multiline}
    rows={rows}
  />
);

// 数字输入字段
export const NumberInputField: React.FC<{
  label: string;
  value: number | string;
  property: string;
  onChange: (property: string, value: any) => void;
  min?: number;
  max?: number;
  step?: number;
}> = ({ label, value, property, onChange, min, max, step }) => {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    if (inputValue === '') {
      onChange(property, '');
    } else {
      const numValue = parseFloat(inputValue);
      if (!isNaN(numValue)) {
        onChange(property, numValue);
      }
    }
  };

  return (
    <TextField
      size="small"
      label={label}
      type="number"
      value={value === '' ? '' : (value ?? 0)}
      onChange={handleChange}
      fullWidth
      margin="dense"
      InputProps={{ inputProps: { min, max, step } }}
    />
  );
};

// 选择字段
export const SelectField: React.FC<{
  label: string;
  value: string;
  property: string;
  onChange: (property: string, value: any) => void;
  options: Array<{value: string, label: string}>;
}> = ({ label, value, property, onChange, options }) => (
  <FormControl fullWidth margin="dense" size="small">
    <InputLabel>{label}</InputLabel>
    <Select
      value={value || options[0].value}
      label={label}
      onChange={(e) => handleSelectChange(e, property, onChange)}
    >
      {options.map(option => (
        <MenuItem key={option.value} value={option.value}>{option.label}</MenuItem>
      ))}
    </Select>
  </FormControl>
);

// 开关字段
export const SwitchField: React.FC<{
  label: string;
  checked: boolean;
  property: string;
  onChange: (property: string, value: any) => void;
}> = ({ label, checked, property, onChange }) => (
  <FormControlLabel
    control={
      <Switch
        checked={checked}
        onChange={(e) => handleSwitchChange(e, property, onChange)}
      />
    }
    label={label}
  />
);

// 滑块字段
export const SliderField: React.FC<{
  label: string;
  value: number | string;
  property: string;
  onChange: (property: string, value: any) => void;
  min?: number;
  max?: number;
  step?: number;
}> = ({ label, value, property, onChange, min = 0, max = 100, step = 1 }) => {
  const { t } = useTranslation();

  // 处理滑块的值，确保它是数字类型或默认为min
  const numericValue = value === '' || value === undefined || value === null 
    ? min
    : (typeof value === 'string' ? parseFloat(value) || min : value);
  
  // 清除值的函数
  const handleClear = () => {
    onChange(property, '');
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
        <Typography variant="body2">{label}</Typography>
        <Typography 
          variant="caption" 
          sx={{ 
            cursor: 'pointer', 
            color: 'primary.main',
            '&:hover': { textDecoration: 'underline' }
          }}
          onClick={handleClear}
        >
          {t('common.clear')}
        </Typography>
      </Box>
      <Slider
        value={numericValue}
        onChange={(_, newValue) => onChange(property, newValue as number)}
        step={step}
        min={min}
        max={max}
        valueLabelDisplay="auto"
      />
      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Typography variant="caption" color="text.secondary">
          {min}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          {max}
        </Typography>
      </Box>
    </Box>
  );
};

// 颜色选择器
export const ColorField: React.FC<{
  label: string;
  value: string;
  property: string;
  onChange: (property: string, value: any) => void;
  defaultColor?: string;
}> = ({ label, value, property, onChange, defaultColor = '#000000' }) => (
  <MuiColorInput
    format="hex"
    size="small"
    label={label}
    value={value || defaultColor}
    onChange={(color) => onChange(property, color)}
    fullWidth
    margin="dense"
  />
);

// 编辑器容器
export const EditorContainer: React.FC<{
  title: string;
  children: React.ReactNode;
}> = ({ title, children }) => (
  <Box className="property-section">
    <EditorSectionTitle title={title} />
    <Stack spacing={2} sx={{ mt: 2 }}>
      {children}
    </Stack>
  </Box>
); 