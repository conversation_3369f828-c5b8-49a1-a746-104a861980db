import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  NumberInputField,
  SelectField,
  ColorField
} from './EditorComponents';

const TextWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  const alignmentOptions = [
    { value: 'left', label: t('scada.properties.common.leftAlign') },
    { value: 'center', label: t('scada.properties.common.center') },
    { value: 'right', label: t('scada.properties.common.rightAlign') }
  ];

  return (
    <EditorContainer title={t('scada.properties.components.text')}>
      <TextInputField
        label={t('scada.properties.components.textContent')}
        value={properties.text || ''}
        property="text"
        onChange={handleInputChange}
        multiline
        rows={2}
      />
      <NumberInputField
        label={t('scada.properties.components.fontSize')}
        value={properties.fontSize || 16}
        property="fontSize"
        onChange={handleInputChange}
        min={8}
        max={72}
      />
      <ColorField
        label={t('scada.properties.components.textColor')}
        value={properties.color || '#000000'}
        property="color"
        onChange={handleInputChange}
      />
      <SelectField
        label={t('scada.properties.components.alignment')}
        value={properties.alignment || 'left'}
        property="alignment"
        onChange={handleInputChange}
        options={alignmentOptions}
      />
    </EditorContainer>
  );
};

export default TextWidgetEditor; 