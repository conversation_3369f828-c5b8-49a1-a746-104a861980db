import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  NumberInputField,
  SelectField,
  SwitchField
} from './EditorComponents';

const ProgressBarWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  const colorOptions = [
    { value: 'primary', label: t('scada.properties.components.primary') },
    { value: 'secondary', label: t('scada.properties.components.secondary') },
    { value: 'success', label: t('scada.properties.components.success') },
    { value: 'warning', label: t('scada.properties.components.warning') },
    { value: 'error', label: t('scada.properties.components.error') },
    { value: 'info', label: t('scada.properties.components.info') }
  ];

  const orientationOptions = [
    { value: 'horizontal', label: t('scada.properties.common.horizontal') },
    { value: 'vertical', label: t('scada.properties.common.vertical') }
  ];

  return (
    <EditorContainer title={t('scada.properties.components.progressbar')}>
      <NumberInputField
        label={t('scada.properties.common.minValue')}
        value={properties.min || 0}
        property="min"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.maxValue')}
        value={properties.max || 100}
        property="max"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.currentValue')}
        value={properties.value || 65}
        property="value"
        onChange={handleInputChange}
      />
      <SwitchField
        label={t('scada.properties.common.showValue')}
        checked={properties.showValue === undefined ? true : !!properties.showValue}
        property="showValue"
        onChange={handleInputChange}
      />
      <SelectField
        label={t('scada.properties.common.color')}
        value={properties.color || 'primary'}
        property="color"
        onChange={handleInputChange}
        options={colorOptions}
      />
      <SelectField
        label={t('scada.properties.common.orientation')}
        value={properties.orientation || 'horizontal'}
        property="orientation"
        onChange={handleInputChange}
        options={orientationOptions}
      />
    </EditorContainer>
  );
};

export default ProgressBarWidgetEditor;