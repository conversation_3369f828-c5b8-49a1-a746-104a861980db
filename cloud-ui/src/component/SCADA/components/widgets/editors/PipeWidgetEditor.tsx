import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  SelectField,
  SliderField,
  ColorField
} from './EditorComponents';

const PipeWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  const flowDirectionOptions = [
    { value: 'right', label: t('scada.properties.components.right') },
    { value: 'left', label: t('scada.properties.components.left') },
    { value: 'up', label: t('scada.properties.components.up') },
    { value: 'down', label: t('scada.properties.components.down') }
  ];

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.pipe')}>
      <SelectField
        label={t('scada.properties.components.flowDirection')}
        value={properties.flowDirection || 'right'}
        property="flowDirection"
        onChange={handleInputChange}
        options={flowDirectionOptions}
      />
      <SliderField
        label={t('scada.properties.components.flowSpeed')}
        value={properties.flowSpeed === undefined ? 5 : properties.flowSpeed}
        property="flowSpeed"
        min={0}
        max={10}
        step={0.5}
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.components.pipeColor')}
        value={properties.pipeColor || '#aaaaaa'}
        property="pipeColor"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.components.fluidColor')}
        value={properties.fluidColor || '#3f51b5'}
        property="fluidColor"
        onChange={handleInputChange}
      />
    </EditorContainer>
  );
};

export default PipeWidgetEditor; 