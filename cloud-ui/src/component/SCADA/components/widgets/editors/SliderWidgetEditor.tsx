import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  NumberInputField,
  SelectField,
  SwitchField
} from './EditorComponents';

const SliderWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();
  
  const orientationOptions = [
    { value: 'horizontal', label: t('scada.properties.common.horizontal') },
    { value: 'vertical', label: t('scada.properties.common.vertical') }
  ];

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.slider')}>
      <NumberInputField
        label={t('scada.properties.common.minValue')}
        value={properties.min === '' ? '' : (properties.min ?? 0)}
        property="min"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.maxValue')}
        value={properties.max === '' ? '' : (properties.max ?? 100)}
        property="max"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.currentValue')}
        value={properties.value === '' ? '' : (properties.value ?? 50)}
        property="value"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.components.step')}
        value={properties.step === '' ? '' : (properties.step ?? 0.1)}
        property="step"
        onChange={handleInputChange}
        min={0.001}
        max={100}
        step={0.001}
      />
      <TextInputField
        label={t('scada.properties.common.unit')}
        value={properties.unit === undefined ? '%' : properties.unit}
        property="unit"
        onChange={handleInputChange}
      />
      <SwitchField
        label={t('scada.properties.common.showLabels')}
        checked={properties.showLabels === undefined ? true : !!properties.showLabels}
        property="showLabels"
        onChange={handleInputChange}
      />
      <SelectField
        label={t('scada.properties.common.orientation')}
        value={properties.orientation || 'horizontal'}
        property="orientation"
        onChange={handleInputChange}
        options={orientationOptions}
      />
    </EditorContainer>
  );
};

export default SliderWidgetEditor; 