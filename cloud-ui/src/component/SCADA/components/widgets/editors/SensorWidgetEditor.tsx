import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  NumberInputField,
  SelectField
} from './EditorComponents';

const SensorWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();
  
  const sensorTypeOptions = [
    { value: 'default', label: t('scada.properties.components.genericSensor') },
    { value: 'temperature', label: t('scada.properties.components.temperatureSensor') },
    { value: 'pressure', label: t('scada.properties.components.pressureSensor') },
    { value: 'flow', label: t('scada.properties.components.flowSensor') },
    { value: 'power', label: t('scada.properties.components.powerSensor') }
  ];

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.sensor')}>
      <TextInputField
        label={t('scada.properties.common.title')}
        value={properties.title || t('scada.components.types.sensor')}
        property="title"
        onChange={handleInputChange}
      />
      <TextInputField
        label={t('scada.properties.common.currentValue')}
        value={properties.value || '25.5'}
        property="value"
        onChange={handleInputChange}
      />
      <TextInputField
        label={t('scada.properties.common.unit')}
        value={properties.unit === undefined ? '°C' : properties.unit}
        property="unit"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.common.precision')}
        value={properties.precision === undefined ? 1 : properties.precision}
        property="precision"
        onChange={handleInputChange}
        min={0}
        max={5}
      />
      <SelectField
        label={t('scada.properties.components.sensorType')}
        value={properties.type || 'default'}
        property="type"
        onChange={handleInputChange}
        options={sensorTypeOptions}
      />
    </EditorContainer>
  );
};

export default SensorWidgetEditor; 