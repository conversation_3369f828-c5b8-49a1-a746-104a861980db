import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  SelectField,
  NumberInputField
} from './EditorComponents';

const ChartWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  const chartTypeOptions = [
    { value: 'line', label: t('scada.properties.components.lineChart') },
    { value: 'bar', label: t('scada.properties.components.barChart') },
    { value: 'area', label: t('scada.properties.components.areaChart') }
  ];

  return (
    <EditorContainer title={t('scada.properties.components.chart')}>
      <TextInputField
        label={t('scada.properties.common.title')}
        value={properties.title || t('scada.components.types.chart')}
        property="title"
        onChange={handleInputChange}
      />
      <SelectField
        label={t('scada.properties.components.chartType')}
        value={properties.chartType || 'line'}
        property="chartType"
        onChange={handleInputChange}
        options={chartTypeOptions}
      />
      <TextInputField
        label={t('scada.properties.components.dataSource')}
        value={properties.dataSource || ''}
        property="dataSource"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.components.maxDataPoints')}
        value={properties.maxPoints === undefined ? 50 : properties.maxPoints}
        property="maxPoints"
        onChange={handleInputChange}
        min={10}
        max={500}
        step={10}
      />
    </EditorContainer>
  );
};

export default ChartWidgetEditor; 