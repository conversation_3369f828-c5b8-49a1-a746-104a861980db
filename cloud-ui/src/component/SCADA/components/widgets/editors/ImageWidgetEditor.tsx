import React from 'react';
import {
  <PERSON>,
  Typography,
  Button,
  Input,
  TextField
} from '@mui/material';
import CloudUploadIcon from '@mui/icons-material/CloudUpload';
import { WidgetEditorProps } from './EditorProps';
import { fileToBase64 } from './EditorUtils';
import { useTranslation } from 'react-i18next';
import { EditorContainer } from './EditorComponents';

const ImageWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  // 处理输入变化，允许清空值
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onPropertyChange('src', e.target.value);
  };

  const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) {
      return;
    }

    const file = e.target.files[0];
    
    if (file.size > 5 * 1024 * 1024) {
      alert(t('scada.properties.fileSizeLimit', { size: '5MB' }));
      return;
    }

    if (!file.type.startsWith('image/')) {
      alert(t('scada.properties.onlyImageFiles'));
      return;
    }

    const checkImageDimensions = (file: File): Promise<boolean> => {
      return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          const isValid = img.width <= 1920 && img.height <= 1080;
          if (!isValid) {
            alert(t('scada.properties.imageSizeLimit', { width: 1920, height: 1080 }));
          }
          resolve(isValid);
        };
        img.src = URL.createObjectURL(file);
      });
    };

    const isDimensionsValid = await checkImageDimensions(file);
    if (!isDimensionsValid) {
      return;
    }

    try {
      const base64Data = await fileToBase64(file);
      onPropertyChange('src', base64Data);
    } catch (error) {
      console.error(t('scada.properties.imageProcessError'), error);
      alert(t('scada.properties.uploadError'));
    }
  };

  return (
    <EditorContainer title={t('scada.components.types.image')}>
      <TextField
        size="small"
        label={t('scada.properties.imageUrl')}
        value={properties.src || ''}
        onChange={handleInputChange}
        fullWidth
        margin="dense"
        placeholder={t('scada.properties.imageUrlPlaceholder')}
        helperText={t('scada.properties.imageHelperText')}
      />
      
      <Box sx={{ mt: 1, display: 'flex', justifyContent: 'center' }}>
        <Button
          component="label"
          variant="outlined"
          startIcon={<CloudUploadIcon />}
          sx={{ mb: 1 }}
        >
          {t('scada.properties.uploadImage')}
          <Input
            type="file"
            inputProps={{
              accept: 'image/*'
            }}
            sx={{ display: 'none' }}
            onChange={handleFileUpload}
          />
        </Button>
      </Box>
      
      <Box sx={{ 
        p: 2, 
        border: '1px dashed #bdbdbd', 
        borderRadius: '4px',
        bgcolor: '#f5f5f5',
        textAlign: 'center'
      }}>
        <Typography variant="body2" color="textSecondary" gutterBottom>
          {t('scada.properties.preview')}
        </Typography>
        {properties.src ? (
          <Box sx={{ 
            width: '100%', 
            height: 100,
            overflow: 'hidden',
            borderRadius: '4px',
            bgcolor: 'background.paper'
          }}>
            <img 
              src={properties.src}
              alt={t('scada.properties.preview')}
              draggable="false"
              style={{ 
                width: '100%', 
                height: '100%', 
                objectFit: 'fill'
              }}
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
          </Box>
        ) : (
          <Typography variant="caption" color="textSecondary">
            {t('scada.properties.noImage')}
          </Typography>
        )}
      </Box>
      <Typography variant="caption" color="textSecondary">
        {t('scada.properties.imageUploadTip')}
      </Typography>
    </EditorContainer>
  );
};

export default ImageWidgetEditor; 