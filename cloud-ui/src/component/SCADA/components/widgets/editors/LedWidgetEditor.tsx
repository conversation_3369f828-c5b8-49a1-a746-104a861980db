import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  SelectField,
  SwitchField,
  ColorField
} from './EditorComponents';

const LedWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();
  
  const sizeOptions = [
    { value: 'small', label: t('scada.properties.common.small') },
    { value: 'medium', label: t('scada.properties.common.medium') },
    { value: 'large', label: t('scada.properties.common.large') }
  ];

  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  return (
    <EditorContainer title={t('scada.properties.components.led')}>
      <TextInputField
        label={t('scada.properties.common.label')}
        value={properties.label || t('scada.components.types.led')}
        property="label"
        onChange={handleInputChange}
      />
      <SwitchField
        label={t('scada.properties.components.lightState')}
        checked={properties.isOn === undefined ? false : properties.isOn}
        property="isOn"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.components.onColor')}
        value={properties.onColor || '#4caf50'}
        property="onColor"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.components.offColor')}
        value={properties.offColor || '#bdbdbd'}
        property="offColor"
        onChange={handleInputChange}
      />
      <SelectField
        label={t('scada.properties.common.size')}
        value={properties.size || 'medium'}
        property="size"
        onChange={handleInputChange}
        options={sizeOptions}
      />
    </EditorContainer>
  );
};

export default LedWidgetEditor; 