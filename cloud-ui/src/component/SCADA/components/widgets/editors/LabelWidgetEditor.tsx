import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  NumberInputField,
  ColorField,
  SelectField
} from './EditorComponents';

const LabelWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();

  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  const variantOptions = [
    { value: 'standard', label: t('scada.properties.common.standard') },
    { value: 'filled', label: t('scada.properties.common.filled') },
    { value: 'outlined', label: t('scada.properties.common.outlined') }
  ];

  const alignmentOptions = [
    { value: 'left', label: t('scada.properties.common.leftAlign') },
    { value: 'center', label: t('scada.properties.common.center') },
    { value: 'right', label: t('scada.properties.common.rightAlign') }
  ];

  return (
    <EditorContainer title={t('scada.properties.components.label')}>
      <TextInputField
        label={t('scada.properties.components.textContent')}
        value={properties.text || t('scada.components.types.label')}
        property="text"
        onChange={handleInputChange}
      />
      <NumberInputField
        label={t('scada.properties.components.fontSize')}
        value={properties.fontSize || 14}
        property="fontSize"
        onChange={handleInputChange}
        min={8}
        max={72}
      />
      <SelectField
        label={t('scada.properties.common.variant')}
        value={properties.variant || 'standard'}
        property="variant"
        onChange={handleInputChange}
        options={variantOptions}
      />
      <SelectField
        label={t('scada.properties.components.alignment')}
        value={properties.alignment || 'left'}
        property="alignment"
        onChange={handleInputChange}
        options={alignmentOptions}
      />
      <ColorField
        label={t('scada.properties.components.textColor')}
        value={properties.color || '#000000'}
        property="color"
        onChange={handleInputChange}
      />
      <ColorField
        label={t('scada.properties.backgroundColor')}
        value={properties.backgroundColor || 'transparent'}
        property="backgroundColor"
        onChange={handleInputChange}
      />
    </EditorContainer>
  );
};

export default LabelWidgetEditor; 