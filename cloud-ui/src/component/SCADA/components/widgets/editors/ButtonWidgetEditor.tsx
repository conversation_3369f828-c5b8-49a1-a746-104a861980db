import React from 'react';
import { useTranslation } from 'react-i18next';
import { WidgetEditorProps } from './EditorProps';
import {
  EditorContainer,
  TextInputField,
  SelectField
} from './EditorComponents';

const ButtonWidgetEditor: React.FC<WidgetEditorProps> = ({
  properties,
  onPropertyChange
}) => {
  const { t } = useTranslation();
  
  // 处理输入变化，允许清空值
  const handleInputChange = (property: string, value: any) => {
    onPropertyChange(property, value);
  };

  const actionOptions = [
    { value: 'toggle', label: t('scada.properties.components.toggle') },
    { value: 'set', label: t('scada.properties.components.set') },
    { value: 'reset', label: t('scada.properties.components.reset') }
  ];

  return (
    <EditorContainer title={t('scada.properties.components.button')}>
      <TextInputField
        label={t('scada.properties.components.buttonText')}
        value={properties.label || t('scada.components.types.button')}
        property="label"
        onChange={handleInputChange}
      />
      <SelectField
        label={t('scada.properties.components.actionType')}
        value={properties.action || 'toggle'}
        property="action"
        onChange={handleInputChange}
        options={actionOptions}
      />
      <TextInputField
        label={t('scada.properties.components.targetTag')}
        value={properties.targetTag || ''}
        property="targetTag"
        onChange={handleInputChange}
      />
    </EditorContainer>
  );
};

export default ButtonWidgetEditor; 