import React, { useState, useEffect, useMemo } from 'react';
import { Box, Switch, styled, Typography } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';
import { publishMessage } from '../../../../services/mqttDataService';

export interface SwitchWidgetProps {
  state: boolean;
  onColor?: string;
  offColor?: string;
  label?: string;
  isPreviewMode?: boolean;
  topicValue?: MqttValue | null;
  topic?: string;
}

// 自定义Switch样式 - 使用sx属性处理颜色而不是直接传递属性
const CustomSwitch = styled(Switch)(({ theme }) => ({
  width: 62,
  height: 34,
  padding: 0,
  '& .MuiSwitch-switchBase': {
    padding: 0,
    margin: 2,
    transitionDuration: '300ms',
    '&.Mui-checked': {
      transform: 'translateX(28px)',
      color: '#fff',
      '& + .MuiSwitch-track': {
        opacity: 1,
        border: 0,
      },
    },
  },
  '& .MuiSwitch-thumb': {
    boxSizing: 'border-box',
    width: 30,
    height: 30,
    boxShadow: '0 2px 4px 0 rgba(0,0,0,0.3)',
  },
  '& .MuiSwitch-track': {
    borderRadius: 34 / 2,
    opacity: 1,
    transition: theme.transitions.create(['background-color'], {
      duration: 500,
    }),
  },
}));

export const SwitchWidget: React.FC<SwitchWidgetProps> = ({
  state,
  onColor = '#4caf50',
  offColor = '#f44336',
  label = '',
  isPreviewMode = false,
  topicValue = null,
  topic = ''
}) => {
  // 添加本地状态用于预览模式下的交互
  const [localState, setLocalState] = useState(state);
  // 跟踪发布状态，避免循环发布
  const [isPublishing, setIsPublishing] = useState(false);
  
  // 如果topicValue存在，更新本地状态
  useEffect(() => {
    if (topicValue && topicValue.dataType === 'boolean' && !isPublishing) {
      // 处理字符串类型的布尔值
      if (typeof topicValue.value === 'string') {
        setLocalState(topicValue.value.toLowerCase() === 'true');
      } else {
        setLocalState(Boolean(topicValue.value));
      }
    }
  }, [topicValue, isPublishing]);
  
  // 在组件初始化时设置本地值
  useEffect(() => {
    if (!topicValue) {
      setLocalState(state);
    }
  }, [state, topicValue]);
  
  // 使用传入值或主题值，优先使用主题值
  const currentState = useMemo(() => {
    if (topicValue) {
      if (topicValue.dataType === 'boolean') {
        return Boolean(topicValue.value);
      } else if (topicValue.dataType === 'number') {
        // 对于数值类型，将非0视为true
        return Number(topicValue.value) !== 0;
      } else if (typeof topicValue.value === 'string') {
        // 对于字符串，检查是否为"true"
        return topicValue.value.toLowerCase() === 'true';
      }
    }
    return isPreviewMode ? localState : state;
  }, [isPreviewMode, localState, state, topicValue]);
  
  // 处理开关切换
  const handleChange = () => {
    // 在预览模式下更新本地状态和发布消息
    if (isPreviewMode) {
      const newState = !localState;
      setLocalState(newState);
      
      // 如果有绑定的主题，发布状态变化
      if (topic) {
        setIsPublishing(true);
        publishMessage(topic, newState.toString())
          .then(() => {
            setTimeout(() => {
              setIsPublishing(false);
            }, 300);
          })
          .catch(error => {
            console.error('发布开关状态失败:', error);
            setIsPublishing(false);
          });
      }
    }
    // 在编辑模式下不执行任何操作
  };
  
  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        pointerEvents: isPreviewMode ? 'auto' : 'none', // 在预览模式下启用交互
        backgroundColor: 'transparent'
      }}
    >
      {label && (
        <Typography 
          variant="caption" 
          sx={{ 
            mb: 1,
            textAlign: 'center',
            fontWeight: currentState ? 'bold' : 'normal',
            color: currentState ? onColor : offColor
          }}
        >
          {label}
        </Typography>
      )}
      
      <CustomSwitch 
        checked={currentState}
        onChange={handleChange}
        sx={{
          '& .MuiSwitch-switchBase.Mui-checked': {
            color: '#fff',
            '& + .MuiSwitch-track': {
              backgroundColor: onColor,
            },
          },
          '& .MuiSwitch-track': {
            backgroundColor: offColor,
          }
        }}
      />
    </Box>
  );
};

export default SwitchWidget; 