import React, { useState, useEffect } from 'react';
import { Box, Typography } from '@mui/material';
import ImageIcon from '@mui/icons-material/Image';
import ZoomInIcon from '@mui/icons-material/ZoomIn';
import { useDialogContext } from '../../../../context/GlobalDialog';

export interface ImageWidgetProps {
  src: string;
  isEditMode?: boolean;
}

export const ImageWidget: React.FC<ImageWidgetProps> = ({
  src,
  isEditMode = false
}) => {
  // 状态管理
  const [error, setError] = useState(false);
  
  // 获取对话框上下文
  const { openDialog } = useDialogContext();
  
  // 重置加载状态当src改变时
  useEffect(() => {
    if (src) {
      setError(false);
    }
  }, [src]);
  
  // 处理图片点击 - 使用GlobalDialog打开图片
  const handleImageClick = () => {
    // 只在非编辑模式下激活预览功能
    if (!isEditMode && src && !error) {
      // 创建图片内容元素
      const imageContent = (
        <Box sx={{ 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          width: '100%',
          height: '100%',
          p: 0,
          overflow: 'auto'
        }}>
          <img 
            src={src}
            alt="Image Preview"
            draggable="false"
            style={{ 
              maxWidth: '100%',
              maxHeight: 'calc(100vh - 100px)',
              objectFit: 'contain'
            }}
          />
        </Box>
      );
      
      // 使用空字符串作为标题，这样不会显示标题栏
      openDialog(imageContent, "");
    }
  };
  
  // 防止图片被拖拽
  const preventDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };
  
  // 渲染内容
  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden',
        p: 0, // 移除内边距
        bgcolor: 'background.paper',
        position: 'relative'
      }}
    >
      {src ? (
        <>
          <Box 
            onClick={handleImageClick}
            sx={{ 
              width: '100%', 
              height: '100%', 
              cursor: (!isEditMode && !error) ? 'pointer' : 'default',
              position: 'relative',
              '&:hover': {
                '& .zoom-icon': {
                  opacity: !isEditMode ? 1 : 0, // 只在非编辑模式下显示放大图标
                }
              }
            }}
            onDragStart={preventDrag}
          >
            <Box sx={{ position: 'relative', width: '100%', height: '100%' }}>
              <img 
                src={src}
                alt="Images"
                draggable="false"
                onDragStart={preventDrag}
                style={{ 
                  width: '100%', 
                  height: '100%', 
                  objectFit: 'fill',
                  display: error ? 'none' : 'block',
                  pointerEvents: isEditMode ? 'none' : 'auto'
                }}
              />
              
              {/* 添加放大图标提示 */}
              {!isEditMode && !error && (
                <Box 
                  className="zoom-icon"
                  sx={{
                    position: 'absolute',
                    top: '10px',
                    right: '10px',
                    opacity: 0,
                    transition: 'opacity 0.3s',
                    bgcolor: 'rgba(255,255,255,0.7)',
                    borderRadius: '50%',
                    p: 0.5,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <ZoomInIcon sx={{ fontSize: 20 }} />
                </Box>
              )}
            </Box>
          </Box>
          {error && (
            <Box 
              sx={{ 
                width: '100%', 
                height: '100%',
                display: 'flex', 
                flexDirection: 'column',
                justifyContent: 'center', 
                alignItems: 'center',
                bgcolor: '#f5f5f5',
                border: '1px dashed #bdbdbd',
                p: 2
              }}
            >
              <ImageIcon sx={{ fontSize: 48, color: '#9e9e9e', mb: 1 }} />
              <Typography variant="body2" color="textSecondary" textAlign="center">
                Failed to load image
              </Typography>
            </Box>
          )}
        </>
      ) : (
        <Box 
          sx={{ 
            width: '100%', 
            height: '100%',
            display: 'flex', 
            flexDirection: 'column',
            justifyContent: 'center', 
            alignItems: 'center',
            bgcolor: '#f5f5f5',
            border: '1px dashed #bdbdbd',
            p: 2
          }}
        >
          <ImageIcon sx={{ fontSize: 48, color: '#9e9e9e', mb: 1 }} />
          <Typography variant="body2" color="textSecondary" textAlign="center">
            None Image
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default ImageWidget; 