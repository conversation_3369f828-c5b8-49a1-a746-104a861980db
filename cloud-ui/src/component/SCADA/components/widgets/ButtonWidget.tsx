import React, { useState, useEffect } from 'react';
import { Button, Box } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface ButtonWidgetProps {
  label: string;
  action: string;
  targetTag: string;
  topicValue?: MqttValue | null;
  onAction?: (action: string, value: any) => void;
}

export const ButtonWidget: React.FC<ButtonWidgetProps> = ({ 
  label = 'Button', 
  action = 'toggle',
  targetTag = '',
  topicValue = null,
  onAction
}) => {
  // 跟踪按钮状态
  const [buttonState, setButtonState] = useState(false);
  
  // 当MQTT值变化时更新按钮状态
  useEffect(() => {
    if (topicValue) {
      if (topicValue.dataType === 'boolean') {
        setButtonState(Boolean(topicValue.value));
      } else if (topicValue.dataType === 'number') {
        setButtonState(Number(topicValue.value) > 0);
      } else if (topicValue.dataType === 'string' && typeof topicValue.value === 'string') {
        const val = topicValue.value.toLowerCase();
        setButtonState(val === 'true' || val === 'on' || val === '1');
      }
    }
  }, [topicValue]);
  
  // 处理按钮点击事件
  const handleClick = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation(); // 防止事件冒泡到父元素
    
    // 切换按钮状态
    const newState = !buttonState;
    setButtonState(newState);
    
    // 根据不同的动作类型执行不同的操作
    switch (action) {
      case 'toggle':
        console.log(`Toggle action on tag: ${targetTag}`);
        // 调用回调函数发送操作到父组件
        if (onAction) {
          onAction('toggle', newState);
        }
        break;
      case 'set':
        console.log(`Set action on tag: ${targetTag}`);
        if (onAction) {
          onAction('set', true);
        }
        break;
      case 'reset':
        console.log(`Reset action on tag: ${targetTag}`);
        if (onAction) {
          onAction('reset', false);
        }
        break;
      default:
        console.log(`Unknown action: ${action} on tag: ${targetTag}`);
    }
  };

  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'center', 
        p: 1 
      }}
    >
      <Button 
        variant="contained" 
        color={buttonState ? "success" : "primary"} 
        fullWidth 
        onClick={handleClick}
        sx={{ 
          height: '100%', 
          minHeight: 30, 
          textTransform: 'none',
          fontSize: {
            xs: '0.7rem',
            sm: '0.8rem',
            md: '0.875rem'
          }
        }}
      >
        {label}
      </Button>
    </Box>
  );
};

export default ButtonWidget; 