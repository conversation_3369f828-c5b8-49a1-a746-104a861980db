import React, { useMemo } from 'react';
import { Box, Typography, Card } from '@mui/material';
import SensorsIcon from '@mui/icons-material/Sensors';
import ThermostatIcon from '@mui/icons-material/Thermostat';
import OpacityIcon from '@mui/icons-material/Opacity';
import SpeedIcon from '@mui/icons-material/Speed';
import ElectricBoltIcon from '@mui/icons-material/ElectricBolt';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface SensorWidgetProps {
  value: string | number;
  unit: string;
  title: string;
  precision?: number;
  type?: 'default' | 'temperature' | 'pressure' | 'flow' | 'power';
  topicValue?: MqttValue | null;
}

export const SensorWidget: React.FC<SensorWidgetProps> = ({
  value,
  unit,
  title,
  precision = 1,
  type = 'default',
  topicValue = null
}) => {
  // 根据MQTT数据确定实际值
  const actualValue = useMemo(() => {
    if (topicValue) {
      if (topicValue.dataType === 'number') {
        return Number(topicValue.value);
      }
      // 如果不是数字，则直接使用字符串值
      return String(topicValue.value);
    }
    return value;
  }, [topicValue, value]);
  
  // 如果值是数字，则根据精度进行格式化
  const formattedValue = typeof actualValue === 'number' 
    ? actualValue.toFixed(precision) 
    : actualValue;
  
  // 根据传感器类型选择图标和颜色
  const getSensorIcon = () => {
    switch (type) {
      case 'temperature':
        return <ThermostatIcon sx={{ fontSize: 24, color: '#f44336' }} />;
      case 'pressure':
        return <SpeedIcon sx={{ fontSize: 24, color: '#2196f3' }} />;
      case 'flow':
        return <OpacityIcon sx={{ fontSize: 24, color: '#4caf50' }} />;
      case 'power':
        return <ElectricBoltIcon sx={{ fontSize: 24, color: '#ff9800' }} />;
      default:
        return <SensorsIcon sx={{ fontSize: 24, color: '#757575' }} />;
    }
  };
  
  // 获取传感器类型对应的颜色
  const getSensorColor = () => {
    switch (type) {
      case 'temperature':
        return '#f44336';
      case 'pressure':
        return '#2196f3';
      case 'flow':
        return '#4caf50';
      case 'power':
        return '#ff9800';
      default:
        return '#757575';
    }
  };
  
  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'transparent',
        position: 'relative',
        p: 1
      }}
    >
      <Card
        elevation={0}
        sx={{
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '8px',
          backgroundColor: 'rgba(255, 255, 255, 0.8)',
          position: 'relative',
          overflow: 'hidden',
          border: '1px solid rgba(0, 0, 0, 0.12)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            backgroundColor: getSensorColor(),
            zIndex: 1
          }
        }}
      >
        <Box sx={{ position: 'absolute', top: '10px', left: '10px', opacity: 0.8 }}>
          {getSensorIcon()}
        </Box>
        
        <Box sx={{ position: 'absolute', top: '10px', right: '10px' }}>
          <Typography variant="caption" sx={{ 
            fontWeight: 'bold',
            color: 'text.secondary',
            fontSize: '0.7rem'
          }}>
            {title}
          </Typography>
        </Box>
        
        <Box sx={{ 
          display: 'flex', 
          alignItems: 'baseline',
          justifyContent: 'center',
          mt: 3
        }}>
          <Typography 
            variant="h3" 
            sx={{ 
              fontWeight: 'bold',
              fontFamily: 'monospace',
              color: getSensorColor(),
              letterSpacing: '-1px'
            }}
          >
            {formattedValue}
          </Typography>
          <Typography 
            variant="subtitle1" 
            sx={{ 
              ml: 0.5,
              color: 'text.secondary',
              fontWeight: 'bold',
              fontSize: '1rem'
            }}
          >
            {unit}
          </Typography>
        </Box>
        
        <Box sx={{ 
          width: '100%', 
          display: 'flex', 
          justifyContent: 'center',
          alignItems: 'center',
          mt: 1,
          mb: 1
        }}>
          <Box sx={{ 
            height: '4px', 
            width: '40%', 
            backgroundColor: `${getSensorColor()}30`, 
            borderRadius: '2px' 
          }}/>
        </Box>
      </Card>
    </Box>
  );
};

export default SensorWidget; 