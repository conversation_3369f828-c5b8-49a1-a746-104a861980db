import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import { Box, Slider } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';
import { publishMessage } from '../../../../services/mqttDataService';

// 添加一个简单的防抖函数
function useDebounce(callback: (...args: any[]) => void, delay: number) {
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  
  return useCallback((...args: any[]) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      callback(...args);
    }, delay);
  }, [callback, delay]);
}

export interface SliderWidgetProps {
  min: number;
  max: number;
  value: number;
  unit?: string;
  showLabels?: boolean;
  orientation?: 'horizontal' | 'vertical';
  isPreviewMode?: boolean;
  step?: number | string;
  topicValue?: MqttValue | null;
  topic?: string;
}

export const SliderWidget: React.FC<SliderWidgetProps> = ({
  min = 0,
  max = 100,
  value = 50,
  unit = '',
  showLabels = true,
  orientation = 'horizontal',
  isPreviewMode = false,
  step = 1,
  topicValue = null,
  topic = ''
}) => {

  // 添加本地状态用于预览模式下的交互
  const [localValue, setLocalValue] = useState(value);
  // 跟踪发布状态，避免循环发布
  const [isPublishing, setIsPublishing] = useState(false);
  // 添加一个临时状态来记录用户当前拖动的值，避免频繁触发MQTT更新
  const [draftValue, setDraftValue] = useState<number | null>(null);
  // 跟踪是否正在拖动
  const [isDragging, setIsDragging] = useState(false);
  
  // 确保step是数字类型
  const numericStep = typeof step === 'string' ? parseFloat(step) || 1 : step;
  
  // 使用防抖函数发布消息
  const debouncedPublish = useDebounce((topicToPublish: string, valueToPublish: string) => {
    setIsPublishing(true);
    publishMessage(topicToPublish, valueToPublish)
      .then(() => {
        setTimeout(() => {
          setIsPublishing(false);
        }, 300);
      })
      .catch(error => {
        console.error('发布滑块值失败:', error);
        setIsPublishing(false);
      });
  }, 300); // 300毫秒的防抖延迟
  
  // 如果topicValue存在且不是在发布或拖动状态，更新本地状态
  useEffect(() => {
    if (topicValue && topicValue.dataType === 'number' && !isPublishing && !isDragging) {
      setLocalValue(Number(topicValue.value));
      setDraftValue(null);
    }
  }, [topicValue, isPublishing, isDragging]);
  
  // 在组件初始化时设置本地值
  useEffect(() => {
    if (!topicValue) {
      setLocalValue(value);
    }
  }, [value, topicValue]);
  
  // 使用传入值或主题值，优先使用用户当前拖动的草稿值
  const currentValue = useMemo(() => {
    // 如果有草稿值(用户正在拖动)，优先使用草稿值
    if (draftValue !== null) {
      return draftValue;
    }
    // 否则，使用主题值或本地值
    if (topicValue && topicValue.dataType === 'number') {
      return Number(topicValue.value);
    }
    return isPreviewMode ? localValue : value;
  }, [isPreviewMode, localValue, value, topicValue, draftValue]);
  
  // 处理滑块变化 - 分离拖动中和拖动结束的处理
  const handleChange = (_event: Event, newValue: number | number[]) => {
    const numericValue = newValue as number;
    // 更新草稿值，表示正在拖动
    setDraftValue(numericValue);
  };
  
  // 处理拖动开始
  const handleDragStart = () => {
    setIsDragging(true);
  };
  
  // 处理拖动结束 - 只在拖动结束时发布一次
  const handleDragEnd = (_event: Event | React.SyntheticEvent, newValue: number | number[]) => {
    const numericValue = newValue as number;
    setLocalValue(numericValue);
    setIsDragging(false);
    
    // 在预览模式下，发布消息到MQTT服务器，只在拖动结束时发送一次
    if (isPreviewMode && topic) {
      debouncedPublish(topic, numericValue.toString());
    }
    
    // 清除草稿值
    setTimeout(() => {
      setDraftValue(null);
    }, 50);
  };

  // 生成刻度标记
  const getMarks = () => {
    // 确保至少有min、max和中间点的刻度
    let marks = [];
    
    // 计算合适的刻度间隔（不超过10个刻度）
    let interval = Math.max(numericStep, (max - min) / 10);
    
    // 根据步长值调整间隔的精度
    let precision = 0;
    if (numericStep < 1) {
      // 获取小数部分的位数
      const stepStr = numericStep.toString();
      precision = stepStr.includes('.') ? stepStr.split('.')[1].length : 0;
      // 确保间隔值具有相同的精度
      interval = parseFloat(interval.toFixed(precision));
    }
    
    // 生成刻度点
    for (let i = min; i <= max; i += interval) {
      // 确保精度一致，避免浮点数计算误差
      const value = parseFloat(i.toFixed(precision));
      
      // 格式化显示值
      let displayValue;
      if (value > 1000) {
        displayValue = value.toExponential(1);
      } else if (Number.isInteger(value)) {
        displayValue = value;
      } else {
        // 显示小数时保持适当精度
        displayValue = value.toFixed(Math.min(precision, 2));
      }
      
      marks.push({
        value: value,
        label: `${displayValue}${unit}`
      });
    }
    
    // 确保包含最大值刻度
    const lastMark = marks[marks.length - 1];
    if (lastMark && lastMark.value < max) {
      // 使用相同的格式化逻辑
      let displayMax;
      if (max > 1000) {
        displayMax = max.toExponential(1);
      } else if (Number.isInteger(max)) {
        displayMax = max;
      } else {
        displayMax = max.toFixed(Math.min(precision, 2));
      }
      
      marks.push({
        value: max,
        label: `${displayMax}${unit}`
      });
    }
    
    return marks;
  };

  return (
    <Box
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        padding: '8px',
        pointerEvents: isPreviewMode ? 'auto' : 'none', // 在预览模式下启用交互
        backgroundColor: 'transparent'
      }}
    >
      <Slider
        orientation={orientation}
        min={min}
        max={max}
        value={currentValue}
        step={numericStep}
        onChange={handleChange}
        onMouseDown={handleDragStart}
        onChangeCommitted={handleDragEnd}
        marks={showLabels ? getMarks() : undefined}
        track={orientation === 'horizontal' ? 'normal' : 'inverted'}
        aria-labelledby="slider-widget"
        sx={{
          [orientation === 'horizontal' ? 'width' : 'height']: '100%',
          [orientation === 'horizontal' ? 'maxWidth' : 'maxHeight']: '100%',
          padding: orientation === 'horizontal' ? '15px 0' : '0 15px',
          '& .MuiSlider-thumb': {
            height: 18,
            width: 18,
            backgroundColor: '#fff',
            border: '2px solid currentColor',
            '&:focus, &:hover, &.Mui-active': {
              boxShadow: '0 0 0 8px rgba(25, 118, 210, 0.16)',
            },
          },
          '& .MuiSlider-track': {
            height: orientation === 'horizontal' ? 6 : undefined,
            width: orientation === 'vertical' ? 6 : undefined,
            borderRadius: 3,
            backgroundColor: 'primary.main',
          },
          '& .MuiSlider-rail': {
            height: orientation === 'horizontal' ? 6 : undefined,
            width: orientation === 'vertical' ? 6 : undefined,
            borderRadius: 3,
            opacity: 0.4,
            backgroundColor: '#bdbdbd',
          },
          '& .MuiSlider-markLabel': {
            fontSize: '0.7rem',
            fontWeight: 'bold',
            color: 'text.secondary',
            [orientation === 'vertical' ? 'right' : 'top']: orientation === 'vertical' ? '10px' : '20px',
          },
          '& .MuiSlider-mark': {
            backgroundColor: '#bdbdbd',
            height: 4,
            width: 4,
            borderRadius: '50%',
          }
        }}
      />
    </Box>
  );
};

export default React.memo(SliderWidget, (prevProps, nextProps) => {
  // 只有在这些属性变化时才重新渲染
  // 确保始终返回布尔值，不能有undefined
  if (!prevProps.topicValue && !nextProps.topicValue) {
    return true; // 如果都没有topicValue，认为是相等的
  }
  
  if (!prevProps.topicValue || !nextProps.topicValue) {
    return false; // 如果一个有topicValue而另一个没有，认为是不相等的
  }
  
  // 比较基本属性
  const basicPropsEqual = 
    prevProps.min === nextProps.min &&
    prevProps.max === nextProps.max &&
    prevProps.step === nextProps.step &&
    prevProps.orientation === nextProps.orientation &&
    prevProps.unit === nextProps.unit &&
    prevProps.showLabels === nextProps.showLabels;
  
  // 如果基本属性不同，直接返回false
  if (!basicPropsEqual) {
    return false;
  }
  
  // 比较值，如果值相同或差异很小，认为是相等的
  // 确保使用数字比较
  const prevValue = prevProps.topicValue?.value;
  const nextValue = nextProps.topicValue?.value;
  
  if (prevValue === nextValue) {
    return true;
  }
  
  // 如果都是数字，检查差异是否足够小
  if (typeof prevValue === 'number' && typeof nextValue === 'number') {
    return Math.abs(prevValue - nextValue) < 0.001;
  }
  
  // 其他情况认为是不相等的
  return false;
}); 