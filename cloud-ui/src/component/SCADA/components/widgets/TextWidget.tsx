import React, { useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface TextWidgetProps {
  text: string;
  fontSize: number;
  color: string;
  alignment: string;
  topicValue?: MqttValue | null;
}

export const TextWidget: React.FC<TextWidgetProps> = ({ 
  text = '文本标签', 
  fontSize = 16, 
  color = '#000000',
  alignment = 'center',
  topicValue = null
}) => {
  // 根据alignment属性值设置对应的对齐方式
  const getTextAlignment = () => {
    switch (alignment) {
      case 'left':
        return 'flex-start';
      case 'right':
        return 'flex-end';
      case 'center':
      default:
        return 'center';
    }
  };

  // 获取显示文本，优先使用主题值
  const displayText = useMemo(() => {
    if (topicValue) {
      return String(topicValue.value);
    }
    return text;
  }, [topicValue, text]);

  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: getTextAlignment(),
        p: 1
      }}
    >
      <Typography 
        sx={{ 
          fontSize: `${fontSize}px`, 
          color, 
          textAlign: alignment as 'left' | 'center' | 'right',
          width: '100%',
          wordBreak: 'break-word',
          whiteSpace: 'pre-wrap'
        }}
      >
        {displayText}
      </Typography>
    </Box>
  );
};

export default TextWidget; 