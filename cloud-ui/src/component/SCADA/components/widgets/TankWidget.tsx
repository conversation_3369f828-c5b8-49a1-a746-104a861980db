import React, { useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface TankWidgetProps {
  min: number;
  max: number;
  value: number;
  unit: string;
  fillColor: string;
  title: string;
  topicValue?: MqttValue | null;
}

export const TankWidget: React.FC<TankWidgetProps> = ({ 
  min = 0, 
  max = 100, 
  value = 60, 
  unit = 'L',
  fillColor = '#3f51b5',
  title = '水箱',
  topicValue = null
}) => {
  // 获取实际值，优先使用MQTT数据
  const actualValue = useMemo(() => {
    if (topicValue && topicValue.dataType === 'number') {
      return Number(topicValue.value);
    }
    return value;
  }, [topicValue, value]);
  
  // 确保数值在范围内
  const safeValue = Math.min(Math.max(actualValue, min), max);
  
  // 计算填充高度百分比
  const percentage = ((safeValue - min) / (max - min)) * 100;
  
  return (
    <Box sx={{ 
      width: '100%', 
      height: '100%', 
      display: 'flex', 
      flexDirection: 'column',
      p: 1
    }}>
      {/* 标题 */}
      <Typography 
        variant="subtitle2" 
        align="center" 
        sx={{ mb: 1, fontWeight: 'bold' }}
      >
        {title}
      </Typography>
      
      {/* 水箱容器 */}
      <Box sx={{ 
        flex: 1, 
        border: '2px solid #aaa', 
        borderRadius: '4px', 
        position: 'relative',
        overflow: 'hidden',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'flex-end'
      }}>
        {/* 水箱刻度线 */}
        <Box sx={{ position: 'absolute', width: '100%', height: '100%' }}>
          {[0, 25, 50, 75, 100].map((mark) => (
            <Box key={`mark-${mark}`} sx={{ 
              position: 'absolute', 
              left: 0, 
              right: 0,
              bottom: `${mark}%`, 
              borderBottom: mark === 0 ? 'none' : '1px dashed rgba(0,0,0,0.2)',
              zIndex: 1
            }}>
              <Typography 
                variant="caption" 
                sx={{ 
                  position: 'absolute', 
                  right: 2, 
                  bottom: 0, 
                  fontSize: '0.6rem', 
                  color: 'rgba(0,0,0,0.6)' 
                }}
              >
                {Math.round(min + ((100 - mark) / 100) * (max - min))}
              </Typography>
            </Box>
          ))}
        </Box>
        
        {/* 水位填充 */}
        <Box sx={{ 
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          height: `${percentage}%`,
          backgroundColor: fillColor,
          transition: 'height 0.5s ease-in-out',
          zIndex: 0,
          boxShadow: 'inset 0 5px 5px rgba(255,255,255,0.3)'
        }} />
        
        {/* 水位波浪动画效果 */}
        <Box sx={{
          position: 'absolute',
          bottom: `${percentage}%`,
          left: 0,
          right: 0,
          height: '5px',
          background: `linear-gradient(to bottom, transparent, ${fillColor})`,
          opacity: 0.7,
          zIndex: 2
        }} />
      </Box>
      
      {/* 当前值显示 */}
      <Typography 
        variant="body2" 
        align="center" 
        sx={{ mt: 1, fontWeight: 'bold' }}
      >
        {safeValue.toFixed(1)} {unit}
      </Typography>
    </Box>
  );
};

export default TankWidget; 