import React, { useState, useEffect, useMemo } from 'react';
import { 
  Box, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow, 
  Paper, 
  Typography, 
  Checkbox,
  TablePagination,
  TableSortLabel,
} from '@mui/material';
import { MqttValue, useMqttData } from '../../../../context/MqttDataContext';

export interface TableWidgetProps {
  columns?: Array<{field: string, headerName: string}>;
  dataSource?: string | any[];
  topicValue?: MqttValue | null;
  title?: string;
  showTitle?: boolean;
  selectable?: boolean;
  density?: 'small' | 'medium' | 'large';
  showIndex?: boolean;
  pageSize?: number;
  paginationPosition?: 'top' | 'bottom' | 'both' | 'none';
  sortable?: boolean;
}

// 排序比较函数
function descendingComparator<T>(a: T, b: T, orderBy: keyof T) {
  if (b[orderBy] < a[orderBy]) {
    return -1;
  }
  if (b[orderBy] > a[orderBy]) {
    return 1;
  }
  return 0;
}

type Order = 'asc' | 'desc';

function getComparator<Key extends keyof any>(
  order: Order,
  orderBy: Key,
): (a: { [key in Key]: number | string }, b: { [key in Key]: number | string }) => number {
  return order === 'desc'
    ? (a, b) => descendingComparator(a, b, orderBy)
    : (a, b) => -descendingComparator(a, b, orderBy);
}

export const TableWidget: React.FC<TableWidgetProps> = ({
  dataSource,
  topicValue = null,
  title = '数据表格',
  showTitle = true,
  selectable = true,
  density = 'small',
  showIndex = true,
  pageSize = 10,
  paginationPosition = 'bottom',
  sortable = true
}) => {
  // 状态用于存储实际数据
  const [tableData, setTableData] = useState<any[]>([]);
  // 选中行的状态
  const [selected, setSelected] = useState<string[]>([]);
  // 分页状态
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(pageSize);
  // 排序状态
  const [order, setOrder] = useState<Order>('asc');
  const [orderBy, setOrderBy] = useState<string>('topic');
  
  // 获取MQTT上下文
  const { topicValues } = useMqttData();
  
  // 固定表格列为三列：主题、时间、值
  const fixedColumns = useMemo(() => [
    { field: 'topic', headerName: 'Topic' },
    { field: 'time', headerName: 'Time' },
    { field: 'value', headerName: 'Value' }
  ], []);

  // 当属性变化时更新内部状态
  useEffect(() => {
    setRowsPerPage(pageSize);
  }, [pageSize]);
  
  // 当 topicValue 变化或 topicValues 全局变化时更新数据
  useEffect(() => {
    // 创建用于表格显示的数据数组
    const newTableData: any[] = [];
    
    // 如果有特定的 topicValue，添加它
    if (topicValue && topicValue.value !== undefined) {
      newTableData.push({
        id: topicValue.topic,
        topic: topicValue.topic,
        time: new Date(topicValue.timestamp).toLocaleString(),
        value: typeof topicValue.value === 'object' ? 
          JSON.stringify(topicValue.value) : String(topicValue.value)
      });
    }
    
    // 从全局 topicValues 中获取所有已订阅主题的最新值
    Object.entries(topicValues).forEach(([topic, value]) => {
      // 避免添加重复的项（如果已经添加了 topicValue）
      if (topic !== topicValue?.topic) {
        newTableData.push({
          id: topic,
          topic: topic,
          time: new Date(value.timestamp).toLocaleString(),
          value: typeof value.value === 'object' ? 
            JSON.stringify(value.value) : String(value.value)
        });
      }
    });
    
    // 如果没有实时数据且提供了示例数据，则使用它
    if (newTableData.length === 0 && Array.isArray(dataSource)) {
      dataSource.forEach((item, index) => {
        newTableData.push({
          id: `sample-${index}`,
          topic: item.topic || `示例主题/${index}`,
          time: item.time || new Date().toLocaleString(),
          value: item.value || `示例值-${index}`
        });
      });
    }
    
    // 如果还是没有数据，生成一些示例数据
    if (newTableData.length === 0) {
      for (let i = 0; i < 3; i++) {
        newTableData.push({
          id: `example-${i}`,
          topic: `example/topic/${i}`,
          time: new Date().toLocaleString(),
          value: `示例值-${i}`
        });
      }
    }
    
    setTableData(newTableData);
  }, [topicValue, topicValues, dataSource]);
  
  // 处理选择所有行
  const handleSelectAllClick = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.checked) {
      const newSelected = tableData.map(n => n.id);
      setSelected(newSelected);
      return;
    }
    setSelected([]);
  };
  
  // 处理选择单行
  const handleClick = (id: string) => {
    if (!selectable) return;
    
    const selectedIndex = selected.indexOf(id);
    let newSelected: string[] = [];
    
    if (selectedIndex === -1) {
      newSelected = [...selected, id];
    } else {
      newSelected = selected.filter(itemId => itemId !== id);
    }
    
    setSelected(newSelected);
  };
  
  // 处理分页变化
  const handleChangePage = (_event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // 处理每页行数变化
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };
  
  // 处理排序
  const handleRequestSort = (property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };
  
  // 检查一行是否被选中
  const isSelected = (id: string) => selected.indexOf(id) !== -1;
  
  // 获取应该显示的数据，应用排序和分页
  const visibleRows = useMemo(() => {
    // 首先排序
    const sortedData = sortable 
      ? [...tableData].sort(getComparator(order, orderBy))
      : tableData;
    
    // 然后分页，如果不显示分页则返回所有数据
    return paginationPosition === 'none'
      ? sortedData
      : sortedData.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);
  }, [tableData, sortable, order, orderBy, page, rowsPerPage, paginationPosition]);
  
  // 创建排序标签
  const createSortHandler = (property: string) => () => {
    if (sortable) {
      handleRequestSort(property);
    }
  };
  
  // 获取表格密度
  const getTableSize = () => {
    switch (density) {
      case 'small': return 'small';
      case 'medium': return 'medium';
      case 'large': return 'medium'; // MUI表格不直接支持large，但我们可以通过样式调整
      default: return 'small';
    }
  };
  
  // 获取行高，根据密度调整
  const getRowHeight = () => {
    switch (density) {
      case 'small': return '';
      case 'medium': return '';
      case 'large': return '64px'; // large密度的行高增加
      default: return '';
    }
  };

  // 渲染分页控件
  const renderPagination = () => {
    if (paginationPosition === 'none') return null;
    
    return (
      <TablePagination
        rowsPerPageOptions={[5, 10, 25, 50]}
        component="div"
        count={tableData.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
        labelRowsPerPage="Rows per page"
        labelDisplayedRows={({ from, to, count }) => `${from}-${to} / ${count}`}
      />
    );
  };

  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        p: 0.5
      }}
    >
      {showTitle && (
        <Typography variant="subtitle2" sx={{ mb: 0.5, px: 1 }}>
          {title}
        </Typography>
      )}
      
      {/* 顶部分页 */}
      {(paginationPosition === 'top' || paginationPosition === 'both') && renderPagination()}
      
      <TableContainer 
        component={Paper} 
        sx={{
          width: '100%',
          height: (() => {
            let height = '100%';
            if (showTitle) height = 'calc(100% - 24px)';
            if (paginationPosition === 'top') height = 'calc(100% - 52px)';
            if (paginationPosition === 'bottom') height = 'calc(100% - 52px)';
            if (paginationPosition === 'both') height = 'calc(100% - 104px)';
            if (showTitle && (paginationPosition === 'top' || paginationPosition === 'bottom')) {
              height = 'calc(100% - 76px)';
            }
            if (showTitle && paginationPosition === 'both') {
              height = 'calc(100% - 128px)';
            }
            return height;
          })(),
          overflow: 'auto',
          boxShadow: 'none',
          border: '1px solid rgba(224, 224, 224, 1)'
        }}
      >
        <Table size={getTableSize()} stickyHeader>
          <TableHead>
            <TableRow>
              {selectable && (
                <TableCell padding="checkbox">
                  <Checkbox
                    indeterminate={selected.length > 0 && selected.length < tableData.length}
                    checked={tableData.length > 0 && selected.length === tableData.length}
                    onChange={handleSelectAllClick}
                    inputProps={{ 'aria-label': '全选' }}
                    size="small"
                  />
                </TableCell>
              )}
              {showIndex && (
                <TableCell padding="none" sx={{ width: 40, fontWeight: 'bold' }}>
                  #
                </TableCell>
              )}
              {fixedColumns.map((column, colIndex) => (
                <TableCell 
                  key={`header-${colIndex}-${column.field}`} 
                  sx={{ 
                    fontWeight: 'bold',
                    ...(density === 'large' && { padding: '16px' })
                  }}
                  align={column.field === 'topic' ? 'left' : column.field === 'time' ? 'center' : 'right'}
                  sortDirection={orderBy === column.field ? order : false}
                >
                  {sortable ? (
                    <TableSortLabel
                      active={orderBy === column.field}
                      direction={orderBy === column.field ? order : 'asc'}
                      onClick={createSortHandler(column.field)}
                    >
                      {column.headerName}
                    </TableSortLabel>
                  ) : (
                    column.headerName
                  )}
                </TableCell>
              ))}
            </TableRow>
          </TableHead>
          <TableBody>
            {visibleRows.map((row, rowIndex) => {
              const isItemSelected = isSelected(row.id);
              const displayIndex = page * rowsPerPage + rowIndex + 1;
              
              return (
                <TableRow 
                  key={`row-${rowIndex}-${row.id}`}
                  hover
                  onClick={() => handleClick(row.id)}
                  role={selectable ? "checkbox" : undefined}
                  aria-checked={selectable ? isItemSelected : undefined}
                  selected={isItemSelected}
                  sx={{ 
                    cursor: selectable ? 'pointer' : 'default',
                    height: getRowHeight()
                  }}
                >
                  {selectable && (
                    <TableCell padding="checkbox">
                      <Checkbox
                        checked={isItemSelected}
                        size="small"
                      />
                    </TableCell>
                  )}
                  {showIndex && (
                    <TableCell padding="none" align="center">
                      {displayIndex}
                    </TableCell>
                  )}
                  {fixedColumns.map((column, colIndex) => (
                    <TableCell 
                      key={`cell-${rowIndex}-${colIndex}`}
                      align={column.field === 'topic' ? 'left' : column.field === 'time' ? 'center' : 'right'}
                      sx={{
                        maxWidth: column.field === 'topic' ? 150 : 'none',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        ...(density === 'large' && { padding: '16px' })
                      }}
                    >
                      {row[column.field] !== undefined ? row[column.field] : ''}
                    </TableCell>
                  ))}
                  {sortable && <TableCell padding="none" />}
                </TableRow>
              );
            })}
          </TableBody>
        </Table>
      </TableContainer>
      
      {/* 底部分页 */}
      {(paginationPosition === 'bottom' || paginationPosition === 'both') && renderPagination()}
    </Box>
  );
};

export default TableWidget; 