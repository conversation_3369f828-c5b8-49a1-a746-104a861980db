import React, { useMemo } from 'react';
import { Box, Typography, LinearProgress } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface ProgressBarWidgetProps {
  min: number;
  max: number;
  value: number;
  showValue?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'error' | string;
  orientation?: 'horizontal' | 'vertical';
  topicValue?: MqttValue | null;
}

export const ProgressBarWidget: React.FC<ProgressBarWidgetProps> = ({
  min,
  max,
  value,
  showValue = true,
  color = 'primary',
  orientation = 'horizontal',
  topicValue = null
}) => {
  // 根据MQTT数据确定实际值
  const actualValue = useMemo(() => {
    if (topicValue) {
      if (topicValue.dataType === 'number') {
        return Number(topicValue.value);
      } else if (topicValue.dataType === 'string' && typeof topicValue.value === 'string') {
        const parsed = parseFloat(topicValue.value);
        return isNaN(parsed) ? 0 : parsed;
      } else if (topicValue.dataType === 'boolean') {
        return topicValue.value ? 1 : 0;
      }
    }
    return value;
  }, [topicValue, value]);

  // 计算百分比值
  const normalizedValue = ((actualValue - min) / (max - min)) * 100;
  
  // 处理自定义颜色
  const isCustomColor = !['primary', 'secondary', 'success', 'info', 'warning', 'error'].includes(color);
  
  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex',
        flexDirection: orientation === 'vertical' ? 'row' : 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 1
      }}
    >
      {showValue && (
        <Typography 
          variant="subtitle2" 
          sx={{ 
            mb: orientation === 'vertical' ? 0 : 1,
            mr: orientation === 'vertical' ? 1 : 0,
            textAlign: 'center'
          }}
        >
          {actualValue}
        </Typography>
      )}
      
      {orientation === 'horizontal' ? (
        <LinearProgress
          variant="determinate"
          value={normalizedValue}
          color={isCustomColor ? undefined : (color as any)}
          sx={{
            width: '100%',
            height: 10,
            borderRadius: '5px',
            ...(isCustomColor && {
              '& .MuiLinearProgress-bar': {
                backgroundColor: color
              }
            })
          }}
        />
      ) : (
        <Box 
          sx={{ 
            height: '100%',
            width: 20,
            bgcolor: '#e0e0e0',
            borderRadius: '5px',
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'flex-end'
          }}
        >
          <Box
            sx={{
              width: '100%',
              height: `${normalizedValue}%`,
              bgcolor: isCustomColor ? color : `${color}.main`,
              borderTopLeftRadius: '5px',
              borderTopRightRadius: '5px',
              transition: 'height 0.3s'
            }}
          />
        </Box>
      )}
      
      <Box
        sx={{
          display: 'flex',
          width: '100%',
          justifyContent: 'space-between',
          mt: orientation === 'vertical' ? 0 : 0.5
        }}
      >
        <Typography variant="caption">{min}</Typography>
        <Typography variant="caption">{max}</Typography>
      </Box>
    </Box>
  );
};

export default ProgressBarWidget; 