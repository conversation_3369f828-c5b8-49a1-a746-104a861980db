import React, { useRef, useEffect } from 'react';
import { Box } from '@mui/material';

export interface IndustrialIconWidgetProps {
  iconType: string;
  color?: string;
  size?: number;
  rotation?: number;
}

export const IndustrialIconWidget: React.FC<IndustrialIconWidgetProps> = ({
  iconType,
  color = '#333333',
  size = 100,
  rotation = 0
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // 绘制工业图标
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置画布尺寸
    canvas.width = size;
    canvas.height = size;

    // 清除画布
    ctx.clearRect(0, 0, size, size);

    // 应用旋转变换
    ctx.save();
    ctx.translate(size / 2, size / 2);
    ctx.rotate((rotation * Math.PI) / 180);
    ctx.translate(-size / 2, -size / 2);

    // 设置绘图样式
    ctx.strokeStyle = color;
    ctx.fillStyle = color;
    ctx.lineWidth = size / 20;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    // 根据图标类型绘制不同的图标
    switch (iconType) {
      case 'pump':
        drawPump(ctx, size);
        break;
      case 'valve':
        drawValve(ctx, size);
        break;
      case 'tank':
        drawTank(ctx, size);
        break;
      case 'motor':
        drawMotor(ctx, size);
        break;
      case 'fan':
        drawFan(ctx, size);
        break;
      case 'heater':
        drawHeater(ctx, size);
        break;
      case 'filter':
        drawFilter(ctx, size);
        break;
      case 'reactor':
        drawReactor(ctx, size);
        break;
      case 'sensor':
        drawSensor(ctx, size);
        break;
      case 'compressor':
        drawCompressor(ctx, size);
        break;
      case 'battery':
        drawBattery(ctx, size);
        break;
      case 'gauge':
        drawGauge(ctx, size);
        break;
      case 'controlValve':
        drawControlValve(ctx, size);
        break;
      case 'switch':
        drawSwitch(ctx, size);
        break;
      case 'transformer':
        drawTransformer(ctx, size);
        break;
      case 'pipeConnection':
        drawPipeConnection(ctx, size);
        break;
      default:
        // 默认绘制一个简单的图形
        drawDefaultIcon(ctx, size);
    }

    // 恢复画布状态
    ctx.restore();
  }, [iconType, color, size, rotation]);

  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center',
        backgroundColor: 'transparent'
      }}
    >
      <canvas ref={canvasRef} style={{ maxWidth: '100%', maxHeight: '100%' }} />
    </Box>
  );
};

// 绘制泵图标
const drawPump = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.3;

  // 绘制圆形
  ctx.beginPath();
  ctx.arc(center, center, radius, 0, Math.PI * 2);
  ctx.stroke();

  // 绘制叶片
  const bladeLength = radius * 0.7;
  for (let i = 0; i < 4; i++) {
    const angle = (i * Math.PI) / 2;
    ctx.beginPath();
    ctx.moveTo(center, center);
    ctx.lineTo(
      center + Math.cos(angle) * bladeLength,
      center + Math.sin(angle) * bladeLength
    );
    ctx.stroke();
  }

  // 绘制连接管道
  ctx.beginPath();
  ctx.moveTo(size * 0.1, center);
  ctx.lineTo(center - radius, center);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center + radius, center);
  ctx.lineTo(size * 0.9, center);
  ctx.stroke();
};

// 绘制阀门图标
const drawValve = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const width = size * 0.7;
  const height = size * 0.4;

  // 绘制阀门主体
  ctx.beginPath();
  ctx.moveTo(center - width / 2, center - height / 2);
  ctx.lineTo(center + width / 2, center + height / 2);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center - width / 2, center + height / 2);
  ctx.lineTo(center + width / 2, center - height / 2);
  ctx.stroke();

  // 绘制连接管道
  ctx.beginPath();
  ctx.moveTo(size * 0.1, center);
  ctx.lineTo(center - width / 2, center);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center + width / 2, center);
  ctx.lineTo(size * 0.9, center);
  ctx.stroke();
};

// 绘制储罐图标
const drawTank = (ctx: CanvasRenderingContext2D, size: number) => {
  const width = size * 0.6;
  const height = size * 0.8;
  const x = (size - width) / 2;
  const y = (size - height) / 2;
  const radius = width / 10;

  // 绘制储罐主体
  ctx.beginPath();
  // 上部弧形
  ctx.arc(x + width / 2, y, width / 2, Math.PI, 0);
  // 右侧
  ctx.lineTo(x + width, y + height);
  // 底部弧形
  ctx.arc(x + width / 2, y + height, width / 2, 0, Math.PI);
  // 左侧
  ctx.lineTo(x, y);
  ctx.stroke();

  // 绘制液面线（装饰）
  ctx.beginPath();
  ctx.moveTo(x + radius, y + height * 0.6);
  ctx.lineTo(x + width - radius, y + height * 0.6);
  ctx.stroke();
};

// 绘制电机图标
const drawMotor = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.3;
  const squareSize = size * 0.5;

  // 绘制圆形
  ctx.beginPath();
  ctx.arc(center, center, radius, 0, Math.PI * 2);
  ctx.stroke();

  // 绘制外部方框
  ctx.beginPath();
  ctx.rect(
    center - squareSize / 2,
    center - squareSize / 2,
    squareSize,
    squareSize
  );
  ctx.stroke();

  // 绘制字母"M"
  ctx.font = `${size * 0.2}px Arial`;
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText('M', center, center);
};

// 绘制风扇图标
const drawFan = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.1;
  const bladeLength = size * 0.35;

  // 绘制中心圆
  ctx.beginPath();
  ctx.arc(center, center, radius, 0, Math.PI * 2);
  ctx.fill();

  // 绘制叶片
  for (let i = 0; i < 4; i++) {
    const angle = (i * Math.PI) / 2;
    const x1 = center + Math.cos(angle) * radius;
    const y1 = center + Math.sin(angle) * radius;
    const x2 = center + Math.cos(angle) * bladeLength;
    const y2 = center + Math.sin(angle) * bladeLength;
    
    // 绘制弧形叶片
    ctx.beginPath();
    ctx.arc(
      center,
      center,
      bladeLength,
      angle - 0.4,
      angle + 0.4
    );
    ctx.stroke();
    
    // 连接到中心
    ctx.beginPath();
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
  }
};

// 绘制加热器图标
const drawHeater = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const width = size * 0.7;
  const height = size * 0.5;
  const x = center - width / 2;
  const y = center - height / 2;

  // 绘制矩形
  ctx.beginPath();
  ctx.rect(x, y, width, height);
  ctx.stroke();

  // 绘制波浪线
  ctx.beginPath();
  ctx.moveTo(x + width * 0.1, center);
  for (let i = 1; i < 6; i++) {
    const waveHeight = height * 0.3;
    const waveX = x + width * (0.1 + i * 0.15);
    if (i % 2 === 0) {
      ctx.lineTo(waveX, center + waveHeight);
    } else {
      ctx.lineTo(waveX, center - waveHeight);
    }
  }
  ctx.stroke();
};

// 绘制过滤器图标
const drawFilter = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const width = size * 0.6;
  const height = size * 0.7;
  const x = center - width / 2;
  const y = center - height / 2;

  // 绘制漏斗形状
  ctx.beginPath();
  ctx.moveTo(x, y);
  ctx.lineTo(x + width, y);
  ctx.lineTo(x + width * 0.6, y + height);
  ctx.lineTo(x + width * 0.4, y + height);
  ctx.lineTo(x, y);
  ctx.stroke();

  // 绘制内部线条
  for (let i = 1; i < 4; i++) {
    const lineY = y + height * (i / 4);
    const shrink = (i / 4) * (width * 0.5);
    ctx.beginPath();
    ctx.moveTo(x + shrink, lineY);
    ctx.lineTo(x + width - shrink, lineY);
    ctx.stroke();
  }
};

// 绘制反应器图标
const drawReactor = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const width = size * 0.6;
  const height = size * 0.7;
  const radius = width / 2;
  const x = center - width / 2;
  const y = center - height / 2;

  // 绘制圆柱体
  ctx.beginPath();
  ctx.arc(center, y, radius, Math.PI, 0);
  ctx.lineTo(x + width, y + height);
  ctx.arc(center, y + height, radius, 0, Math.PI);
  ctx.closePath();
  ctx.stroke();

  // 绘制内部搅拌器
  ctx.beginPath();
  ctx.moveTo(center, y);
  ctx.lineTo(center, y + height);
  ctx.stroke();

  // 绘制搅拌叶片
  for (let i = 0; i < 3; i++) {
    const bladeY = y + height * (0.3 + i * 0.2);
    const bladeWidth = width * 0.4;
    ctx.beginPath();
    ctx.moveTo(center - bladeWidth / 2, bladeY);
    ctx.lineTo(center + bladeWidth / 2, bladeY);
    ctx.stroke();
  }
};

// 绘制传感器图标
const drawSensor = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.2;
  const lineLength = size * 0.7;

  // 绘制圆形
  ctx.beginPath();
  ctx.arc(center, center, radius, 0, Math.PI * 2);
  ctx.stroke();

  // 绘制连接线
  ctx.beginPath();
  ctx.moveTo(center - radius, center);
  ctx.lineTo(center - lineLength / 2, center);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center + radius, center);
  ctx.lineTo(center + lineLength / 2, center);
  ctx.stroke();

  // 绘制测量箭头
  const arrowSize = size * 0.15;
  ctx.beginPath();
  ctx.moveTo(center, center - radius);
  ctx.lineTo(center, center - radius - arrowSize);
  ctx.lineTo(center + arrowSize / 2, center - radius - arrowSize * 0.7);
  ctx.stroke();
};

// 绘制压缩机图标
const drawCompressor = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.3;
  const triangleSize = size * 0.25;

  // 绘制圆形
  ctx.beginPath();
  ctx.arc(center, center, radius, 0, Math.PI * 2);
  ctx.stroke();

  // 绘制内部三角形
  ctx.beginPath();
  ctx.moveTo(center - triangleSize, center + triangleSize);
  ctx.lineTo(center + triangleSize, center);
  ctx.lineTo(center - triangleSize, center - triangleSize);
  ctx.closePath();
  ctx.stroke();

  // 绘制连接管道
  ctx.beginPath();
  ctx.moveTo(size * 0.1, center);
  ctx.lineTo(center - radius, center);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center + radius, center);
  ctx.lineTo(size * 0.9, center);
  ctx.stroke();
};

// 绘制电池图标
const drawBattery = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const width = size * 0.5;
  const height = size * 0.7;
  const x = center - width / 2;
  const y = center - height / 2;
  const terminalWidth = width * 0.3;
  const terminalHeight = size * 0.05;

  // 绘制电池主体
  ctx.beginPath();
  ctx.rect(x, y, width, height);
  ctx.stroke();

  // 绘制电池正极端子
  ctx.beginPath();
  ctx.rect(center - terminalWidth / 2, y - terminalHeight, terminalWidth, terminalHeight);
  ctx.fill();

  // 绘制电池内部正负极标记
  // 正极
  ctx.beginPath();
  ctx.moveTo(center, y + height * 0.25 - size * 0.1);
  ctx.lineTo(center, y + height * 0.25 + size * 0.1);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center - size * 0.1, y + height * 0.25);
  ctx.lineTo(center + size * 0.1, y + height * 0.25);
  ctx.stroke();

  // 负极
  ctx.beginPath();
  ctx.moveTo(center - size * 0.1, y + height * 0.75);
  ctx.lineTo(center + size * 0.1, y + height * 0.75);
  ctx.stroke();
};

// 绘制仪表盘图标
const drawGauge = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.35;
  
  // 绘制表盘外圆
  ctx.beginPath();
  ctx.arc(center, center, radius, 0, Math.PI * 2);
  ctx.stroke();
  
  // 绘制刻度
  for (let i = 0; i < 8; i++) {
    const angle = (i * Math.PI) / 4;
    const x1 = center + Math.cos(angle) * radius;
    const y1 = center + Math.sin(angle) * radius;
    const x2 = center + Math.cos(angle) * (radius - size * 0.05);
    const y2 = center + Math.sin(angle) * (radius - size * 0.05);
    
    ctx.beginPath();
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
  }
  
  // 绘制指针
  const needleAngle = Math.PI * 0.75;
  const needleLength = radius * 0.8;
  
  ctx.beginPath();
  ctx.moveTo(center, center);
  ctx.lineTo(
    center + Math.cos(needleAngle) * needleLength,
    center + Math.sin(needleAngle) * needleLength
  );
  ctx.stroke();
  
  // 绘制中心圆点
  ctx.beginPath();
  ctx.arc(center, center, size * 0.03, 0, Math.PI * 2);
  ctx.fill();
};

// 绘制控制阀图标
const drawControlValve = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const width = size * 0.7;
  const height = size * 0.4;
  const triangleSize = size * 0.15;

  // 绘制阀门主体
  ctx.beginPath();
  ctx.moveTo(center - width / 2, center - height / 2);
  ctx.lineTo(center + width / 2, center + height / 2);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center - width / 2, center + height / 2);
  ctx.lineTo(center + width / 2, center - height / 2);
  ctx.stroke();

  // 绘制连接管道
  ctx.beginPath();
  ctx.moveTo(size * 0.1, center);
  ctx.lineTo(center - width / 2, center);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center + width / 2, center);
  ctx.lineTo(size * 0.9, center);
  ctx.stroke();

  // 绘制控制箭头（三角形）
  ctx.beginPath();
  ctx.moveTo(center, center - height - triangleSize);
  ctx.lineTo(center - triangleSize, center - height);
  ctx.lineTo(center + triangleSize, center - height);
  ctx.closePath();
  ctx.stroke();

  // 绘制控制杆
  ctx.beginPath();
  ctx.moveTo(center, center - height);
  ctx.lineTo(center, center);
  ctx.stroke();
};

// 绘制开关图标
const drawSwitch = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const width = size * 0.6;
  const height = size * 0.6;
  const x = center - width / 2;
  const y = center - height / 2;
  
  // 绘制开关外框
  ctx.beginPath();
  ctx.rect(x, y, width, height);
  ctx.stroke();
  
  // 绘制开关杆
  const switchPos = size * 0.2; // 开关位置（左右偏移量）
  
  ctx.beginPath();
  ctx.moveTo(center - switchPos, y);
  ctx.lineTo(center + switchPos, y + height);
  ctx.stroke();
  
  // 绘制接线点
  const dotRadius = size * 0.04;
  
  // 顶部接线点
  ctx.beginPath();
  ctx.arc(center - switchPos, y, dotRadius, 0, Math.PI * 2);
  ctx.fill();
  
  ctx.beginPath();
  ctx.arc(center + switchPos, y, dotRadius, 0, Math.PI * 2);
  ctx.fill();
  
  // 底部接线点
  ctx.beginPath();
  ctx.arc(center - switchPos, y + height, dotRadius, 0, Math.PI * 2);
  ctx.fill();
  
  ctx.beginPath();
  ctx.arc(center + switchPos, y + height, dotRadius, 0, Math.PI * 2);
  ctx.fill();
};

// 绘制变压器图标
const drawTransformer = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const circleRadius = size * 0.15;
  const distance = size * 0.2;
  
  // 绘制左侧线圈
  ctx.beginPath();
  ctx.arc(center - distance, center, circleRadius, 0, Math.PI * 2);
  ctx.stroke();
  
  // 绘制右侧线圈
  ctx.beginPath();
  ctx.arc(center + distance, center, circleRadius, 0, Math.PI * 2);
  ctx.stroke();
  
  // 绘制中间铁芯
  const coreHeight = circleRadius * 3;
  const coreWidth = size * 0.05;
  
  ctx.beginPath();
  ctx.rect(center - coreWidth / 2, center - coreHeight / 2, coreWidth, coreHeight);
  ctx.stroke();
  
  // 绘制左侧连接线
  ctx.beginPath();
  ctx.moveTo(size * 0.1, center - circleRadius);
  ctx.lineTo(center - distance - circleRadius, center - circleRadius);
  ctx.stroke();
  
  ctx.beginPath();
  ctx.moveTo(size * 0.1, center + circleRadius);
  ctx.lineTo(center - distance - circleRadius, center + circleRadius);
  ctx.stroke();
  
  // 绘制右侧连接线
  ctx.beginPath();
  ctx.moveTo(size * 0.9, center - circleRadius);
  ctx.lineTo(center + distance + circleRadius, center - circleRadius);
  ctx.stroke();
  
  ctx.beginPath();
  ctx.moveTo(size * 0.9, center + circleRadius);
  ctx.lineTo(center + distance + circleRadius, center + circleRadius);
  ctx.stroke();
};

// 绘制管道连接图标
const drawPipeConnection = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.08;
  const pipeLength = size * 0.35;
  
  // 绘制中心连接点
  ctx.beginPath();
  ctx.arc(center, center, radius * 1.5, 0, Math.PI * 2);
  ctx.fill();
  
  // 绘制四通管道
  const directions = [0, Math.PI / 2, Math.PI, Math.PI * 1.5]; // 东南西北四个方向
  
  directions.forEach(angle => {
    const x1 = center + Math.cos(angle) * radius * 1.5;
    const y1 = center + Math.sin(angle) * radius * 1.5;
    const x2 = center + Math.cos(angle) * pipeLength;
    const y2 = center + Math.sin(angle) * pipeLength;
    
    // 绘制管道
    ctx.beginPath();
    ctx.moveTo(x1, y1);
    ctx.lineTo(x2, y2);
    ctx.stroke();
    
    // 绘制管道端点
    ctx.beginPath();
    ctx.arc(x2, y2, radius, 0, Math.PI * 2);
    ctx.stroke();
  });
};

// 绘制默认图标
const drawDefaultIcon = (ctx: CanvasRenderingContext2D, size: number) => {
  const center = size / 2;
  const radius = size * 0.3;

  // 绘制圆形
  ctx.beginPath();
  ctx.arc(center, center, radius, 0, Math.PI * 2);
  ctx.stroke();

  // 绘制十字
  ctx.beginPath();
  ctx.moveTo(center - radius * 0.7, center);
  ctx.lineTo(center + radius * 0.7, center);
  ctx.stroke();

  ctx.beginPath();
  ctx.moveTo(center, center - radius * 0.7);
  ctx.lineTo(center, center + radius * 0.7);
  ctx.stroke();
};

export default IndustrialIconWidget; 