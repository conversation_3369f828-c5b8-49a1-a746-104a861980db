import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';
import * as echarts from 'echarts';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface GaugeWidgetProps {
  min: number;
  max: number;
  value: number;
  title: string;
  unit: string;
  precision?: number;
  showValue?: boolean;
  color?: string;
  topicValue?: MqttValue | null;
}

export const GaugeWidget: React.FC<GaugeWidgetProps> = ({ 
  min = 0, 
  max = 100, 
  value = 50, 
  title = '仪表盘',
  unit = '%',
  precision = 1,
  showValue = true,
  color = '#2196f3',
  topicValue = null
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  
  // 获取实际显示值，优先使用主题值（如果是数值类型）
  const displayValue = React.useMemo(() => {
    if (topicValue && topicValue.dataType === 'number') {
      return Number(topicValue.value);
    }
    return value;
  }, [topicValue, value]);
  
  // 渲染图表的函数，抽取为单独的函数以便多处调用
  const renderChart = () => {
    if (!chartRef.current) return;
    
    // 确保图表实例存在
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    } else {
      // 当容器大小变化时，调整图表大小
      chartInstance.current.resize();
    }
    
    // 确保数值在范围内
    const safeValue = Math.min(Math.max(displayValue, min), max);
    const percentage = ((safeValue - min) / (max - min)) * 100;
    
    // 根据传入的颜色创建渐变色数组
    const getColorGradient = () => {
      if (!color) return [
        [0.3, '#67e0e3'],
        [0.7, '#37a2da'],
        [1, '#fd666d']
      ];
      
      // 根据自定义颜色生成渐变
      const baseColor = color;
      const lighterColor = adjustBrightness(baseColor, 40); // 亮一些的颜色
      const darkerColor = adjustBrightness(baseColor, -20); // 暗一些的颜色
      
      return [
        [0.3, lighterColor],
        [0.7, baseColor],
        [1, darkerColor]
      ];
    };
    
    // 辅助函数：调整颜色亮度
    const adjustBrightness = (hex: string, percent: number) => {
      // 去掉可能存在的#前缀
      const hexValue = hex.replace('#', '');
      
      // 解析RGB值
      let r = parseInt(hexValue.substr(0, 2), 16);
      let g = parseInt(hexValue.substr(2, 2), 16);
      let b = parseInt(hexValue.substr(4, 2), 16);
      
      // 调整亮度
      r = Math.min(255, Math.max(0, r + percent));
      g = Math.min(255, Math.max(0, g + percent));
      b = Math.min(255, Math.max(0, b + percent));
      
      // 转回16进制
      return '#' + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
    };
    
    // 配置图表选项
    const option = {
      series: [
        {
          type: 'gauge',
          startAngle: 180,
          endAngle: 0,
          center: ['50%', '75%'],
          radius: '90%',
          min: 0,
          max: 100,
          splitNumber: 10,
          axisLine: {
            lineStyle: {
              width: 6,
              color: getColorGradient()
            }
          },
          pointer: {
            icon: 'path://M12.8,0.7l12,40.1H0.7L12.8,0.7z',
            length: '12%',
            width: 20,
            offsetCenter: [0, '-60%'],
            itemStyle: {
              color: 'inherit'
            }
          },
          axisTick: {
            length: 12,
            lineStyle: {
              color: 'inherit',
              width: 2
            }
          },
          splitLine: {
            length: 20,
            lineStyle: {
              color: 'inherit',
              width: 3
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 10,
            distance: -60,
            formatter: function(value: number) {
              return Math.round(min + (value / 100) * (max - min));
            }
          },
          title: {
            offsetCenter: [0, '-20%'],
            fontSize: 14
          },
          detail: {
            fontSize: 14,
            offsetCenter: [0, '0%'],
            valueAnimation: true,
            formatter: function(_value: number) {
              // 使用精度设置格式化值
              return showValue 
                ? safeValue.toFixed(typeof precision === 'number' ? precision : 1) + ' ' + unit
                : '';
            },
            color: 'inherit'
          },
          data: [
            {
              value: percentage,
              name: title
            }
          ]
        }
      ]
    };
    
    // 设置图表选项
    chartInstance.current.setOption(option);
  };
  
  // 初始化和数据变化时更新图表
  useEffect(() => {
    renderChart();
    
    // 清理函数
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, [min, max, displayValue, title, unit, precision, showValue, color]);

  // 创建一个ResizeObserver来监听容器大小变化
  useEffect(() => {
    if (!chartRef.current) return;
    
    const resizeObserver = new ResizeObserver(() => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    });
    
    resizeObserver.observe(chartRef.current);
    
    return () => {
      if (chartRef.current) {
        resizeObserver.unobserve(chartRef.current);
      }
      resizeObserver.disconnect();
    };
  }, []);

  // 窗口大小改变时重新渲染图表
  useEffect(() => {
    const handleResize = () => {
      renderChart();
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [min, max, displayValue, title, unit, precision, showValue, color]);

  // 当MQTT数据更新时重新渲染图表
  useEffect(() => {
    if (topicValue) {
      renderChart();
    }
  }, [topicValue]);

  return (
    <Box sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
    </Box>
  );
};

export default GaugeWidget; 