import React from 'react';
import { Box, Typography } from '@mui/material';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface LedWidgetProps {
  state?: boolean;
  isOn?: boolean;
  color?: string;
  onColor?: string;
  offColor?: string;
  size?: 'small' | 'medium' | 'large';
  label: string;
  topicValue?: MqttValue | null;
}

export const LedWidget: React.FC<LedWidgetProps> = ({
  state,
  isOn,
  color = '#f44336',
  onColor = '#4caf50',
  offColor = '#bdbdbd',
  size = 'medium',
  label,
  topicValue = null
}) => {
  // 使用isOn或state确定初始状态，优先使用isOn
  const initialState = isOn !== undefined ? isOn : (state !== undefined ? state : false);

  // 根据MQTT数据确定实际状态
  const actualState = React.useMemo(() => {
    if (topicValue) {
      if (topicValue.dataType === 'boolean') {
        return Boolean(topicValue.value);
      } else if (topicValue.dataType === 'number') {
        // 对于数值类型，将非0视为true
        return Number(topicValue.value) !== 0;
      } else if (typeof topicValue.value === 'string') {
        // 对于字符串，检查是否为"true"
        return topicValue.value.toLowerCase() === 'true';
      }
    }
    return initialState;
  }, [topicValue, initialState]);

  // 确定使用的颜色 - 如果提供了单一color，优先使用它
  const getLedColor = () => {
    if (actualState) {
      return color !== '#f44336' ? color : onColor; // 如果设置了自定义color就用它，否则用onColor
    } else {
      return offColor;
    }
  };

  // 根据size确定LED尺寸
  const ledSize = {
    small: 20,
    medium: 40,
    large: 60
  }[size];
  
  // 获取当前应该显示的颜色
  const currentColor = getLedColor();
  
  // 闪烁效果 - 可以通过CSS animation实现
  const ledStyle = {
    width: ledSize,
    height: ledSize,
    borderRadius: '50%',
    backgroundColor: currentColor,
    boxShadow: actualState ? `0 0 10px 2px ${currentColor}` : 'none',
    transition: 'all 0.3s ease',
    opacity: actualState ? 1 : 0.5,
    border: '2px solid #555'
  };
  
  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 1
      }}
    >
      <Box sx={ledStyle} />
      
      <Typography 
        variant="caption" 
        sx={{ 
          mt: 1,
          textAlign: 'center',
          fontWeight: actualState ? 'bold' : 'normal',
          color: actualState ? currentColor : 'text.secondary'
        }}
      >
        {label}
      </Typography>
    </Box>
  );
};

export default LedWidget; 