import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Box, Typography } from '@mui/material';
import * as echarts from 'echarts';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface ChartWidgetProps {
  chartType: string;
  dataSource: string;
  title: string;
  maxPoints?: number;
  topicValue?: MqttValue | null;
}

export const ChartWidget: React.FC<ChartWidgetProps> = ({ 
  chartType = 'line', 
  dataSource = '', 
  title = 'Chart',
  maxPoints = 50,
  topicValue = null
}) => {
  const chartRef = useRef<HTMLDivElement>(null);
  const chartInstance = useRef<echarts.ECharts | null>(null);
  const lastValueRef = useRef<any>(null);
  const lastUpdateTimeRef = useRef<number>(0);
  
  // 保存历史数据的状态
  const [dataHistory, setDataHistory] = useState<Array<number>>([]);
  // 保存时间戳的状态
  const [timeLabels, setTimeLabels] = useState<Array<string>>([]);
  // 跟踪是否有数据的状态
  const [hasData, setHasData] = useState(false);
  
  // 处理MQTT数据更新
  useEffect(() => {
    // 如果没有数据或者值没变，跳过更新
    if (!topicValue || topicValue.value === lastValueRef.current) {
      return;
    }
    
    // 添加防抖逻辑，限制更新频率
    const now = Date.now();
    if (now - lastUpdateTimeRef.current < 300) {
      return; // 如果更新太频繁，直接跳过
    }
    
    lastValueRef.current = topicValue.value;
    lastUpdateTimeRef.current = now;
    
    // 将MQTT数据转换为数值
    let numericValue: number;
    if (topicValue.dataType === 'number') {
      numericValue = Number(topicValue.value);
    } else if (topicValue.dataType === 'boolean') {
      numericValue = topicValue.value ? 1 : 0;
    } else if (topicValue.dataType === 'string' && typeof topicValue.value === 'string') {
      const parsed = parseFloat(topicValue.value);
      numericValue = isNaN(parsed) ? 0 : parsed;
    } else {
      numericValue = 0;
    }

    // 添加新数据点
    setDataHistory(prev => {
      const newData = [...prev, numericValue];
      // 保持数据长度在最大值以内
      return newData.length > maxPoints ? newData.slice(-maxPoints) : newData;
    });
    
    // 生成时间标签
    const date = new Date();
    const timeLabel = `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}:${date.getSeconds().toString().padStart(2, '0')}`;
    
    setTimeLabels(prev => {
      const newLabels = [...prev, timeLabel];
      // 保持标签长度与数据同步
      return newLabels.length > maxPoints ? newLabels.slice(-maxPoints) : newLabels;
    });

    // 标记已有数据
    if (!hasData) {
      setHasData(true);
    }
  }, [topicValue, maxPoints, hasData]);
  
  // 渲染图表的函数
  const renderChart = useCallback(() => {
    if (!chartRef.current) return;
    
    // 确保图表实例存在
    if (!chartInstance.current) {
      chartInstance.current = echarts.init(chartRef.current);
    } else {
      // 当容器大小变化时，调整图表大小
      chartInstance.current.resize();
    }
    
    // 如果没有数据，显示无数据状态
    if (dataHistory.length === 0) {
      chartInstance.current.setOption({
        title: {
          text: title,
          left: 'center',
          textStyle: {
            fontSize: 14
          },
          subtext: dataSource || '',
          subtextStyle: {
            fontSize: 10,
            color: '#999'
          }
        },
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'middle',
            style: {
              text: 'Waiting for data...',
              fontSize: 14,
              fill: '#999'
            }
          }
        ]
      });
      return;
    }
    
    // 根据chartType设置不同类型的图表
    let seriesType = 'line';
    let areaStyle = undefined;
    
    switch (chartType) {
      case 'bar':
        seriesType = 'bar';
        break;
      case 'area':
        seriesType = 'line';
        areaStyle = {};
        break;
      default:
        seriesType = 'line';
    }
    
    // 配置图表选项
    const option = {
      title: {
        text: title,
        left: 'center',
        textStyle: {
          fontSize: 14
        },
        subtext: dataSource || '',
        subtextStyle: {
          fontSize: 10,
          color: '#999'
        }
      },
      tooltip: {
        trigger: 'axis',
        formatter: function(params: any) {
          const dataIndex = params[0].dataIndex;
          const time = timeLabels[dataIndex] || '';
          const value = params[0].value;
          return `${time}<br/>${params[0].seriesName}: ${value}`;
        }
      },
      grid: {
        left: '10%',
        right: '10%',
        bottom: '15%',
        top: '20%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: timeLabels,
        axisLabel: {
          fontSize: 10,
          interval: Math.max(1, Math.floor(timeLabels.length / 8))
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          fontSize: 10
        },
        scale: true
      },
      series: [
        {
          name: dataSource || 'Data',
          type: seriesType,
          data: dataHistory,
          areaStyle: areaStyle,
          smooth: true,
          emphasis: {
            focus: 'series'
          },
          animation: true,
          animationDuration: 500
        }
      ]
    };
    
    // 设置图表选项
    chartInstance.current.setOption(option, true);
  }, [dataHistory, timeLabels, chartType, title, dataSource]);
  
  // 数据变化时更新图表 - 使用防抖处理
  useEffect(() => {
    let timeoutId: NodeJS.Timeout | null = null;
    
    if (dataHistory.length > 0) {
      // 添加延迟避免频繁更新
      timeoutId = setTimeout(() => {
        renderChart();
      }, 100);
    }
    
    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [dataHistory, renderChart]);

  // 组件挂载和卸载时的处理
  useEffect(() => {
    // 当组件挂载时创建图表
    renderChart();
    
    // 清理函数
    return () => {
      if (chartInstance.current) {
        chartInstance.current.dispose();
        chartInstance.current = null;
      }
    };
  }, []);

  // 创建一个ResizeObserver来监听容器大小变化
  useEffect(() => {
    if (!chartRef.current) return;
    
    const resizeObserver = new ResizeObserver(() => {
      if (chartInstance.current) {
        chartInstance.current.resize();
      }
    });
    
    resizeObserver.observe(chartRef.current);
    
    return () => {
      if (chartRef.current) {
        resizeObserver.unobserve(chartRef.current);
      }
      resizeObserver.disconnect();
    };
  }, []);

  // 窗口大小改变时重新渲染图表 - 添加防抖
  useEffect(() => {
    let resizeTimeoutId: NodeJS.Timeout | null = null;
    
    const handleResize = () => {
      if (resizeTimeoutId) {
        clearTimeout(resizeTimeoutId);
      }
      
      resizeTimeoutId = setTimeout(() => {
        renderChart();
      }, 300);
    };
    
    window.addEventListener('resize', handleResize);
    
    return () => {
      if (resizeTimeoutId) {
        clearTimeout(resizeTimeoutId);
      }
      window.removeEventListener('resize', handleResize);
    };
  }, [renderChart]);

  return (
    <Box sx={{ width: '100%', height: '100%', position: 'relative' }}>
      <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
      {!topicValue && (
        <Box sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          background: 'rgba(255,255,255,0.7)',
          zIndex: 10
        }}>
          <Typography variant="caption" color="text.secondary">
            Please bind data topic
          </Typography>
        </Box>
      )}
    </Box>
  );
};

// 使用React.memo包装组件
export default React.memo(ChartWidget); 