import React, { useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import LoopIcon from '@mui/icons-material/Loop';
import PowerIcon from '@mui/icons-material/Power';
import { MqttValue } from '../../../../context/MqttDataContext';

export interface MotorWidgetProps {
  running: boolean;
  speed: number;
  direction: 'clockwise' | 'counterclockwise';
  label: string;
  topicValue?: MqttValue | null;
}

export const MotorWidget: React.FC<MotorWidgetProps> = ({
  running,
  speed,
  direction,
  label,
  topicValue = null
}) => {
  // 根据MQTT数据确定电机状态
  const actualRunning = useMemo(() => {
    if (topicValue) {
      if (topicValue.dataType === 'boolean') {
        return Boolean(topicValue.value);
      } else if (topicValue.dataType === 'number') {
        return Number(topicValue.value) > 0;
      } else if (topicValue.dataType === 'string' && typeof topicValue.value === 'string') {
        const val = topicValue.value.toLowerCase();
        return val === 'true' || val === 'on' || val === '1' || val === 'running';
      }
    }
    return running;
  }, [topicValue, running]);

  // 根据MQTT数据确定电机速度
  const actualSpeed = useMemo(() => {
    if (topicValue && topicValue.dataType === 'number') {
      return Number(topicValue.value);
    }
    return speed;
  }, [topicValue, speed]);
  
  // 根据速度计算旋转动画的持续时间
  const rotationDuration = actualSpeed > 0 ? 5 / (actualSpeed / 50) : 0;
  
  // 根据运行状态和方向设置旋转样式
  const getRotationStyle = () => {
    if (!actualRunning) return {};
    
    return {
      animation: `rotate${direction === 'clockwise' ? '' : 'CounterClockwise'} ${rotationDuration}s linear infinite`
    };
  };
  
  return (
    <Box 
      sx={{ 
        width: '100%', 
        height: '100%', 
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        p: 1,
        // 添加关键帧动画
        '@keyframes rotate': {
          '0%': {
            transform: 'rotate(0deg)'
          },
          '100%': {
            transform: 'rotate(360deg)'
          }
        },
        '@keyframes rotateCounterClockwise': {
          '0%': {
            transform: 'rotate(0deg)'
          },
          '100%': {
            transform: 'rotate(-360deg)'
          }
        }
      }}
    >
      <Typography 
        variant="subtitle2"
        sx={{ 
          mb: 1,
          color: actualRunning ? '#4caf50' : '#f44336',
          fontWeight: 'bold'
        }}
      >
        {label}
      </Typography>
      
      <Box 
        sx={{ 
          position: 'relative',
          width: '70%',
          height: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}
      >
        {/* 电机外壳 */}
        <Box 
          sx={{ 
            width: '100%',
            height: '100%',
            bgcolor: '#e0e0e0',
            borderRadius: '10px',
            border: '2px solid #9e9e9e',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* 电机轮轴 */}
          <Box 
            sx={{ 
              width: '60%',
              height: '60%',
              borderRadius: '50%',
              bgcolor: '#9e9e9e',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              ...getRotationStyle()
            }}
          >
            <LoopIcon 
              sx={{ 
                color: 'white',
                fontSize: 30,
                transform: direction === 'counterclockwise' ? 'scaleX(-1)' : 'none'
              }}
            />
          </Box>
          
          {/* 运行状态指示灯 */}
          <Box 
            sx={{ 
              position: 'absolute',
              top: '10px',
              right: '10px',
              width: '12px',
              height: '12px',
              borderRadius: '50%',
              bgcolor: actualRunning ? '#4caf50' : '#f44336',
              boxShadow: actualRunning ? '0 0 5px #4caf50' : 'none',
            }}
          />
        </Box>
      </Box>
      
      <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <PowerIcon sx={{ mr: 0.5, color: actualRunning ? '#4caf50' : '#9e9e9e', fontSize: 16 }} />
        <Typography variant="caption">
          {actualRunning ? '运行中' : '停止'} - {actualSpeed}%
        </Typography>
      </Box>
    </Box>
  );
};

export default MotorWidget; 