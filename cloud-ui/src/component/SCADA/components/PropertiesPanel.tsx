import React, { useCallback, useState, useEffect, useMemo, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Divider,
  Stack,
  IconButton,
  Tooltip,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft';
import ChevronRightIcon from '@mui/icons-material/ChevronRight';
import { useTranslation } from 'react-i18next';
import { SelectChangeEvent } from '@mui/material/Select';

// 导入MQTT主题服务
import { MQTTTopic } from '../../../services/topicService';
import { useMqttData, useTopicValue } from '../../../context/MqttDataContext';

// 导入各组件的属性编辑器
import ButtonWidgetEditor from './widgets/editors/ButtonWidgetEditor';
import ChartWidgetEditor from './widgets/editors/ChartWidgetEditor';
import GaugeWidgetEditor from './widgets/editors/GaugeWidgetEditor';
import ImageWidgetEditor from './widgets/editors/ImageWidgetEditor';
import IndustrialIconWidgetEditor from './widgets/editors/IndustrialIconWidgetEditor';
import LabelWidgetEditor from './widgets/editors/LabelWidgetEditor';
import LedWidgetEditor from './widgets/editors/LedWidgetEditor';
import MotorWidgetEditor from './widgets/editors/MotorWidgetEditor';
import PipeWidgetEditor from './widgets/editors/PipeWidgetEditor';
import ProgressBarWidgetEditor from './widgets/editors/ProgressBarWidgetEditor';
import SensorWidgetEditor from './widgets/editors/SensorWidgetEditor';
import SliderWidgetEditor from './widgets/editors/SliderWidgetEditor';
import SwitchWidgetEditor from './widgets/editors/SwitchWidgetEditor';
import TableWidgetEditor from './widgets/editors/TableWidgetEditor';
import TankWidgetEditor from './widgets/editors/TankWidgetEditor';
import TextWidgetEditor from './widgets/editors/TextWidgetEditor';

export interface PropertiesPanelProps {
  selectedElement: any | null;
  onPropertyChange: (widgetId: string, property: string, value: any) => void;
  onClose?: () => void;
  availableTopics?: MQTTTopic[];
  isLoadingTopics?: boolean;
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  selectedElement,
  onPropertyChange,
  onClose,
  availableTopics = [],
  isLoadingTopics = false
}) => {
  const { t } = useTranslation();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { topicValues, batchSubscribe } = useMqttData();
  
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  
  // 获取选中组件的主题值 - 使用主动订阅模式
  const topicValue = useTopicValue(selectedTopic);
  
  // 维护一个已订阅主题列表的引用
  const subscribedTopicRef = useRef<string | null>(null);
  
  // 确保即使未显示属性面板时，也已经订阅了主题
  useEffect(() => {
    // 检查是否是禁用主题绑定的组件类型
    const disableTopicBinding = selectedElement?.type === 'image' || 
                                selectedElement?.type === 'pipe' || 
                                selectedElement?.type === 'industrialIcon'
    
    // 检查主题是否已经订阅或者变化
    const topicChanged = subscribedTopicRef.current !== selectedTopic;
    
    // 只有在不是禁用主题绑定的组件并且有设置主题时才订阅
    if (selectedTopic && !disableTopicBinding) {
      // 如果主题发生变化，进行订阅
      if (topicChanged) {
        console.log('[PropertiesPanel] 属性面板订阅主题:', selectedTopic);
        // 使用批量订阅方式订阅主题，确保数据能够正确获取
        batchSubscribe([selectedTopic]);
        
        // 更新已订阅主题引用
        subscribedTopicRef.current = selectedTopic;
      }
    }
  }, [selectedTopic, batchSubscribe, selectedElement]);
  
  // 通过memo获取当前主题值，避免重复渲染
  const currentTopicValue = useMemo(() => {
    if (!selectedTopic) return null;
    return topicValues[selectedTopic] || null;
  }, [selectedTopic, topicValues]);

  // 处理当前选中主题不在主题列表中的情况
  const currentTopicsWithSelected = useMemo(() => {
    // 如果还没有加载或者没有选中主题，直接返回原始主题列表
    if (!selectedTopic || availableTopics.length === 0) return availableTopics;
    
    // 检查选中的主题是否已经在列表中
    const topicExists = availableTopics.some(t => t.topic === selectedTopic);
    
    // 如果不在列表中，添加一个临时选项
    if (!topicExists) {
      // 创建临时主题对象
      const dataType = topicValues[selectedTopic]?.dataType || 'string';
      const tempTopic: MQTTTopic = {
        id: `temp-${selectedTopic}`,
        topic: selectedTopic,
        data_type: dataType,
        device_sn: '当前设备'
      };
      
      // 返回包含临时主题的新列表
      return [...availableTopics, tempTopic];
    }
    
    // 如果已存在，直接返回原列表
    return availableTopics;
  }, [selectedTopic, availableTopics, topicValues]);
  
  const toggleCollapse = () => {
    setIsCollapsed(!isCollapsed);
  };

  // 使用传入的主题列表，不再自己获取
  useEffect(() => {
    // 不需要设置topics，因为我们直接使用availableTopics
    // 保留这个effect以便将来可能需要做一些额外处理
  }, [availableTopics]);

  // 当选中组件变化时，更新选中的主题
  useEffect(() => {
    if (selectedElement && selectedElement.type === 'mqtt') {
      const topic = selectedElement.properties.topic || '';
      setSelectedTopic(topic);
    } else {
      setSelectedTopic('');
    }
  }, [selectedElement]);

  const handleChange = useCallback((property: string, value: any) => {
    if (selectedElement) {
      onPropertyChange(selectedElement.id, property, value);
    }
  }, [selectedElement, onPropertyChange]);

  // 处理主题选择变化
  const handleTopicChange = (event: SelectChangeEvent<string>) => {
    const newTopic = event.target.value;
    setSelectedTopic(newTopic);
    if (selectedElement && selectedElement.type === 'mqtt') {
      onPropertyChange(selectedElement.id, 'topic', newTopic);
    }
  };

  // 渲染实时数据显示部分
  const renderLiveDataSection = () => {
    if (!selectedTopic) return null;
    
    // 使用当前topicValue或者通过topicValues直接获取的值
    const displayValue = topicValue || currentTopicValue;
    
    return (
      <Box className="property-section" sx={{ mt: 2 }}>
        <Typography variant="subtitle2" className="property-title">
          {t('scada.properties.liveData')}
        </Typography>
        <Box sx={{ my: 1 }}>
          {displayValue ? (
            <Box sx={{ 
              p: 1.5, 
              border: '1px solid', 
              borderColor: 'divider', 
              borderRadius: 1,
              backgroundColor: 'action.hover'
            }}>

              <TextField
                size="small"
                variant="outlined"
                value={
                  typeof displayValue.value === 'object' 
                    ? JSON.stringify(displayValue.value) 
                    : String(displayValue.value)
                }
                fullWidth
                InputProps={{
                  readOnly: true,
                }}
                sx={{ mb: 1 }}
              />
              <Typography variant="caption" color="text.secondary">
                {new Date(displayValue.timestamp).toLocaleString()}
              </Typography>
            </Box>
          ) : (
            <Alert severity="info" variant="outlined" sx={{ mt: 1 }}>
              {t('scada.properties.noDataReceived')}
            </Alert>
          )}
        </Box>
      </Box>
    );
  };

  // 渲染通用属性
  const renderCommonProperties = () => {
    // 按设备ID对主题进行分组
    const groupedTopics: { [key: string]: MQTTTopic[] } = {};
    currentTopicsWithSelected.forEach(topic => {
      const deviceId = topic.device_sn || '未知设备';
      if (!groupedTopics[deviceId]) {
        groupedTopics[deviceId] = [];
      }
      groupedTopics[deviceId].push(topic);
    });

    // 确定组件是否应该禁用主题绑定功能
    const disableTopicBinding = selectedElement.type === 'image' || 
                                selectedElement.type === 'pipe' || 
                                selectedElement.type === 'industrialIcon'

    return (
      <Box className="property-section">
        <Typography variant="subtitle2" className="property-title">{t('scada.properties.basicProperties')}</Typography>
        <Stack spacing={2}>
          <TextField
            size="small"
            label={t('scada.properties.common.id')}
            value={selectedElement.id}
            disabled
            fullWidth
            margin="dense"
          />
          <TextField
            size="small"
            label={t('scada.properties.common.type')}
            value={selectedElement.type}
            disabled
            fullWidth
            margin="dense"
          />
          
          {!disableTopicBinding ? (
            <FormControl size="small" fullWidth margin="dense">
              <InputLabel id="scada-dataTag-label">{t('scada.properties.dataTag')}</InputLabel>
              <Select
                labelId="scada-dataTag-label"
                value={selectedElement.properties?.topic || ''}
                onChange={(e) => handleChange('topic', e.target.value)}
                label={t('scada.properties.dataTag')}
                fullWidth
                endAdornment={isLoadingTopics ? <CircularProgress size={20} sx={{ mr: 2 }} /> : null}
              >
                <MenuItem value="">
                  <em>{t('scada.properties.noDataTag')}</em>
                </MenuItem>
                
                {/* 按设备分组显示主题 */}
                {Object.entries(groupedTopics).map(([deviceId, deviceTopics]) => [
                  <MenuItem 
                    key={`device-${deviceId}`} 
                    disabled 
                    sx={{ 
                      color: 'text.secondary',
                      py: 0.5
                    }}
                  >
                    {`${deviceId}`}
                  </MenuItem>,
                  ...deviceTopics.map(topic => (
                    <MenuItem key={topic.id} value={topic.topic} sx={{ pl: 3 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
                        <Typography variant="body2" noWrap sx={{ maxWidth: '70%' }}>
                          {topic.topic.split('/').slice(1).join('/')}
                        </Typography>
                        <Chip 
                          label={topic.data_type} 
                          size="small" 
                          color={topic.data_type === 'number' ? 'primary' : topic.data_type === 'boolean' ? 'success' : 'default'}
                          variant="outlined"
                          sx={{ ml: 1, minWidth: 60 }}
                        />
                      </Box>
                    </MenuItem>
                  ))
                ]).flat()}
                
                {currentTopicsWithSelected.length === 0 && !isLoadingTopics && (
                  <MenuItem disabled sx={{ color: 'text.secondary', fontStyle: 'italic' }}>
                    {t('scada.properties.noAvailableTopics')}
                  </MenuItem>
                )}
              </Select>
            </FormControl>
          ): null}
        </Stack>
        
        {/* 实时数据显示 */}
        {selectedElement.properties?.topic && !disableTopicBinding && renderLiveDataSection()}
      </Box>
    );
  };

  // 根据组件类型渲染对应的属性编辑器
  const renderComponentEditor = () => {
    const commonProps = {
      properties: selectedElement.properties || {},
      onPropertyChange: handleChange
    };

    switch (selectedElement.type) {
      case 'button':
        return <ButtonWidgetEditor {...commonProps} />;
      case 'chart':
        return <ChartWidgetEditor {...commonProps} />;
      case 'gauge':
        return <GaugeWidgetEditor {...commonProps} />;
      case 'image':
        return <ImageWidgetEditor {...commonProps} />;
      case 'industrialIcon':
        return <IndustrialIconWidgetEditor {...commonProps} />;
      case 'label':
        return <LabelWidgetEditor {...commonProps} />;
      case 'led':
        return <LedWidgetEditor {...commonProps} />;
      case 'motor':
        return <MotorWidgetEditor {...commonProps} />;
      case 'pipe':
        return <PipeWidgetEditor {...commonProps} />;
      case 'progressbar':
        return <ProgressBarWidgetEditor {...commonProps} />;
      case 'sensor':
        return <SensorWidgetEditor {...commonProps} />;
      case 'slider':
        return <SliderWidgetEditor {...commonProps} />;
      case 'switch':
        return <SwitchWidgetEditor {...commonProps} />;
      case 'table':
        return <TableWidgetEditor {...commonProps} />;
      case 'tank':
        return <TankWidgetEditor {...commonProps} />;
      case 'text':
        return <TextWidgetEditor {...commonProps} />;
      default:
        return null;
    }
  };

  // 获取组件类型的名称
  const getComponentTypeName = () => {
    if (!selectedElement) return '';
    const key = `scada.components.types.${selectedElement.type}`;
    return t(key, { defaultValue: selectedElement.type });
  };

  // 处理MQTT部分的代码
  const renderMqttSection = () => {
    return (
      <FormControl fullWidth size="small" sx={{ mb: 2 }}>
        <InputLabel>主题</InputLabel>
        <Select
          value={selectedTopic}
          onChange={handleTopicChange}
          label="主题"
          size="small"
        >
          <MenuItem value="">
            <em>无</em>
          </MenuItem>
          {currentTopicsWithSelected.map((topic) => (
            <MenuItem key={topic.id} value={topic.topic}>
              {topic.topic}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    );
  };

  // 渲染空面板
  if (!selectedElement) {
    return (
      <Paper 
        sx={{
          width: 280, 
          p: 2, 
          overflowY: 'auto', 
          position: 'absolute',
          right: 0,
          top: 64,
          bottom: 0,
          zIndex: 100,
          borderLeft: '1px solid rgba(0, 0, 0, 0.12)',
          boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.1)',
          transition: 'all 0.3s ease-in-out',
          maxHeight: 'calc(100vh - 64px)',
          userSelect: 'none',
          WebkitUserSelect: 'none',
          MozUserSelect: 'none',
          msUserSelect: 'none'
        }}
      >
        <Typography variant="subtitle1" fontWeight="bold">{t('scada.properties.panelTitle')}</Typography>
        <Box sx={{ py: 4, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <Typography variant="body2" color="text.secondary">
            {t('scada.properties.selectComponent')}
          </Typography>
        </Box>
      </Paper>
    );
  }

  // 渲染折叠状态的面板
  if (isCollapsed) {
    return (
      <Paper 
        sx={{ 
          width: 'auto', 
          position: 'absolute',
          right: 0,
          top: 120,
          zIndex: 100,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          py: 1,
          px: 0.5,
          borderLeft: '1px solid rgba(0, 0, 0, 0.12)',
          borderTop: '1px solid rgba(0, 0, 0, 0.12)',
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
          transition: 'all 0.3s ease-in-out',
          userSelect: 'none',
          WebkitUserSelect: 'none',
          MozUserSelect: 'none',
          msUserSelect: 'none',
          borderRadius: '4px 0 0 4px',
          boxShadow: '-2px 2px 8px rgba(0, 0, 0, 0.08)',
          height: 'auto',
          mr: 0,
          backgroundColor: 'background.paper'
        }}
      >
        <Tooltip title={t('scada.properties.expandPanel')} placement="left">
          <IconButton
            size="small"
            onClick={toggleCollapse}
            sx={{
              backgroundColor: 'action.hover',
              mb: 1,
              '&:hover': {
                backgroundColor: 'primary.light',
                color: 'primary.contrastText'
              }
            }}
          >
            <ChevronLeftIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        
        {selectedElement && (
          <Tooltip title={getComponentTypeName()} placement="left">
            <Box
              sx={{
                width: 28,
                height: 28,
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                borderRadius: '50%',
                border: '1px dashed',
                borderColor: 'primary.light',
                color: 'primary.main',
                fontSize: 14,
                fontWeight: 'bold',
                backgroundColor: 'rgba(25, 118, 210, 0.05)'
              }}
            >
              {selectedElement.type.charAt(0).toUpperCase()}
            </Box>
          </Tooltip>
        )}
        
        {onClose && (
          <Tooltip title={t('scada.properties.closePanel')} placement="left">
            <IconButton 
              size="small" 
              onClick={onClose}
              sx={{ 
                mt: 1,
                color: 'text.secondary',
                '&:hover': {
                  backgroundColor: 'error.light',
                  color: 'error.contrastText'
                }
              }}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        )}
      </Paper>
    );
  }

  // 渲染完整面板
  return (
    <Paper 
      sx={{
        width: 280, 
        p: 2, 
        overflowY: 'auto', 
        position: 'absolute',
        right: 0,
        top: 64,
        bottom: 0,
        zIndex: 100,
        borderLeft: '1px solid rgba(0, 0, 0, 0.12)',
        boxShadow: '-2px 0 8px rgba(0, 0, 0, 0.1)',
        transition: 'all 0.3s ease-in-out',
        maxHeight: 'calc(100vh - 64px)',
        userSelect: 'none',
        WebkitUserSelect: 'none',
        MozUserSelect: 'none',
        msUserSelect: 'none'
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        <Tooltip title={t('scada.properties.collapsePanel')}>
          <IconButton
            size="small"
            onClick={toggleCollapse}
            sx={{ 
              mr: 0.5,
              backgroundColor: 'action.hover',
              '&:hover': {
                backgroundColor: 'primary.light',
                color: 'primary.contrastText'
              }
            }}
            aria-label={t('scada.properties.collapsePanel')}
          >
            <ChevronRightIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Typography variant="subtitle1" fontWeight="bold">
          {`${getComponentTypeName()} ${t('scada.properties.panelTitle')}`}
        </Typography>
        <Box>
          {onClose && (
            <Tooltip title={t('scada.properties.closePanel')}>
              <IconButton 
                size="small" 
                onClick={onClose}
                sx={{ 
                  ml: 0.5,
                  color: 'text.secondary',
                  '&:hover': {
                    backgroundColor: 'error.light',
                    color: 'error.contrastText'
                  }
                }}
                aria-label={t('scada.properties.closePanel')}
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          )}
        </Box>
      </Box>
      
      <Divider sx={{ my: 1 }} />
      
      {renderCommonProperties()}
      <Divider sx={{ my: 1 }} />
      {selectedElement?.type === 'mqtt' && renderMqttSection()}
      {renderComponentEditor()}
    </Paper>
  );
};

export default PropertiesPanel; 