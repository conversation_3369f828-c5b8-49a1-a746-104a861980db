import React, { useEffect, useState, useMemo } from 'react';
import { Box, CircularProgress, Typography, IconButton, Tooltip } from '@mui/material';
import { useParams, useNavigate } from 'react-router-dom';
import { HMICanvas } from './HMICanvas';
import { getSCADAProject } from '../../../services/scadaService';
import { useTranslation } from 'react-i18next';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useMqttData } from '../../../context/MqttDataContext';

export const SCADAPreview: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [projectData, setProjectData] = useState<any>(null);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { batchSubscribe } = useMqttData();

  // 收集所有需要订阅的主题
  const topicsToSubscribe = useMemo(() => {
    if (!projectData?.widgets) return [];
    
    return projectData.widgets
      .filter((widget: any) => widget.properties?.topic)
      .map((widget: any) => widget.properties.topic);
  }, [projectData]);

  // 订阅所有组件的主题
  useEffect(() => {
    if (topicsToSubscribe.length > 0) {
      // console.log('[SCADAPreview] 订阅主题:', topicsToSubscribe);
      batchSubscribe(topicsToSubscribe);
    }
  }, [topicsToSubscribe, batchSubscribe]);

  // 计算画布内容的尺寸
  // const contentSize = useMemo(() => {
  //   if (!projectData?.widgets || projectData.widgets.length === 0) {
  //     return { width: 0, height: 0 };
  //   }

  //   // 计算所有组件的边界
  //   let minX = Infinity, minY = Infinity;
  //   let maxX = -Infinity, maxY = -Infinity;
  //   const gridSize = 10; // 与HMICanvas中使用的相同
    
  //   projectData.widgets.forEach((widget: any) => {
  //     const widgetRight = widget.x + widget.w * gridSize;
  //     const widgetBottom = widget.y + widget.h * gridSize;
      
  //     minX = Math.min(minX, widget.x);
  //     minY = Math.min(minY, widget.y);
  //     maxX = Math.max(maxX, widgetRight);
  //     maxY = Math.max(maxY, widgetBottom);
  //   });
    
  //   // 内容的总宽高
  //   return { 
  //     width: maxX - minX + 200, // 添加边距
  //     height: maxY - minY + 200
  //   };
  // }, [projectData]);

  useEffect(() => {
    const loadProject = async () => {
      if (!projectId) {
        setError(t('scada.preview.noProjectId'));
        setLoading(false);
        return;
      }

      try {
        const data = await getSCADAProject(projectId);
        setProjectData(data);
      } catch (err) {
        console.error('Failed to load project:', err);
        setError(t('scada.preview.loadError'));
      } finally {
        setLoading(false);
      }
    };

    loadProject();
    
    // 阻止整个文档的默认右键菜单，使画布拖拽功能正常工作
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
    };
    
    document.addEventListener('contextmenu', handleContextMenu);
    
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, [projectId, t]);

  const handleBackToEditor = () => {
    navigate('/dashboard/scada');
  };

  if (loading) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: 2
      }}>
        <CircularProgress />
        <Typography variant="body1" color="text.secondary">
          {t('scada.preview.loading')}
        </Typography>
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: 2
      }}>
        <Typography color="error">{error}</Typography>
        <Tooltip title={t('scada.preview.backToEditor')}>
          <IconButton onClick={handleBackToEditor} color="primary">
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  if (!projectData) {
    return (
      <Box sx={{ 
        display: 'flex', 
        justifyContent: 'center', 
        alignItems: 'center', 
        height: '100vh',
        flexDirection: 'column',
        gap: 2
      }}>
        <Typography color="error">{t('scada.preview.projectNotFound')}</Typography>
        <Tooltip title={t('scada.preview.backToEditor')}>
          <IconButton onClick={handleBackToEditor} color="primary">
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
      </Box>
    );
  }

  return (
    <Box sx={{ 
      width: '100vw', 
      height: '100vh', 
      overflow: 'hidden', // 外层容器保持overflow: hidden防止滚动条
      position: 'relative',
      cursor: 'default' // 默认鼠标样式
    }}>
      <Box sx={{ 
        position: 'absolute', 
        top: 16, 
        left: 16, 
        zIndex: 1000,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        borderRadius: 1,
        p: 1
      }}>
        <Tooltip title={t('scada.preview.backToEditor')}>
          <IconButton onClick={handleBackToEditor} color="primary">
            <ArrowBackIcon />
          </IconButton>
        </Tooltip>
      </Box>
      <div className="canvas-container" style={{ 
        width: '100%', 
        height: '100%', 
        position: 'relative',
        overflow: 'hidden', // 容器保持hidden，但内部画布设为visible
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}>
        <div className="canvas-wrapper" style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          overflow: 'visible', // 关键：将包装器设置为overflow: visible
        }}>
          <HMICanvas
            widgets={projectData.widgets}
            showGrid={false}
            selectedElement={null}
            onWidgetSelect={() => {}}
            onLayoutChange={() => {}}
            isPreviewMode={true}
            gridSize={10}
          />
        </div>
      </div>
    </Box>
  );
};

export default SCADAPreview; 