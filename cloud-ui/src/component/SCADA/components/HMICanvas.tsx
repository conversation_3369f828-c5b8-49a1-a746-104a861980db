import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { Box, Typography } from '@mui/material';
import { GaugeWidget } from './widgets/GaugeWidget';
import { ChartWidget } from './widgets/ChartWidget';
import { ButtonWidget } from './widgets/ButtonWidget';
import { TextWidget } from './widgets/TextWidget';
import { TankWidget } from './widgets/TankWidget';
import { PipeWidget } from './widgets/PipeWidget';
import { SliderWidget } from './widgets/SliderWidget';
import { SwitchWidget } from './widgets/SwitchWidget';
import { LedWidget } from './widgets/LedWidget';
import { SensorWidget } from './widgets/SensorWidget';
import { TableWidget } from './widgets/TableWidget';
import { ProgressBarWidget } from './widgets/ProgressBarWidget';
import { MotorWidget } from './widgets/MotorWidget';
import { ImageWidget } from './widgets/ImageWidget';
import { IndustrialIconWidget } from './widgets/IndustrialIconWidget';
import { useMqttData, MqttValue } from '../../../context/MqttDataContext';

export interface HMICanvasProps {
  widgets: any[];
  showGrid: boolean;
  selectedElement: any | null;
  onWidgetSelect: (widget: any) => void;
  onLayoutChange: (widgets: any[]) => void;
  isPreviewMode?: boolean;
  gridSize?: number;
}

export const HMICanvas: React.FC<HMICanvasProps> = React.memo(({
  widgets,
  showGrid,
  selectedElement,
  onWidgetSelect,
  onLayoutChange,
  isPreviewMode = false,
  gridSize = 20
}) => {
  const [draggedWidget, setDraggedWidget] = useState<string | null>(null);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [resizingWidget, setResizingWidget] = useState<string | null>(null);
  const [resizeDirection, setResizeDirection] = useState('');
  const canvasRef = useRef<HTMLDivElement>(null);
  const [isDraggingOrResizing, setIsDraggingOrResizing] = useState(false);
  
  // 添加画布拖拽状态
  const [canvasDrag, setCanvasDrag] = useState<{
    dragging: boolean;
    startX: number;
    startY: number;
    offsetX: number;
    offsetY: number;
  }>({
    dragging: false,
    startX: 0,
    startY: 0,
    offsetX: 0,
    offsetY: 0
  });
  
  // 保存画布尺寸和内容尺寸信息
  const [canvasBounds, setCanvasBounds] = useState({
    minX: -Infinity,
    maxX: Infinity,
    minY: -Infinity, 
    maxY: Infinity,
    contentWidth: 0,
    contentHeight: 0,
    viewportWidth: 0,
    viewportHeight: 0
  });
  
  const { topicValues, batchSubscribe, batchUnsubscribe } = useMqttData();
  
  const subscribedTopics = useRef<Set<string>>(new Set());
  const previousWidgetsRef = useRef<any[]>([]);
  const isFirstRender = useRef<boolean>(true);
  // 用于防止拖动时重复订阅
  const isDraggingRef = useRef<boolean>(false);
  
  // 设置拖动状态
  useEffect(() => {
    isDraggingRef.current = isDraggingOrResizing;
  }, [isDraggingOrResizing]);
  
  // 从组件配置中获取所有主题
  const getTopicsFromWidgets = useCallback((widgetsList: any[]) => {
    const topics = new Set<string>();
    
    widgetsList.forEach(widget => {
      if (widget.properties?.topic && 
          typeof widget.properties.topic === 'string' && 
          widget.properties.topic.trim() !== '') {
        topics.add(widget.properties.topic.trim());
      }
    });
    
    return topics;
  }, []);

  // 主题订阅管理 - 使用useCallback优化函数
  const updateSubscriptions = useCallback(() => {
    // 如果正在拖动，跳过订阅更新
    if (isDraggingRef.current) {
      return;
    }

    const activeTopics = getTopicsFromWidgets(widgets);
    
    // 如果主题列表没有变化，跳过更新
    if (!isFirstRender.current) {
      const prevTopics = getTopicsFromWidgets(previousWidgetsRef.current);
      const sameTopics = 
        activeTopics.size === prevTopics.size && 
        [...activeTopics].every(topic => prevTopics.has(topic));
      
      if (sameTopics) {
        return;
      }
    }
    
    isFirstRender.current = false;
    
    // 查找需要订阅的新主题
    const topicsToSubscribe: string[] = [];
    activeTopics.forEach(topic => {
      if (!subscribedTopics.current.has(topic)) {
        topicsToSubscribe.push(topic);
      }
    });
    
    // 查找需要取消的主题
    const topicsToUnsubscribe: string[] = [];
    subscribedTopics.current.forEach(topic => {
      if (!activeTopics.has(topic)) {
        topicsToUnsubscribe.push(topic);
      }
    });
    
    // 批量订阅新主题 - 一次性发送所有主题
    if (topicsToSubscribe.length > 0) {
      console.log('[HMICanvas] 批量订阅主题数量:', topicsToSubscribe.length);
      batchSubscribe(topicsToSubscribe);
      
      // 更新已订阅集合
      topicsToSubscribe.forEach(topic => {
        subscribedTopics.current.add(topic);
      });
    }
    
    // 批量取消不再使用的主题 - 一次性发送所有取消订阅
    if (topicsToUnsubscribe.length > 0) {
      console.log('[HMICanvas] 批量取消订阅主题数量:', topicsToUnsubscribe.length);
      batchUnsubscribe(topicsToUnsubscribe);
      
      // 从已订阅集合中移除
      topicsToUnsubscribe.forEach(topic => {
        subscribedTopics.current.delete(topic);
      });
    }
    
    // 保存当前组件状态以便下次比较
    previousWidgetsRef.current = JSON.parse(JSON.stringify(widgets));
  }, [widgets, batchSubscribe, batchUnsubscribe, getTopicsFromWidgets]);
  
  // 组件挂载和widgets变化时更新订阅
  useEffect(() => {
    // 收集所有实际要订阅的主题
    const allTopicsToSubscribe = new Set<string>();
    
    // 收集当前组件的所有主题
    widgets.forEach(widget => {
      if (widget.properties?.topic && 
          typeof widget.properties.topic === 'string' && 
          widget.properties.topic.trim() !== '') {
        const topic = widget.properties.topic.trim();
        // 避免重复添加
        if (!subscribedTopics.current.has(topic)) {
          allTopicsToSubscribe.add(topic);
        }
      }
    });
    
    // 如果有需要订阅的主题，一次性批量订阅
    if (allTopicsToSubscribe.size > 0) {
      console.log('[HMICanvas] Numbers of topics to subscribe:', allTopicsToSubscribe.size);
      const topicsArray = Array.from(allTopicsToSubscribe);
      batchSubscribe(topicsArray);
      
      // 添加到已订阅集合
      topicsArray.forEach(topic => {
        subscribedTopics.current.add(topic);
      });
    }
    
    // 触发完整订阅更新以处理取消订阅等操作
    updateSubscriptions();
    
    // 如果仍然有问题，可以再次尝试重新订阅（作为备份）
    const retryTimeoutId = setTimeout(() => {
      // 如果首次渲染，强制再次执行订阅
      if (isFirstRender.current) {
        updateSubscriptions();
      }
    }, 500);
    
    return () => {
      clearTimeout(retryTimeoutId);
    };
  }, [widgets, updateSubscriptions, batchSubscribe]);
  
  // 确保在组件挂载时就对所有绑定的主题进行订阅
  useEffect(() => {
    // 组件首次挂载时，立即订阅所有主题
    updateSubscriptions();
    
    // 监听topicValues变化，确保组件能够响应MQTT数据更新
    const topicValuesInterval = setInterval(() => {
      let hasUpdates = false;
      subscribedTopics.current.forEach(topic => {
        const value = topicValues[topic];
        if (value) {
          const widget = widgets.find(w => 
            w.properties?.topic === topic && 
            value.timestamp > (w.properties.lastUpdated || 0)
          );
          
          if (widget) {
            hasUpdates = true;
            widget.properties.lastUpdated = value.timestamp;
          }
        }
      });
      
      if (hasUpdates) {
        setRenderKey(prev => prev + 1);
      }
    }, 100); // 降低至100ms检查一次更新，提高实时性
    
    return () => {
      clearInterval(topicValuesInterval);
    };
  }, []);
  
  // 直接监听 topicValues 变化以立即触发渲染更新
  useEffect(() => {
    // 遍历所有订阅的主题检查是否有数据更新
    let updatedWidgets = false;
    
    if (subscribedTopics.current.size > 0) {
      subscribedTopics.current.forEach(topic => {
        const topicValue = topicValues[topic];
        if (topicValue) {
          // 查找使用此主题的组件
          const widget = widgets.find(w => w.properties?.topic === topic);
          if (widget) {
            // 检查是否需要更新
            if (!widget.properties.lastMqttUpdate || widget.properties.lastMqttUpdate < topicValue.timestamp) {
              updatedWidgets = true;
              // 更新时间戳以避免重复渲染
              widget.properties.lastMqttUpdate = topicValue.timestamp;
            }
          }
        }
      });
    }
    
    // 如果有组件需要更新，触发重新渲染
    if (updatedWidgets) {
      // 使用异步更新以避免过于频繁的渲染阻塞UI
      setTimeout(() => {
        setRenderKey(prev => prev + 1);
      }, 0);
    }
  }, [topicValues, widgets]);
  
  // 监听topicValues变化主动触发渲染更新
  useEffect(() => {
    const hasUpdates = Object.keys(topicValues).some(topic => {
      if (!subscribedTopics.current.has(topic)) return false;
      
      const value = topicValues[topic];
      const widget = widgets.find(w => 
        w.properties?.topic === topic
      );
      
      if (widget && (!widget.properties.lastCheckedTimestamp || widget.properties.lastCheckedTimestamp < value.timestamp)) {
        // 更新最后检查时间戳
        widget.properties.lastCheckedTimestamp = value.timestamp;
        return true;
      }
      
      return false;
    });
    
    if (hasUpdates) {
      // 触发重新渲染
      setRenderKey(prev => prev + 1);
    }
  }, [topicValues, widgets]);
  
  // 强制重新渲染的机制
  const [renderKey, setRenderKey] = useState(0);
  
  const handleDragStart = useCallback((e: React.MouseEvent, widget: any) => {
    if (isPreviewMode) return;
    
    e.stopPropagation();
    if (!selectedElement || selectedElement.id !== widget.id) {
      onWidgetSelect(widget);
    }
    
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    });
    
    setDraggedWidget(widget.id);
    setIsDraggingOrResizing(true);
    isDraggingRef.current = true;
  }, [isPreviewMode, selectedElement, onWidgetSelect]);
  
  const handleDrag = useCallback((e: React.MouseEvent) => {
    if (!draggedWidget || !canvasRef.current) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const canvasRect = canvasRef.current.getBoundingClientRect();
    const widget = widgets.find(w => w.id === draggedWidget);
    
    if (widget) {
      let newX = e.clientX - canvasRect.left - dragOffset.x;
      let newY = e.clientY - canvasRect.top - dragOffset.y;
      
      newX = Math.max(0, Math.min(newX, canvasRect.width - widget.w * gridSize));
      newY = Math.max(0, Math.min(newY, canvasRect.height - widget.h * gridSize));
      
      const updatedWidgets = widgets.map(w => {
        if (w.id === draggedWidget) {
          return {
            ...w,
            x: newX,
            y: newY
          };
        }
        return w;
      });
      
      onLayoutChange(updatedWidgets);
    }
  }, [draggedWidget, widgets, gridSize, dragOffset, onLayoutChange]);
  
  const handleDragEnd = useCallback(() => {
    setDraggedWidget(null);
    setTimeout(() => {
      setIsDraggingOrResizing(false);
      isDraggingRef.current = false;
      // 拖动结束后更新订阅
      updateSubscriptions();
    }, 100);
  }, [updateSubscriptions]);
  
  // 添加被删除的 handleResizeStart, handleResize 和 handleResizeEnd 函数
  const handleResizeStart = useCallback((e: React.MouseEvent, widget: any, direction: string) => {
    if (isPreviewMode) return;
    
    e.stopPropagation();
    e.preventDefault();
    
    if (!selectedElement || selectedElement.id !== widget.id) {
      onWidgetSelect(widget);
    }
    
    setResizingWidget(widget.id);
    setResizeDirection(direction);
    setIsDraggingOrResizing(true);
    isDraggingRef.current = true; // 防止调整大小时触发订阅
  }, [isPreviewMode, selectedElement, onWidgetSelect]);
  
  const handleResize = useCallback((e: React.MouseEvent) => {
    if (!resizingWidget || !canvasRef.current) return;
    
    e.preventDefault();
    e.stopPropagation();
    
    const canvasRect = canvasRef.current.getBoundingClientRect();
    const widget = widgets.find(w => w.id === resizingWidget);
    
    if (widget) {
      let newW = widget.w;
      let newH = widget.h;
      let newX = widget.x;
      let newY = widget.y;
      
      const mouseX = e.clientX - canvasRect.left;
      const mouseY = e.clientY - canvasRect.top;
      
      if (resizeDirection.includes('e')) {
        newW = Math.max(1, (mouseX - widget.x) / gridSize);
      }
      if (resizeDirection.includes('s')) {
        newH = Math.max(1, (mouseY - widget.y) / gridSize);
      }
      if (resizeDirection.includes('w')) {
        const newWidth = widget.x + widget.w * gridSize - mouseX;
        if (newWidth >= gridSize) {
          newW = newWidth / gridSize;
          newX = mouseX;
        }
      }
      if (resizeDirection.includes('n')) {
        const newHeight = widget.y + widget.h * gridSize - mouseY;
        if (newHeight >= gridSize) {
          newH = newHeight / gridSize;
          newY = mouseY;
        }
      }
      
      const updatedWidgets = widgets.map(w => {
        if (w.id === resizingWidget) {
          return {
            ...w,
            x: newX,
            y: newY,
            w: newW,
            h: newH
          };
        }
        return w;
      });
      
      onLayoutChange(updatedWidgets);
    }
  }, [resizingWidget, widgets, resizeDirection, gridSize, onLayoutChange]);
  
  const handleResizeEnd = useCallback(() => {
    setResizingWidget(null);
    setResizeDirection('');
    setTimeout(() => {
      setIsDraggingOrResizing(false);
      isDraggingRef.current = false;
      // 调整结束后更新订阅
      updateSubscriptions();
    }, 100);
  }, [updateSubscriptions]);
  
  // 重新添加处理鼠标移动和释放的回调
  const handleMouseMove = useCallback((e: React.MouseEvent) => {
    if (draggedWidget) {
      handleDrag(e);
    } else if (resizingWidget) {
      handleResize(e);
    }
  }, [draggedWidget, resizingWidget, handleDrag, handleResize]);
  
  const handleMouseUp = useCallback(() => {
    if (draggedWidget) {
      handleDragEnd();
    } else if (resizingWidget) {
      handleResizeEnd();
    }
  }, [draggedWidget, resizingWidget, handleDragEnd, handleResizeEnd]);
  
  // 计算画布内容的边界，用于限制拖动范围
  useEffect(() => {
    if (!canvasRef.current || widgets.length === 0) return;

    const viewportWidth = canvasRef.current.clientWidth;
    const viewportHeight = canvasRef.current.clientHeight;
    
    // 计算所有组件的边界
    let minX = Infinity, minY = Infinity;
    let maxX = -Infinity, maxY = -Infinity;
    
    widgets.forEach(widget => {
      const widgetRight = widget.x + widget.w * gridSize;
      const widgetBottom = widget.y + widget.h * gridSize;
      
      minX = Math.min(minX, widget.x);
      minY = Math.min(minY, widget.y);
      maxX = Math.max(maxX, widgetRight);
      maxY = Math.max(maxY, widgetBottom);
    });
    
    // 内容的总宽高
    const contentWidth = maxX - minX;
    const contentHeight = maxY - minY;
    
    // 计算可拖动的范围
    // 左上角边界：保证不能拖动超过内容的右下部分，同时留出边距
    // 右下角边界：保证不能拖动超过内容的左上部分，同时留出边距
    const margin = 100; // 至少保留100px可见区域
    const maxOffsetX = Math.max(contentWidth - margin, 0);
    const maxOffsetY = Math.max(contentHeight - margin, 0);

    setCanvasBounds({
      minX: -maxOffsetX,
      maxX: viewportWidth - margin,
      minY: -maxOffsetY, 
      maxY: viewportHeight - margin,
      contentWidth,
      contentHeight,
      viewportWidth,
      viewportHeight
    });
  }, [widgets, gridSize]);
  
  // 处理画布右键拖拽开始
  const handleCanvasRightMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.button !== 2) return; // 只处理右键点击
    
    e.preventDefault();
    e.stopPropagation();
    
    setCanvasDrag({
      dragging: true,
      startX: e.clientX,
      startY: e.clientY,
      offsetX: canvasDrag.offsetX,
      offsetY: canvasDrag.offsetY
    });
  }, [canvasDrag.offsetX, canvasDrag.offsetY]);
  
  // 处理画布拖拽移动
  const handleCanvasRightMouseMove = useCallback((e: MouseEvent) => {
    if (!canvasDrag.dragging) return;
    
    const deltaX = e.clientX - canvasDrag.startX;
    const deltaY = e.clientY - canvasDrag.startY;
    
    // 计算新的偏移量，并限制在安全范围内
    let newOffsetX = canvasDrag.offsetX + deltaX;
    let newOffsetY = canvasDrag.offsetY + deltaY;
    
    // 限制拖动范围，确保画布不会完全拖出视口
    // 对于小于视口的内容，确保不会拖得太远
    // 对于大于视口的内容，确保至少有一部分内容可见
    newOffsetX = Math.max(canvasBounds.minX, Math.min(newOffsetX, canvasBounds.maxX));
    newOffsetY = Math.max(canvasBounds.minY, Math.min(newOffsetY, canvasBounds.maxY));
    
    if (canvasRef.current) {
      canvasRef.current.style.transform = `translate(${newOffsetX}px, ${newOffsetY}px)`;
    }
  }, [canvasDrag, canvasBounds]);
  
  // 处理画布拖拽结束
  const handleCanvasRightMouseUp = useCallback(() => {
    if (!canvasDrag.dragging) return;
    
    const transformStyle = canvasRef.current?.style.transform || '';
    const match = transformStyle.match(/translate\((-?\d+(?:\.\d+)?)px,\s*(-?\d+(?:\.\d+)?)px\)/);
    
    const newOffsetX = match ? parseFloat(match[1]) : canvasDrag.offsetX;
    const newOffsetY = match ? parseFloat(match[2]) : canvasDrag.offsetY;
    
    setCanvasDrag({
      dragging: false,
      startX: 0,
      startY: 0,
      offsetX: newOffsetX,
      offsetY: newOffsetY
    });
  }, [canvasDrag]);
  
  useEffect(() => {
    const handleGlobalMouseMove = (e: MouseEvent) => {
      if (draggedWidget || resizingWidget) {
        const mouseEvent = e as unknown as React.MouseEvent;
        handleMouseMove(mouseEvent);
      }
      
      // 处理画布拖拽移动
      if (canvasDrag.dragging) {
        handleCanvasRightMouseMove(e);
      }
    };
    
    const handleGlobalMouseUp = (e: MouseEvent) => {
      if (draggedWidget || resizingWidget) {
        handleMouseUp();
      }
      
      // 处理画布拖拽结束
      if (canvasDrag.dragging && e.button === 2) {
        handleCanvasRightMouseUp();
      }
    };
    
    const handleContextMenu = (e: MouseEvent) => {
      // 阻止右键菜单
      if (canvasRef.current?.contains(e.target as Node)) {
        e.preventDefault();
      }
    };
    
    document.addEventListener('mousemove', handleGlobalMouseMove);
    document.addEventListener('mouseup', handleGlobalMouseUp);
    document.addEventListener('contextmenu', handleContextMenu);
    
    return () => {
      document.removeEventListener('mousemove', handleGlobalMouseMove);
      document.removeEventListener('mouseup', handleGlobalMouseUp);
      document.removeEventListener('contextmenu', handleContextMenu);
    };
  }, [draggedWidget, resizingWidget, canvasDrag.dragging, handleMouseMove, handleMouseUp, handleCanvasRightMouseMove, handleCanvasRightMouseUp]);

  const renderWidget = useCallback((widget: any) => {
    if (!widget || !widget.id || !widget.type || 
        typeof widget.x !== 'number' || 
        typeof widget.y !== 'number' ||
        typeof widget.w !== 'number' ||
        typeof widget.h !== 'number') {
      console.error('Invalid widget skipped during render:', widget);
      return null;
    }
    
    const isSelected = selectedElement && selectedElement.id === widget.id && !isPreviewMode;
    
    // 获取关联的MQTT值，无论组件是否被选中
    let topicValue: MqttValue | null = null;
    
    if (widget.properties?.topic && typeof widget.properties.topic === 'string' && widget.properties.topic.trim() !== '') {
      const topic = widget.properties.topic.trim();
      
      // 直接从 topicValues 获取最新值，确保始终使用最新数据
      topicValue = topicValues[topic] || null;
      
      // 初始化lastUpdated
      if (!widget.properties.lastUpdated) {
        widget.properties.lastUpdated = 0;
      }
      
      // 检查数据是否变化 - 如果有值且时间戳更新则标记变化
      if (topicValue && topicValue.timestamp > widget.properties.lastUpdated) {
        widget.properties.lastUpdated = topicValue.timestamp;
      }
    }

    // 生成一个总是包含最新数据的组件 key，确保在数据更新时重新渲染
    const mqttTimestamp = topicValue ? topicValue.timestamp : 0;
    const widgetKey = `${widget.type}-${widget.id}`;

    const baseStyle: React.CSSProperties = {
      position: 'absolute',
      left: `${widget.x}px`,
      top: `${widget.y}px`,
      width: `${widget.w * gridSize}px`,
      height: `${widget.h * gridSize}px`,
      zIndex: widget.zIndex || 0,
      boxSizing: 'border-box',
      cursor: isPreviewMode ? 'default' : (isSelected ? 'move' : 'pointer'),
      transform: 'translateZ(0)',
      willChange: isSelected ? 'left, top, width, height' : 'auto'
    };
    
    if (!widget.properties) {
      widget.properties = {};
    }
    
    const handleWidgetClick = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      
      if (isDraggingOrResizing) return;
      
      onWidgetSelect(widget);
    };
    
    const resizeHandleStyle: React.CSSProperties = {
      position: 'absolute',
      width: '8px',
      height: '8px',
      background: '#2196f3',
      border: '1px solid #fff',
      zIndex: 1
    };
    
    let content;
    
    try {
      // 从 topicValue 中提取最新值，确保组件使用最新数据
      const currentValue = topicValue ? topicValue.value : widget.properties.value;
      
      switch (widget.type) {
      case 'gauge':
        content = (
          <GaugeWidget 
            min={widget.properties.min} 
            max={widget.properties.max} 
            value={currentValue ?? widget.properties.value} 
            title={widget.properties.title}
            unit={widget.properties.unit}
            precision={widget.properties.precision}
            showValue={widget.properties.showValue}
            color={widget.properties.color}
            topicValue={topicValue}
            key={`gauge-inner-${widget.id}`}
          />
        );
        break;
        
      case 'chart':
        content = (
          <ChartWidget 
            chartType={widget.properties.chartType}
            dataSource={widget.properties.dataSource}
            title={widget.properties.title}
            maxPoints={widget.properties.maxPoints}
            topicValue={topicValue}
            key={`chart-inner-${widget.id}`}
          />
        );
        break;
        
      case 'button':
        content = (
          <ButtonWidget 
            label={widget.properties.label}
            action={widget.properties.action}
            targetTag={widget.properties.targetTag}
            key={`button-inner-${widget.id}`}
          />
        );
        break;
        
      case 'text':
        content = (
          <TextWidget 
            text={currentValue !== undefined ? String(currentValue) : widget.properties.text}
            fontSize={widget.properties.fontSize}
            color={widget.properties.color}
            alignment={widget.properties.alignment}
            key={`text-inner-${widget.id}`}
          />
        );
        break;
        
      case 'tank':
        content = (
          <TankWidget 
            min={widget.properties.min}
            max={widget.properties.max}
            value={currentValue ?? widget.properties.value}
            unit={widget.properties.unit}
            fillColor={widget.properties.fillColor}
            title={widget.properties.title}
            key={`tank-inner-${widget.id}`}
          />
        );
        break;
        
      case 'pipe':
        content = (
          <PipeWidget 
            flowDirection={widget.properties.flowDirection}
            flowSpeed={widget.properties.flowSpeed}
            flowActive={currentValue ?? widget.properties.flowActive}
            pipeColor={widget.properties.pipeColor}
            fluidColor={widget.properties.fluidColor}
            key={`pipe-inner-${widget.id}`}
          />
        );
        break;

      case 'slider':
        content = (
          <SliderWidget 
            min={widget.properties.min}
            max={widget.properties.max}
            value={currentValue ?? widget.properties.value}
            unit={widget.properties.unit}
            showLabels={widget.properties.showLabels}
            orientation={widget.properties.orientation}
            isPreviewMode={isPreviewMode}
            step={widget.properties.step}
            topic={widget.properties.topic}
            key={`slider-inner-${widget.id}`}
          />
        );
        break;

      case 'switch':
        content = (
          <SwitchWidget 
            state={currentValue ?? widget.properties.state}
            onColor={widget.properties.onColor}
            offColor={widget.properties.offColor}
            label={widget.properties.label}
            isPreviewMode={isPreviewMode}
            topic={widget.properties.topic}
            topicValue={topicValue}
            key={`switch-inner-${widget.id}`}
          />
        );
        break;

      case 'led':
        content = (
          <LedWidget 
            state={currentValue ?? widget.properties.state}
            isOn={widget.properties.isOn}
            color={widget.properties.color}
            onColor={widget.properties.onColor}
            offColor={widget.properties.offColor}
            size={widget.properties.size}
            label={widget.properties.label}
            topicValue={topicValue}
            key={`led-inner-${widget.id}`}
          />
        );
        break;

      case 'sensor':
        content = (
          <SensorWidget 
            value={currentValue ?? widget.properties.value}
            unit={widget.properties.unit}
            title={widget.properties.title}
            precision={widget.properties.precision}
            type={widget.properties.type}
            key={`sensor-inner-${widget.id}`}
          />
        );
        break;
      case 'image':
        content = (
          <ImageWidget 
            src={widget.properties.src}
            isEditMode={isPreviewMode ? false : true}
            key={`image-inner-${widget.id}`}
          />
        );
        break;
      case 'table':
        content = (
          <TableWidget 
            columns={widget.properties.columns}
            dataSource={widget.properties.dataSource}
            topicValue={topicValue}
            title={widget.properties.title}
            showTitle={widget.properties.showTitle}
            selectable={widget.properties.selectable}
            density={widget.properties.density}
            showIndex={widget.properties.showIndex}
            pageSize={widget.properties.pageSize}
            paginationPosition={widget.properties.paginationPosition}
            sortable={widget.properties.sortable}
            key={`table-inner-${widget.id}`}
          />
        );
        break;

      case 'progressbar':
        content = (
          <ProgressBarWidget 
            min={widget.properties.min}
            max={widget.properties.max}
            value={currentValue ?? widget.properties.value}
            showValue={widget.properties.showValue}
            color={widget.properties.color}
            orientation={widget.properties.orientation}
            key={`progressbar-inner-${widget.id}`}
          />
        );
        break;

      case 'motor':
        content = (
          <MotorWidget 
            running={currentValue ?? widget.properties.running}
            speed={widget.properties.speed}
            direction={widget.properties.direction}
            label={widget.properties.label}
            key={`motor-inner-${widget.id}`}
          />
        );
        break;

      case 'industrialIcon':
        content = (
          <IndustrialIconWidget 
            iconType={widget.properties.iconType || 'pump'}
            color={widget.properties.color || '#333333'}
            rotation={widget.properties.rotation || 0}
            size={Math.min(widget.w, widget.h) * gridSize * 0.8}
            key={`icon-inner-${widget.id}`}
          />
        );
        break;

      default:
        content = (
          <Box 
            sx={{ 
              width: '100%', 
              height: '100%', 
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px dashed #aaa',
              borderRadius: '4px',
              backgroundColor: 'rgba(200, 200, 200, 0.2)',
              color: 'text.secondary'
            }}
          >
            <Typography variant="caption">不支持的组件类型: {widget.type}</Typography>
          </Box>
        );
      }
    } catch (error) {
      console.error(`Error rendering widget ${widget.id} of type ${widget.type}:`, error);
      content = (
        <Box 
          sx={{ 
            width: '100%', 
            height: '100%', 
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: '#ffdddd',
            p: 1,
            borderRadius: '4px',
            border: '1px dashed #f44336'
          }}
        >
          <div style={{ fontWeight: 'bold', color: '#f44336', marginBottom: '8px' }}>
            Error
          </div>
          <div style={{ fontSize: '12px', textAlign: 'center' }}>
            Failed to render {widget.type}
          </div>
        </Box>
      );
    }
    
    return (
      <div 
        style={baseStyle} 
        onClick={isPreviewMode ? undefined : handleWidgetClick}
        onMouseDown={isPreviewMode ? undefined : (e) => handleDragStart(e, widget)}
        className={isSelected ? 'selected' : ''}
        key={widgetKey}
        data-mqtt-updated={mqttTimestamp}
      >
        {isSelected && !isPreviewMode && (
          <>
            <div 
              style={{ ...resizeHandleStyle, top: '-5px', left: '-5px', cursor: 'nw-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 'nw')}
            />
            <div 
              style={{ ...resizeHandleStyle, top: '-5px', right: '-5px', cursor: 'ne-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 'ne')}
            />
            <div 
              style={{ ...resizeHandleStyle, bottom: '-5px', left: '-5px', cursor: 'sw-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 'sw')}
            />
            <div 
              style={{ ...resizeHandleStyle, bottom: '-5px', right: '-5px', cursor: 'se-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 'se')}
            />
            <div 
              style={{ ...resizeHandleStyle, top: '-5px', left: '50%', marginLeft: '-5px', cursor: 'n-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 'n')}
            />
            <div 
              style={{ ...resizeHandleStyle, bottom: '-5px', left: '50%', marginLeft: '-5px', cursor: 's-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 's')}
            />
            <div 
              style={{ ...resizeHandleStyle, left: '-5px', top: '50%', marginTop: '-5px', cursor: 'w-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 'w')}
            />
            <div 
              style={{ ...resizeHandleStyle, right: '-5px', top: '50%', marginTop: '-5px', cursor: 'e-resize' }}
              onMouseDown={(e) => handleResizeStart(e, widget, 'e')}
            />
          </>
        )}
        {content}
      </div>
    );
  }, [selectedElement, isPreviewMode, gridSize, onWidgetSelect, isDraggingOrResizing, handleDragStart, handleResizeStart, topicValues, renderKey]);

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    if (isPreviewMode) return;
    
    if (e.target === e.currentTarget && selectedElement && !isDraggingOrResizing) {
      onWidgetSelect(null);
    }
  }, [isPreviewMode, selectedElement, isDraggingOrResizing, onWidgetSelect]);

  const sortedWidgets = useMemo(() => {
    return widgets
      .slice()
      .sort((a, b) => {
        if (selectedElement && a.id === selectedElement.id) return 1;
        if (selectedElement && b.id === selectedElement.id) return -1;
        return (a.zIndex || 0) - (b.zIndex || 0);
      });
  }, [widgets, selectedElement]);

  const visibleWidgets = useMemo(() => {
    if (!canvasRef.current) return sortedWidgets;

    const canvasRect = canvasRef.current.getBoundingClientRect();
    const buffer = 200;  
    
    const visibleLeft = -buffer;
    const visibleTop = -buffer;
    const visibleRight = canvasRect.width + buffer;
    const visibleBottom = canvasRect.height + buffer;
    
    return sortedWidgets.filter(widget => {
      const widgetRight = widget.x + widget.w * gridSize;
      const widgetBottom = widget.y + widget.h * gridSize;
      
      return (
        widget.x < visibleRight &&
        widgetRight > visibleLeft &&
        widget.y < visibleBottom &&
        widgetBottom > visibleTop
      );
    });
  }, [sortedWidgets, gridSize]);

  const canvasStyle = useMemo(() => ({
    width: isPreviewMode ? 
      Math.max(10000, canvasBounds.contentWidth + 2000) : '100%', // 预览模式下确保足够大的画布尺寸
    height: isPreviewMode ? 
      Math.max(10000, canvasBounds.contentHeight + 2000) : '100%',
    position: 'relative' as const,
    backgroundColor: showGrid ? '#f5f5f5' : '#ffffff',
    backgroundImage: showGrid ? 'linear-gradient(#e5e5e5 1px, transparent 1px), linear-gradient(90deg, #e5e5e5 1px, transparent 1px)' : 'none',
    backgroundSize: showGrid ? `${gridSize}px ${gridSize}px` : '0',
    overflow: 'visible', // 修改为visible，确保内容不被裁剪
    transform: `translate(${canvasDrag.offsetX}px, ${canvasDrag.offsetY}px)`,
    transition: canvasDrag.dragging ? 'none' : 'transform 0.1s ease-out'
  }), [showGrid, gridSize, canvasDrag.offsetX, canvasDrag.offsetY, canvasDrag.dragging, isPreviewMode, canvasBounds]);

  // 组件卸载时清理所有订阅
  useEffect(() => {
    return () => {
      if (subscribedTopics.current.size > 0) {
        batchUnsubscribe(Array.from(subscribedTopics.current));
        subscribedTopics.current.clear();
      }
    };
  }, [batchUnsubscribe]);

  return (
    <div 
      ref={canvasRef}
      style={{
        position: 'relative',
        width: '100%', 
        height: '100%',
        overflow: 'visible',
        transformOrigin: '0 0',
        transform: `translate(${canvasDrag.offsetX}px, ${canvasDrag.offsetY}px)`,
        backgroundColor: showGrid ? '#fcfcfc' : 'transparent'
      }}
      onMouseDown={isPreviewMode ? handleCanvasRightMouseDown : undefined}
      key="hmi-canvas"
    >
      {visibleWidgets.map(widget => {
        const widgetTopic = widget.properties?.topic;
        const topicValueTimestamp = widgetTopic ? 
          (topicValues[widgetTopic]?.timestamp || 0) : 0;
        
        return renderWidget({
          ...widget,
          _mqttUpdateTimestamp: topicValueTimestamp
        });
      })}
    </div>
  );
}); 