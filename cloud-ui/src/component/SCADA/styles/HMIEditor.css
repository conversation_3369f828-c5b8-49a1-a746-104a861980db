.react-grid-layout {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #fafafa;
}

.react-grid-item {
  transition: all 200ms ease;
  transition-property: left, top, width, height;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  transform: translateZ(0);
  will-change: transform, left, top, width, height;
}

.selected {
  border: 2px dashed #2196f3;
}

.react-grid-item:hover {
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23);
}

.react-grid-item.react-grid-placeholder {
  background-color: rgba(33, 150, 243, 0.2);
  border: 1px dashed #2196f3;
  border-radius: 4px;
  opacity: 0.8;
  z-index: 0;
}

.react-resizable-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  bottom: 0;
  right: 0;
  background-repeat: no-repeat;
  background-origin: content-box;
  box-sizing: border-box;
  cursor: se-resize;
  padding: 0 3px 3px 0;
}

.widget-panel-item {
  /* padding: 8px; */
  margin-bottom: 8px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.2s ease;
}

.widget-panel-item:hover {
  background-color: #f0f8ff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
}

.widget-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 4px;
}

.property-section {
  margin-bottom: 12px;
}

.property-title {
  font-weight: 600;
  margin-bottom: 4px;
}

/* 禁止所有图片默认拖拽 */
img {
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 禁止选择文本，避免拖拽时选中文本 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 防止拖动过程中选中文本 */
.no-text-select {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 添加性能优化样式 */
.hardware-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 为动画优化的元素添加硬件加速 */
.animated-widget {
  will-change: transform, opacity;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 提高所有Canvas组件的性能 */
canvas {
  transform: translateZ(0);
} 