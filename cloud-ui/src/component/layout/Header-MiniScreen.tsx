import GlobalStyles from '@mui/material/GlobalStyles';
import Paper from '@mui/material/Paper';
import IconButton from '@mui/material/IconButton';
import { toggleSidebar } from '../utils/utils';
import LanguageSwitch from '../theme/LanguageSwitch';
import ColorSchemeToggle from '../theme/ColorSchemeToggle';
import { Box, styled, alpha } from '@mui/material';
import { DragHandle } from '@mui/icons-material';

const StyledPaper = styled(Paper)(({ theme }) => ({
  backgroundImage: `linear-gradient(to right, ${alpha(theme.palette.background.paper, 0.8)}, ${theme.palette.background.paper})`,
  backdropFilter: 'blur(5px)',
  [theme.breakpoints.down('sm')]: {
    backdropFilter: 'none',
    backgroundColor: theme.palette.background.paper,
  },
  boxShadow: theme.shadows[3],
}));

const StyledIconButton = styled(IconButton)(({ theme }) => ({
  backgroundColor: alpha(theme.palette.primary.main, 0.1),
  borderRadius: '30%',
  '&:hover': {
    backgroundColor: alpha(theme.palette.primary.main, 0.2),
  }
}));

export default function Header() {
    return (
        <StyledPaper
            sx={{
                display: { xs: 'flex', md: 'none' },
                alignItems: 'center',
                justifyContent: 'space-between',
                position: 'fixed',
                top: 0,
                width: '100vw',
                height: 'var(--Header-height)',
                p: 2,
                borderBottom: '1px solid',
                borderColor: 'divider',
                zIndex: 1200,
            }}
        >
            <GlobalStyles
                styles={(theme) => ({
                    ':root': {
                        '--Header-height': '60px',
                        [theme.breakpoints.up('md')]: {
                            '--Header-height': '0px',
                        },
                    },
                })}
            />
            <StyledIconButton
                onClick={() => toggleSidebar()}
                color="primary"
                size="small"
            >
                <DragHandle />
            </StyledIconButton>
            {/* <Typography variant="h6">Beacon</Typography> */}
            <Box sx={{ 
              width: 120, 
              height: 40, 
              zIndex: 1200,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center'
            }}>
                <img src="/logo.png" alt="logo" style={{ 
                  width: '100%', 
                  height: '100%',
                  objectFit: 'contain'
                }} />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <LanguageSwitch />
                <ColorSchemeToggle />
            </Box>
        </StyledPaper>
    );
}