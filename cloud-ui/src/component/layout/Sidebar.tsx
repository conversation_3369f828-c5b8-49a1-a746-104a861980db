import * as React from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  GlobalStyles, Avatar, Box, Divider, IconButton, List, ListItem,
  ListItemButton, ListItemIcon, ListItemText, Typography, Paper,
  Collapse, Menu, MenuItem, Tooltip, alpha, styled,
  useTheme, Stack,
  Chip
} from '@mui/material';
import {
  LogoutRounded,
  KeyboardArrowDown, ChevronLeft, ChevronRight,
  QrCode2
} from '@mui/icons-material';
import ColorSchemeToggle from '../theme/ColorSchemeToggle';
import { closeSidebar } from '../utils/utils';
import { RouteConfig, useRoutes } from '../../config/routes';
import LanguageSwitch from '../theme/LanguageSwitch';
import { DialogContext } from '../../context/GlobalDialog';
import { useUser } from '../../context/UserContext';

// Styled components for Sidebar
const StyledListItemButton = styled(ListItemButton, {
  shouldForwardProp: (prop) => prop !== 'component'
})<{ component?: React.ElementType }>(({ theme }) => ({
  borderRadius: theme.shape.borderRadius,
  margin: '2px 0',
  color: theme.palette.text.primary,
  padding: '6px 12px',
  minHeight: 40,
  transition: theme.transitions.create(['all'], {
    duration: theme.transitions.duration.shorter,
  }),
  '&.Mui-selected': {
    background: `linear-gradient(90deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${theme.palette.background.paper} 100%)`,
    color: theme.palette.primary.main,
    '&:hover': {
      background: alpha(theme.palette.primary.main, 0.2),
    },
    '& .MuiListItemIcon-root': {
      color: theme.palette.primary.main,
    },
    '&::before': {
      content: '""',
      position: 'absolute',
      left: 0,
      width: 3,
      height: '60%',
      backgroundColor: theme.palette.primary.main,
      borderRadius: theme.shape.borderRadius
    }
  },
}));

const StyledListItemIcon = styled(ListItemIcon)(({ theme }) => ({
  minWidth: 36,
  color: theme.palette.text.secondary,
}));

const StyledPaper = styled(Paper)(({ theme }) => ({
  backgroundImage: `linear-gradient(to bottom, ${alpha(theme.palette.background.paper, 0.8)}, ${theme.palette.background.paper})`,
  backdropFilter: 'blur(4px)',
  boxShadow: theme.shadows[3],
  [theme.breakpoints.down('sm')]: {
    backdropFilter: 'none',
    backgroundColor: theme.palette.background.paper,
  },
}));

// WechatQRCode component
export function WechatQRCode() {
  const theme = useTheme();
  const { openDialog } = React.useContext(DialogContext)!;

  const handleClick = () => {
    openDialog(
      <Box
        sx={{
          maxWidth: '500px', 
        }}
      >
        <img 
          src="/qrcode.png" 
          alt="WeChat QR Code" 
          style={{ 
            maxWidth: '100%', 
            maxHeight: '100%',
            borderRadius: theme.shape.borderRadius
          }} 
        />
      </Box>,""
    );
  };

  return (
    <IconButton
      size="small"
      onClick={handleClick} 
    >
      <QrCode2 fontSize="small"/>
    </IconButton>
  );
}

function Sidebar() {
  const routes = useRoutes();
  const location = useLocation();
  const { userInfo, logout } = useUser();
  const { username, name, email, role, avatar } = userInfo;

  const [collapsed, setCollapsed] = React.useState(false);
  const [openMenus, setOpenMenus] = React.useState<{ [key: string]: boolean }>({});
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [activeMenu, setActiveMenu] = React.useState<string | null>(null);
  // 新增用户菜单状态
  const [userMenuAnchor, setUserMenuAnchor] = React.useState<null | HTMLElement>(null);

  // Consolidated effect for sidebar state management
  React.useEffect(() => {
    // Handle window resize
    const handleResize = () => {
      if (window.innerWidth >= 900) {
        setCollapsed(false);
        closeSidebar();
      }
    };

    // Handle sidebar toggle events
    const handleSidebarToggle = (event: Event) => {
      const customEvent = event as CustomEvent;
      if (window.innerWidth < 900 && customEvent.detail?.isOpen) {
        setCollapsed(false);
      }
    };

    // Check sidebar state based on CSS variable
    const checkSidebarState = () => {
      if (window.innerWidth < 900) {
        const slideIn = window
          .getComputedStyle(document.documentElement)
          .getPropertyValue('--SideNavigation-slideIn');
        
        if (slideIn && slideIn.trim() === '1') {
          setCollapsed(false);
        }
      }
    };

    // Set up event listeners
    window.addEventListener('resize', handleResize);
    document.addEventListener('sidebarToggled', handleSidebarToggle);
    
    // Set up MutationObserver for style changes
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'style') {
          checkSidebarState();
        }
      });
    });
    
    observer.observe(document.documentElement, { attributes: true });
    
    // Initial setup
    handleResize();
    checkSidebarState();
    
    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('sidebarToggled', handleSidebarToggle);
      observer.disconnect();
    };
  }, []);

  // 检查用户是否有权限访问该路由
  const hasAccess = (route: RouteConfig) => {
    if (!route.roles) return true;
    return route.roles.includes(role || '');
  };

  // 处理菜单点击
  const handleMenuClick = (menuId: string, event: React.MouseEvent<HTMLElement>) => {
    if (collapsed) {
      setAnchorEl(event.currentTarget);
      setActiveMenu(menuId);
    } else {
      setOpenMenus(prev => ({ ...prev, [menuId]: !prev[menuId] }));
    }
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setActiveMenu(null);
  };

  // 新增处理用户菜单的函数
  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setUserMenuAnchor(event.currentTarget);
  };

  const handleUserMenuClose = () => {
    setUserMenuAnchor(null);
  };

  // 渲染单个菜单项
  const renderMenuItem = (item: RouteConfig, isSubItem: boolean = false) => {
    if (!hasAccess(item) || item.showInMenu === false) return null;

    return (
      <ListItem key={item.id} disablePadding>
        <Tooltip title={collapsed ? item.label : ""} placement="right" arrow>
          <Link to={`/dashboard/${item.path}`} style={{ textDecoration: 'none', width: '100%' }}>
            <StyledListItemButton
              selected={location.pathname === `/dashboard/${item.path}`}
              sx={{
                pl: collapsed ? 2 : isSubItem ? 4 : 2,
                justifyContent: collapsed ? 'center' : 'flex-start',
                position: 'relative'
              }}
            >
              {item.icon && (
                <StyledListItemIcon sx={{ 
                  minWidth: collapsed ? 'auto' : 40,
                  mr: collapsed ? 0 : 2,
                  transition: 'transform 0.2s',
                  transform: location.pathname === `/dashboard/${item.path}` 
                    ? 'scale(1.1)' 
                    : 'none'
                }}>
                  {item.icon}
                </StyledListItemIcon>
              )}
              {!collapsed && (
                <ListItemText 
                  primary={item.label} 
                  primaryTypographyProps={{
                    fontWeight: location.pathname === `/dashboard/${item.path}` 
                      ? 600 
                      : 500,
                    variant: 'body2'
                  }}
                />
              )}
            </StyledListItemButton>
          </Link>
        </Tooltip>
      </ListItem>
    );
  };

  // 渲染菜单组
  const renderMenuGroup = (menu: RouteConfig) => {
    // Skip if no accessible items
    if (!menu.items?.some(item => hasAccess(item) && item.showInMenu !== false)) return null;

    // Filter accessible items once
    const accessibleItems = menu.items.filter(item => hasAccess(item) && item.showInMenu !== false);

    return (
      <React.Fragment key={menu.id}>
        {/* Menu group header */}
        <ListItem disablePadding>
          <Tooltip title={collapsed ? menu.label : ""} placement="right">
            <StyledListItemButton onClick={(e) => handleMenuClick(menu.id, e)}>
              {menu.icon && <StyledListItemIcon>{menu.icon}</StyledListItemIcon>}
              {!collapsed && (
                <>
                  <ListItemText primary={menu.label} />
                  <KeyboardArrowDown
                    sx={{
                      transform: openMenus[menu.id] ? 'rotate(180deg)' : 'none',
                      transition: '0.2s ease',
                    }}
                  />
                </>
              )}
            </StyledListItemButton>
          </Tooltip>
        </ListItem>

        {/* Menu items - either in collapse or popup menu */}
        {collapsed ? (
          <Menu
            anchorEl={anchorEl}
            open={activeMenu === menu.id}
            onClose={handleMenuClose}
            anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
            transformOrigin={{ vertical: 'top', horizontal: 'left' }}
          >
            {accessibleItems.map(item => (
              <MenuItem
                key={item.id}
                component={Link}
                to={`/dashboard/${item.path}`}
                onClick={handleMenuClose}
                sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
              >
                {item.icon}
                {item.label}
              </MenuItem>
            ))}
          </Menu>
        ) : (
          <Collapse in={openMenus[menu.id]} timeout="auto" unmountOnExit>
            <List component="div" disablePadding>
              {accessibleItems.map(item => renderMenuItem(item, true))}
            </List>
          </Collapse>
        )}
      </React.Fragment>
    );
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <StyledPaper
        className="Sidebar"
        sx={{
          position: { xs: 'fixed', md: 'sticky' },
          transform: {
            xs: 'translateX(calc(100% * (var(--SideNavigation-slideIn, 0) - 1)))',
            md: 'none',
          },
          transition: 'transform 0.4s, width 0.4s',
          zIndex: 1201,
          height: '100dvh',
          width: collapsed ? 'var(--Sidebar-width-collapsed)' : 'var(--Sidebar-width)',
          top: 0,
          p: 1.5,
          flexShrink: 0,
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
          borderRight: '1px solid',
          borderColor: 'divider',
        }}
      >
        <GlobalStyles
          styles={(theme) => ({
            ':root': {
              '--Sidebar-width': '230px',
              '--Sidebar-width-collapsed': '68px',
              [theme.breakpoints.up('lg')]: {
                '--Sidebar-width': '250px',
                '--Sidebar-width-collapsed': '68px',
              },
            },
          })}
        />
        
        {/* Sidebar overlay for mobile */}
        <Box
          className="Sidebar-overlay"
          sx={{
            position: 'fixed',
            top: 0,
            left: 0,
            width: '100vw',
            height: '100vh',
            opacity: 'var(--SideNavigation-slideIn)',
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            transition: 'opacity 0.4s',
            transform: {
              xs: 'translateX(calc(100% * (var(--SideNavigation-slideIn, 0) - 1) + var(--SideNavigation-slideIn, 0) * var(--Sidebar-width, 0px)))',
              lg: 'translateX(-100%)',
            },
          }}
          onClick={() => {
            closeSidebar();
            setAnchorEl(null);
            setActiveMenu(null);
          }}
        />
        
        {/* Logo */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', zIndex: 1200, py: 0.5 }}>
          <img src="/logo.png" alt="logo" style={{ width: '100%', height: '100%' }} />
        </Box>
        
        {/* Navigation menu */}
        <Box sx={{ minHeight: 0, overflow: 'hidden auto', flexGrow: 1, p: 0 }}>
          <List sx={{
            gap: 0.5,
            '--List-nestedInsetStart': collapsed ? '0px' : '24px',
            '--ListItem-radius': (theme) => theme.shape.borderRadius,
          }}>
            {routes.dashboard.map(route => 
              route.items ? renderMenuGroup(route) : renderMenuItem(route)
            )}
          </List>
        </Box>
        
        {/* Theme and language toggles */}
        <Stack 
          direction={collapsed ? 'column' : 'row'} 
          spacing={collapsed ? 0.5 : 2} 
          justifyContent="center"
          sx={{
            '& .MuiIconButton-root': {
              width: 32,
              height: 32,
              padding: 0.5
            }
          }}
        >
          <LanguageSwitch />
          <ColorSchemeToggle/>
          <WechatQRCode />
        </Stack>
        
        {/* User profile section - 简化并添加菜单弹出功能 */}
        <Divider sx={{ 
          opacity: 0.5,
          my: 0.5,
          background: (theme) => `linear-gradient(90deg, ${alpha(theme.palette.divider, 0)} 0%, ${theme.palette.divider} 50%, ${alpha(theme.palette.divider, 0)} 100%)`,
          height: 2
        }} />
        
        {/* 简化的用户资料区域 */}
        <Box 
          sx={{ 
            display: 'flex', 
            alignItems: 'center', 
            p: collapsed ? 0.5 : 1,
            borderRadius: 1,
            justifyContent: collapsed ? 'center' : 'flex-start',
            cursor: 'pointer',
            '&:hover': {
              bgcolor: (theme) => alpha(theme.palette.primary.main, 0.1),
            },
            transition: 'background-color 0.2s ease'
          }}
          onClick={handleUserMenuOpen}
        >
          <Avatar
            variant="circular"
            src={avatar}
            sx={{ 
              width: collapsed ? 32 : 36, 
              height: collapsed ? 32 : 36,
            }}
          />
          {!collapsed && (
            <Box sx={{ ml: 1.5, flex: 1, overflow: 'hidden' }}>
              <Typography variant="subtitle2" fontSize="0.85rem" noWrap>
                {name || username}
              </Typography>
            </Box>
          )}
          
          {!collapsed && (
            <KeyboardArrowDown
              fontSize="small"
              sx={{
                color: 'text.secondary',
                transition: '0.2s ease',
                transform: userMenuAnchor ? 'rotate(180deg)' : 'none',
              }}
            />
          )}
        </Box>
        
        {/* 用户菜单 */}
        <Menu
          anchorEl={userMenuAnchor}
          open={Boolean(userMenuAnchor)}
          onClose={handleUserMenuClose}
          anchorOrigin={{
            vertical: 'top',
            horizontal: collapsed ? 'right' : 'center',
          }}
          transformOrigin={{
            vertical: 'bottom',
            horizontal: collapsed ? 'left' : 'center',
          }}
          slotProps={{
            paper: {
              elevation: 3,
              sx: {
                minWidth: 200,
                overflow: 'visible',
                mt: 1.5,
                '&:before': {
                  content: '""',
                  display: 'block',
                  position: 'absolute',
                  top: 0,
                  left: collapsed ? 10 : '50%',
                  width: 10,
                  height: 10,
                  bgcolor: 'background.paper',
                  transform: collapsed ? 'translate(0, -50%) rotate(45deg)' : 'translate(-50%, -50%) rotate(45deg)',
                  zIndex: 0,
                }
              }
            }
          }}
        >
          <Box sx={{ px: 2, py: 1.5 }}>
            <Typography variant="subtitle1" sx={{ mb: 0.5 }}>
              {name || username}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
              {email}
            </Typography>
            <Chip label={role} color="primary" variant="outlined" />
          </Box>
          <Divider />
          <MenuItem 
            onClick={() => {
              logout();
              handleUserMenuClose();
            }}
            sx={{ 
              color: 'error.main',
              gap: 1,
              py: 1
            }}
          >
            <LogoutRounded fontSize="small" />
            Logout
          </MenuItem>
        </Menu>
      </StyledPaper>
      
      {/* 优化折叠切换按钮 */}
      <Box
        sx={{
          display: { xs: 'none', md: 'flex' },
          position: 'relative',
          zIndex: 1201,
        }}
      >
        <IconButton
          size="small"
          onClick={() => setCollapsed(!collapsed)}
          sx={{
            position: 'absolute',
            left: '-10px',
            top: '50%',
            transform: 'translateY(-50%)',
            width: '20px',
            height: '20px',
            borderRadius: '50%',
            backgroundColor: 'background.paper',
            border: '1px solid',
            borderColor: 'divider',
            boxShadow: 1,
            transition: 'all 0.2s ease-in-out'
          }}
        >
          {collapsed ? <ChevronRight fontSize="small" /> : <ChevronLeft fontSize="small" />}
        </IconButton>
      </Box>
    </Box>
  );
}

export default Sidebar;