import { <PERSON><PERSON>, <PERSON><PERSON>ield, <PERSON>po<PERSON>, <PERSON>, Stack, Box } from '@mui/material';
import ForgotPassword from './ForgotPassword';
import { DialogContext } from '../../context/GlobalDialog';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from '../../context/SnackbarContext';
import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useUser } from '../../context/UserContext';
import { login, saveToken, saveUserInfo } from '../../services/authService';

interface LoginFormProps {
  onSwitchToSignUp: () => void;
}

export default function LoginForm({ onSwitchToSignUp }: LoginFormProps) {
  const { showSnackbar } = useSnackbar();
  const navigate = useNavigate();
  const dialog = useContext(DialogContext);
  const { t } = useTranslation();
  const { updateUserInfo } = useUser();

  const handleForgotPasswordClick = (event: { preventDefault: () => void; }) => {
    event.preventDefault();
    dialog?.openDialog(<ForgotPassword />, t('auth.forgotPassword.title'));
  };

  return (
    <Stack spacing={4}>
      <Typography variant="h4" component="h1">
        {t('auth.signIn.title')}
      </Typography>
      <form
        onSubmit={async (event) => {
          event.preventDefault();
          const formData = new FormData(event.currentTarget);
          const data = {
            username: formData.get('email') as string,
            password: formData.get('password') as string,
          };
          try {
            const response = await login(data);
            
            // 保存令牌和用户信息
            saveToken(response.token);
            saveUserInfo(response);
            
            // 更新上下文中的用户信息
            updateUserInfo(response);
            
            navigate('/dashboard');
          } catch (error) {
            showSnackbar(t('auth.signIn.loginError', { error: (error as any).response.data.error }), 'error', 3000);
          }
        }}
      >
        <TextField 
          fullWidth 
          name="email" 
          label={t('auth.signIn.emailLabel')}
          variant="outlined" 
          required
        />
        <TextField
          type="password"
          name="password"
          label={t('auth.signIn.passwordLabel')}
          fullWidth
          variant="outlined"
        />
        <Stack spacing={2}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            {/* <FormControlLabel 
              control={
                <Checkbox 
                  name="persistent" 
                  size="small" 
                  defaultChecked={localStorage.getItem('beacon_persistent') === 'true'} 
                />
              } 
              label={t('auth.signIn.rememberMe')} 
            /> */}
            <Link
              variant="body2"
              onClick={handleForgotPasswordClick}
              sx={{ cursor: 'pointer' }}
            >
              {t('auth.signIn.forgotPassword')}
            </Link>
          </Box>
          <Button type="submit" fullWidth variant="contained">
            {t('auth.signIn.submitButton')}
          </Button>
        </Stack>
      </form>
      <Typography variant="body2">
        {t('auth.signIn.newUser')}{' '}
        <Link 
          component="button" 
          variant="body2" 
          onClick={onSwitchToSignUp} 
          sx={{ cursor: 'pointer' }}
        >
          {t('auth.signIn.signUpLink')}
        </Link>
      </Typography>
    </Stack>
  );
}