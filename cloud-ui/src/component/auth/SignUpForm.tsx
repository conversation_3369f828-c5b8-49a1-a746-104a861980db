import React, { useContext } from 'react';
import { Button, <PERSON>pography, Link, Stack, FormControl, CircularProgress, Checkbox, FormControlLabel, OutlinedInput, InputLabel } from '@mui/material';
import { useCountdown } from '../utils/utils';
import { validatePasswordComplexity } from '../utils/utils';
import { useSnackbar } from '../../context/SnackbarContext';
import { DialogContext } from '../../context/GlobalDialog';
import VerificationCodeInput from '../utils/VerificationCodeInput';
import { useTranslation } from 'react-i18next';
import { sendRegisterCode, register } from '../../services/authService';

interface SignUpFormProps {
  onSwitchToLogin: () => void;
}

export default function SignUpForm({ onSwitchToLogin }: SignUpFormProps) {
  const { countdown, setCountdown, setCountdownEndTime, startCountdown } = useCountdown();
  const { showSnackbar } = useSnackbar();
  const [isLoading, setLoading] = React.useState(false);
  const [isEnterprise, setIsEnterprise] = React.useState(false);
  const dialog = useContext(DialogContext);
  const { t } = useTranslation();
  if (!dialog) return null;
  const { openDialog } = dialog;

  const [formData, setFormData] = React.useState({
    username: '',
    name: '',
    email: '',
    password: '',
    confirmpassword: '',
    enterpriseCode: '',
  });

  const handleSendVerificationCode = async () => {
    if (countdown > 0) {
      showSnackbar(t('auth.signUp.validation.waitForCode', { seconds: countdown }), 'info', 2000);
      return;
    }
    if (formData.email === '') {
      showSnackbar(t('auth.signUp.validation.emailRequired'), 'info', 2000);
      return;
    }

    if (formData.username.length > 12) {
      showSnackbar(t('auth.signUp.validation.usernameTooLong'), 'info', 2000);
      return;
    }

    if (formData.password !== formData.confirmpassword) {
      showSnackbar(t('auth.signUp.validation.passwordMismatch'), 'info', 2000);
      return;
    }

    if (!validatePasswordComplexity(formData.password)) {
      showSnackbar(t('auth.signUp.validation.passwordComplexity'), 'info', 5000);
      return;
    }

    setLoading(true);
    try {
      const response = await sendRegisterCode(formData.email, formData.username);
      showSnackbar(response.message, 'success', 2000);

      const endTime = Date.now() + 60000;
      setCountdownEndTime(endTime);
      setCountdown(60);
      setLoading(false);
      startCountdown(endTime);

      openDialog(
        <VerificationCodeInput
          onSubmit={async (code) => {
            try {
              const registerData = {
                username: formData.username,
                name: formData.name,
                email: formData.email,
                password: formData.password,
              };
              
              const response = await register(
                registerData,
                code,
                isEnterprise ? formData.enterpriseCode : undefined
              );
              
              showSnackbar(response.message, 'success', 2000);
              dialog.closeDialog();
              setTimeout(() => {
                onSwitchToLogin();
              }, 2000);
            } catch (error) {
              showSnackbar(t('auth.signUp.messages.registrationError', { error: (error as any).response.data.error }), 'error', 3000);
            }
          }}
          title={t('auth.verifyEmail.enterCode')}
        />,
        t('auth.verifyEmail.title')
      );
    } catch (error) {
      setLoading(false);
      showSnackbar(t('auth.signUp.messages.sendCodeError', { error: (error as any).response.data.error }), 'error', 3000);
    }
  };

  return (
    <Stack spacing={4}>
      <Typography variant="h4" component="h1">
        {t('auth.signUp.title')}
      </Typography>
      <Stack spacing={2}>
        {(['username', 'name', 'password', 'confirmpassword', 'email'] as const).map((key, index) => (
          <FormControl key={index} required>
            <InputLabel htmlFor={key}>
              {t(`auth.signUp.fields.${key === 'confirmpassword' ? 'confirmPassword' : key}`)}
            </InputLabel>
            <OutlinedInput
              type={key.includes('password') ? 'password' : key.includes('email') ? 'email' : 'text'}
              name={key}
              value={formData[key]}
              autoComplete='off'
              onChange={(e) => {
                setFormData({
                  ...formData,
                  [e.target.name]: e.target.value,
                });
              }}
              inputProps={{
                maxLength: key === 'username' ? 12 : undefined,
              }}
              label={t(`auth.signUp.fields.${key === 'confirmpassword' ? 'confirmPassword' : key}`)}
            />
          </FormControl>
        ))}

        <FormControl>
          <FormControlLabel
            control={
              <Checkbox
                checked={isEnterprise}
                onChange={(e) => setIsEnterprise(e.target.checked)}
                name="isEnterprise"
              />
            }
            label={t('auth.signUp.enterpriseUser')}
          />
        </FormControl>

        {isEnterprise && (
          <FormControl required>
            <InputLabel>{t('auth.signUp.fields.enterpriseCode')}</InputLabel>
            <OutlinedInput
              type="text"
              name="enterpriseCode"
              autoComplete='off'
              value={formData.enterpriseCode}
              onChange={(e) => {
                setFormData({
                  ...formData,
                  enterpriseCode: e.target.value,
                });
              }}
              label={t('auth.signUp.fields.enterpriseCode')}
            />
          </FormControl>
        )}

        <Button
          variant="contained"
          onClick={handleSendVerificationCode}
          disabled={countdown > 0 || isLoading}
        >
          {isLoading ? (
            <CircularProgress size={20} color="inherit" />
          ) : countdown > 0 ? (
            t('auth.signUp.resendCode', { seconds: countdown })
          ) : (
            t('auth.signUp.sendCode')
          )}
        </Button>
      </Stack>

      <Typography variant="body2">
        {t('auth.signUp.alreadyHaveAccount')}{' '}
        <Link component="button" variant="body2" onClick={onSwitchToLogin} sx={{ cursor: 'pointer' }}>
          {t('auth.signUp.signInLink')}
        </Link>
      </Typography>
    </Stack>
  );
} 