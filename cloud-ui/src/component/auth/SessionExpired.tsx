import { Typo<PERSON>, But<PERSON>, Container, Card, CardContent, CardMedia } from '@mui/material';
import { Link as RouterLink } from 'react-router-dom'; // 如果你使用 React Router
import { useTranslation } from 'react-i18next';
import { clearAuth } from '../../services/authService';
import { useEffect } from 'react';

const SessionExpiredPage = () => {
    const { t } = useTranslation();

    // 页面加载时清除认证信息
    useEffect(() => {
        clearAuth();
    }, []);

    return (
        <Container
            maxWidth="sm"
            sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                height: '60vh',
                justifyContent: 'center',
                textAlign: 'center',
            }}
        >
            <Card sx={{ p: 3, textAlign: 'center' }}>
                <CardMedia
                    sx={{ height: 160 }}
                    image="logo.png"
                    title="Beacon Cloud"
                />
                <CardContent>
                    {/* 标题 */}
                    <Typography variant="h4" component="h3" sx={{ mb: 2 }}>
                        {t('auth.sessionExpired.title')}
                    </Typography>

                    {/* 提示信息 */}
                    <Typography variant="body1" sx={{ mb: 2 }}>
                        {t('auth.sessionExpired.message')}
                    </Typography>
                    <Button
                        component={RouterLink}
                        to="/login"
                        variant="contained"
                    >
                        {t('auth.sessionExpired.loginButton')}
                    </Button>
                </CardContent>
            </Card>
        </Container>
    );
};

export default SessionExpiredPage;