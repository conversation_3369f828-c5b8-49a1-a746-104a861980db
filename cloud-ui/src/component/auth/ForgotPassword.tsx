import React, { useState, useContext } from 'react';
import { <PERSON>ton, TextField, Box, CircularProgress, InputAdornment } from '@mui/material';
import MailLockOutlined from '@mui/icons-material/MailLockOutlined';
import { useCountdown } from '../utils/utils';
import { useSnackbar } from '../../context/SnackbarContext';
import { DialogContext } from '../../context/GlobalDialog';
import VerificationCodeInput from '../utils/VerificationCodeInput';
import { useTranslation } from 'react-i18next';
import { sendForgotPasswordCode, resetPassword } from '../../services/authService';

export default function ForgotPassword() {
  const [email, setEmail] = useState('');
  const { countdown, setCountdown, setCountdownEndTime, startCountdown } = useCountdown();
  const { showSnackbar } = useSnackbar();
  const [isLoading, setLoading] = React.useState(false);
  const dialog = useContext(DialogContext);
  const { t } = useTranslation();
  if (!dialog) return null;
  const { openDialog } = dialog;

  // 发送验证码到邮箱
  const handleSendVerificationCode = async () => {
    if (countdown > 0) {
      showSnackbar(t('auth.forgotPassword.validation.waitForCode', { seconds: countdown }), 'info', 2000);
      return;
    }
    if (email === '') {
      showSnackbar(t('auth.forgotPassword.validation.emailRequired'), 'info', 2000);
      return;
    }

    setLoading(true);
    try {
      const response = await sendForgotPasswordCode(email);
      showSnackbar(response.message, 'success', 2000);

      const endTime = Date.now() + 60000;
      setCountdownEndTime(endTime);
      setCountdown(60);
      setLoading(false);
      startCountdown(endTime);
      
      // Open verification dialog
      openDialog(
        <VerificationCodeInput
          onSubmit={async (code) => {
            try {
              const response = await resetPassword(email, code);
              showSnackbar(response.message, 'success', 2000);
              dialog.closeDialog();
            } catch (error) {
              showSnackbar(t('auth.forgotPassword.messages.resetError', { error: (error as any).response.data.error }), 'error', 3000);
            }
          }}
          title={t('auth.verifyEmail.enterCode')}
        />,
        t('auth.verifyEmail.title')
      );
    } catch (error) {
      setLoading(false);
      showSnackbar(t('auth.forgotPassword.messages.sendCodeError', { error: (error as any).response.data.error }), 'error', 3000);
    }
  };

  return (
    <Box sx={{ width: '100%', p: 2 }}>
      {/* 邮箱输入框 */}
      <TextField
        fullWidth
        variant="outlined"
        name="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder={t('auth.forgotPassword.emailPlaceholder')}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <MailLockOutlined />
            </InputAdornment>
          ),
        }}
      />

      {/* 发送验证码按钮 */}
      <Button
        fullWidth
        variant="contained"
        onClick={handleSendVerificationCode}
        disabled={countdown > 0 || isLoading}
        sx={{ mt: 2 }}
      >
        {isLoading ? (
          <CircularProgress size={20} color="inherit" />
        ) : countdown > 0 ? (
          t('auth.forgotPassword.resendCode', { seconds: countdown })
        ) : (
          t('auth.forgotPassword.sendCode')
        )}
      </Button>
    </Box>
  );
}