{"name": "cloud-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@fontsource/inter": "^5.1.1", "@mui/icons-material": "^6.3.1", "@mui/joy": "^5.0.0-beta.51", "@mui/lab": "^6.0.0-beta.23", "@mui/material": "^6.3.1", "@mui/x-charts": "^7.24.0", "@mui/x-data-grid-pro": "^7.27.0", "@mui/x-date-pickers": "^7.27.0", "@mui/x-date-pickers-pro": "^7.27.0", "@mui/x-tree-view": "^7.26.0", "@svgdotjs/svg.js": "^3.2.4", "@toolpad/core": "^0.12.0", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/date-fns": "^2.5.3", "@types/lodash": "^4.17.16", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "browser-image-compression": "^2.0.2", "country-flag-icons": "^1.5.18", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "echarts": "^5.6.0", "echarts-for-react": "^3.0.2", "i18next": "^24.2.2", "i18next-browser-languagedetector": "^8.0.4", "jointjs": "^3.7.7", "lodash": "^4.17.21", "mui-color-input": "^5.0.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-draggable": "^4.4.6", "react-grid-layout": "^1.5.1", "react-i18next": "^15.4.1", "react-router-dom": "^7.1.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/node": "^22.10.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-grid-layout": "^1.3.5", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "sass-embedded": "^1.88.0", "typescript": "~5.6.2", "typescript-eslint": "^8.18.2", "vite": "^6.0.5"}}