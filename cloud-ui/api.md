
## 前端调用说明

### 1. 获取Peer AllowedIPs

**API路径**: `/api/wg/peer/allowed-ips`

**HTTP方法**: `GET`

**请求参数**:
- `device_name` (query string): WireGuard设备名称
- `peer_id` (query string): Peer的ID (MongoDB ObjectID格式)

**请求示例**:
```javascript
// 使用Axios进行API调用
async function getPeerAllowedIPs(deviceName, peerId) {
  try {
    const response = await axios.get('/api/wg/peer/allowed-ips', {
      params: {
        device_name: deviceName,
        peer_id: peerId
      },
      headers: {
        'Authorization': 'Bearer ' + yourAuthToken
      }
    });
    
    return response.data.allowed_ips; // 返回允许的IP数组
  } catch (error) {
    console.error('获取AllowedIPs失败:', error);
    throw error;
  }
}
```

**响应示例**:
```json
{
  "allowed_ips": ["10.0.0.0/24", "***********/24"]
}
```

### 2. 保存Peer AllowedIPs

**API路径**: `/api/wg/peer/allowed-ips`

**HTTP方法**: `POST`

**请求体**:
```json
{
  "device_name": "wg0",
  "enterprise_id": "company123",
  "peer_id": "60a1e2c63f6e9a7b4c8d9e0f",
  "allowed_ips": ["10.0.0.0/24", "***********/24", "**********/16"]
}
```

**请求示例**:
```javascript
// 使用Axios进行API调用
async function savePeerAllowedIPs(deviceName, enterpriseId, peerId, allowedIps) {
  try {
    const response = await axios.post('/api/wg/peer/allowed-ips', {
      device_name: deviceName,
      enterprise_id: enterpriseId,
      peer_id: peerId,
      allowed_ips: allowedIps
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + yourAuthToken
      }
    });
    
    return response.data; // 返回成功消息
  } catch (error) {
    console.error('保存AllowedIPs失败:', error);
    throw error;
  }
}
```

**响应示例**:
```json
{
  "message": "Peer allowed IPs updated successfully"
}
```