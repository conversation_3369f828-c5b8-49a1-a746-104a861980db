version: '3.8'

services:
  # 数据库服务
  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: beacon
      MONGO_INITDB_ROOT_PASSWORD: beacON028
    volumes:
      - ./data/mongodb_data:/data/db
    restart: always
    networks:
      - cloud_network

  tdengine:
    image: tdengine/tdengine
    container_name: tdengine
    ports:
      - "6030:6030" # taosd
      - "6041:6041" # taosAdapter
    volumes:
      - ./data/taos/dnode/data:/var/lib/taos
      - ./data/taos/dnode/log:/var/log/taos
    restart: unless-stopped
    networks:
      - cloud_network
    
  redis:
    image: redis:latest
    container_name: redis
    volumes:
      - ./data/redis/data:/data
      - ./data/redis/logs:/logs
      - ./data/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    command: redis-server /usr/local/etc/redis/redis.conf
    restart: unless-stopped
    networks:
      - cloud_network

  # 后端服务
  app:
    image: ztbcs/cloud:server
    container_name: ztbcs-cloud-server
    restart: unless-stopped
    networks:
      - cloud_network
    ports:
      - "8080:8080"  # 如果需要直接访问后端API
      - "1883:1883"  # MQTT端口
      - "50000-59999:50000-59999"  # wireguard
    volumes:
      - ./server/config:/app/config  # 挂载本地 config 目录到容器内的 /app/config
      - /etc/wireguard:/etc/wireguard
      # - /var/run/docker.sock:/var/run/docker.sock  # 挂载 Docker API 的 Unix socket（如果需要）
    environment:
      - TZ=Asia/Shanghai  # 设置时区
    user: "root"  # 使用 root 用户运行
    cap_add:
      - NET_ADMIN  # 添加网络管理权限
    depends_on:
      - mongodb
      - tdengine
      - redis

  # 前端服务
  ui:
    image: ztbcs/cloud:ui
    container_name: ztbcs-cloud-ui
    restart: unless-stopped
    networks:
      - cloud_network
    environment:
      - API_HOST=app  # 这里指定后端服务名称，对应nginx中的代理目标
    ports:
      - "5000:5000"
    depends_on:
      - app

# 定义网络，使所有服务能够相互通信
networks:
  cloud_network:
    driver: bridge
